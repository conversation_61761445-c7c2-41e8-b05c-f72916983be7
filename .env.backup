# 🔑 钱包配置
# Solana钱包私钥（base58格式）- 真实交易时必需
PRIVATE_KEY=2xb4HCtYqMUEwZgDEHaE8Fb3JzmKd33BneJVaNL2MWM4H1uq1FW1bPTjShCvd5rRwjFmyUWLbyb8yWoiA7jXpVZQ
MAX_MONITORED_TOKENS=30
# Solana RPC节点地址
SOLANA_RPC_ENDPOINT=https://solana-rpc.publicnode.com

featureWindowSize=10
FEATURE_WINDOW_SIZE=10
# 📊 监控配置
# 要监控的Token地址，多个地址用逗号分隔
# TARGET_TOKEN_ADDRESSES=9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump,另一个token地址
# 是否使用Jito (true/false，默认false)
USE_JITO=false

# Jito端点 (默认使用Frankfurt节点)
JITO_ENDPOINT=https://frankfurt.mainnet.block-engine.jito.wtf
TRADE_AMOUNT_SOL=0.0001
BUY_SLIPPAGE=0.5
SELL_SLIPPAGE=0.5
BUY_SLIPPAGE_PERCENT=15
SELL_SLIPPAGE_PERCENT=50
SLIPPAGE_TOLERANCE=0.5
# 钱包追踪配置
TARGET_WALLET=8J5GUAf7hr3LTPHJSkwrKFDNJPtAXtLHhnNHq6XxTLrW
WINDOW_HOURS=1
UPDATE_INTERVAL_MS=60000

# 是否强制使用所有地址（忽略格式验证）
FORCE_USE_ALL_ADDRESSES=false

# 🌐 GRPC配置
# Yellowstone GRPC端点
ENDPOINT=https://solana-yellowstone-grpc.publicnode.com:443
GRPC_ENDPOINT=https://solana-yellowstone-grpc.publicnode.com:443

# GRPC访问token（如果需要，某些端点不需要token）
X_TOKEN=
GRPC_TOKEN=

# 📝 交易模式
# 设为true启用纸上交易（安全测试）
PAPER_TRADING=true
TELEGRAM_CHAT_ID=5281826763
# 测试模式（生成模拟交易数据）
TEST_MODE=true

# 💰 交易配置
TRADE_AMOUNT_SOL=0.05
INITIAL_CAPITAL_SOL=2.0
MAX_POSITIONS=20

# 📊 滑点和交易费用配置
SLIPPAGE_TOLERANCE=0.20           # 3% 滑点容忍度
MAX_SLIPPAGE=0.20                 # 5% 最大滑点
ESTIMATED_GAS_FEE=0.000001        # 预估Gas费用 (SOL)
PRICE_IMPACT_LIMIT=0.20           # 2% 价格冲击限制
RETRY_ATTEMPTS=3                  # 交易重试次数
RETRY_DELAY=100                  # 重试延迟(ms)

# 🛡️ 风险控制
STOP_LOSS_PERCENTAGE=0.5         # 8% 止损
TAKE_PROFIT_PERCENTAGE=6          # 15% 止盈
MAX_LOSS_PER_TRADE_SOL=0.5       # 单笔最大亏损
MAX_DAILY_LOSS_SOL=0.5            # 日内最大亏损
MAX_TOTAL_LOSS_SOL=1.0            # 累计最大亏损
MAX_DRAWDOWN_PERCENTAGE=0.5      # 25% 最大回撤
MAX_POSITION_SIZE_PERCENTAGE=100 # 15% 最大仓位占比
COOLDOWN_PERIOD_MINUTES=0        # 10分钟冷却期
CONSECUTIVE_LOSS_LIMIT=8          # 连续亏损限制

# 🤖 AI模型配置
BUY_THRESHOLD=0.5                 # 60% 买入阈值
SELL_THRESHOLD=0.55     # 65% 卖出阈值

# 📊 监控和日志配置
LOG_LEVEL=info                    # debug, info, warn, error
ENABLE_DETAILED_LOGS=true         # 是否启用详细日志
STATS_UPDATE_INTERVAL_SECONDS=15  # 统计更新间隔（秒）
STATUS_UPDATE_INTERVAL_MS=120000  # 状态更新间隔（毫秒），2分钟

# 🧪 调试配置
DEBUG_MODE=false                  # 调试模式
VERBOSE_LOGGING=false             # 详细日志输出 