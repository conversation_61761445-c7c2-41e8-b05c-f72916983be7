import { TradingConfig } from '../types/trading-interfaces';

export class EnhancedConfigValidator {
  public static validate(config: Partial<TradingConfig>): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 必填字段检查
    const requiredFields: (keyof TradingConfig)[] = [
      'tradeAmountSol', 'initialCapitalSol', 'maxPositions', 'paperTrading',
      'slippageTolerance', 'maxDailyLossSol', 'stopLossPercentage', 
      'consecutiveLossLimit', 'buyThreshold', 'sellThreshold'
    ];

    for (const field of requiredFields) {
      if (config[field] === undefined || config[field] === null) {
        errors.push(`缺少必填字段: ${field}`);
      }
    }

    // 交易金额验证
    if (config.tradeAmountSol !== undefined) {
      if (config.tradeAmountSol <= 0) {
        errors.push('交易金额必须大于0');
      } else if (config.tradeAmountSol < 0.01) {
        warnings.push('交易金额过小，可能导致交易费用过高');
      } else if (config.tradeAmountSol > 10) {
        warnings.push('交易金额较大，请确认风险承受能力');
      }
    }

    // 初始资金验证
    if (config.initialCapitalSol !== undefined) {
      if (config.initialCapitalSol <= 0) {
        errors.push('初始资金必须大于0');
      }
      
      if (config.tradeAmountSol && config.initialCapitalSol) {
        const positionSizeRatio = config.tradeAmountSol / config.initialCapitalSol;
        if (positionSizeRatio > 0.2) {
          warnings.push('单笔交易金额占总资金比例过高（>20%），建议降低');
        }
      }
    }

    // 最大仓位数验证
    if (config.maxPositions !== undefined) {
      if (config.maxPositions <= 0 || !Number.isInteger(config.maxPositions)) {
        errors.push('最大仓位数必须为正整数');
      } else if (config.maxPositions > 20) {
        warnings.push('最大仓位数过多，可能难以有效管理');
      }
    }

    // 滑点容忍度验证
    if (config.slippageTolerance !== undefined) {
      if (config.slippageTolerance < 0 || config.slippageTolerance > 1) {
        errors.push('滑点容忍度必须在0-1之间');
      } else if (config.slippageTolerance < 0.01) {
        warnings.push('滑点容忍度过低，可能导致交易失败');
      } else if (config.slippageTolerance > 0.1) {
        warnings.push('滑点容忍度过高，可能导致价格偏差过大');
      }
    }

    // 日内最大损失验证
    if (config.maxDailyLossSol !== undefined) {
      if (config.maxDailyLossSol <= 0) {
        errors.push('日内最大损失必须大于0');
      }
      
      if (config.initialCapitalSol && config.maxDailyLossSol) {
        const dailyLossRatio = config.maxDailyLossSol / config.initialCapitalSol;
        if (dailyLossRatio > 0.3) {
          warnings.push('日内最大损失占总资金比例过高（>30%）');
        }
      }
    }

    // 止损百分比验证
    if (config.stopLossPercentage !== undefined) {
      if (config.stopLossPercentage <= 0 || config.stopLossPercentage > 100) {
        errors.push('止损百分比必须在0-100之间');
      } else if (config.stopLossPercentage < 5) {
        warnings.push('止损百分比过低，可能导致频繁止损');
      } else if (config.stopLossPercentage > 50) {
        warnings.push('止损百分比过高，风险控制效果有限');
      }
    }

    // 连续亏损限制验证
    if (config.consecutiveLossLimit !== undefined) {
      if (config.consecutiveLossLimit <= 0 || !Number.isInteger(config.consecutiveLossLimit)) {
        errors.push('连续亏损限制必须为正整数');
      } else if (config.consecutiveLossLimit > 10) {
        warnings.push('连续亏损限制过高，建议设置更严格的风控');
      }
    }

    // AI模型阈值验证
    if (config.buyThreshold !== undefined) {
      if (config.buyThreshold < 0 || config.buyThreshold > 1) {
        errors.push('买入阈值必须在0-1之间');
      }
    }

    if (config.sellThreshold !== undefined) {
      if (config.sellThreshold < 0 || config.sellThreshold > 1) {
        errors.push('卖出阈值必须在0-1之间');
      }
    }

    // 阈值逻辑验证
    if (config.buyThreshold !== undefined && config.sellThreshold !== undefined) {
      if (config.buyThreshold <= config.sellThreshold) {
        warnings.push('买入阈值应该高于卖出阈值，以避免频繁交易');
      }
    }

    // 统计更新间隔验证
    if (config.statsUpdateIntervalSeconds !== undefined) {
      if (config.statsUpdateIntervalSeconds <= 0) {
        errors.push('统计更新间隔必须大于0');
      } else if (config.statsUpdateIntervalSeconds < 10) {
        warnings.push('统计更新间隔过短，可能影响性能');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  public static showConfigSummary(config: TradingConfig): void {
    console.log('\n📋 交易配置概览:');
    console.log('=====================================');
    
    // 基础配置
    console.log('🔧 基础配置:');
    console.log(`   💰 交易金额: ${config.tradeAmountSol} SOL`);
    console.log(`   💵 初始资金: ${config.initialCapitalSol} SOL`);
    console.log(`   📊 最大仓位: ${config.maxPositions}个`);
    console.log(`   📝 交易模式: ${config.paperTrading ? '📄 模拟交易' : '💰 实盘交易'}`);
    console.log(`   📈 滑点容忍: ${(config.slippageTolerance * 100).toFixed(2)}%`);
    
    // 风险管理
    console.log('\n⚠️  风险管理:');
    console.log(`   🚫 日内最大损失: ${config.maxDailyLossSol} SOL`);
    console.log(`   🛑 止损百分比: ${config.stopLossPercentage}%`);
    console.log(`   🔄 连续亏损限制: ${config.consecutiveLossLimit}次`);
    
    // AI模型配置
    console.log('\n🤖 AI模型配置:');
    console.log(`   📈 买入阈值: ${config.buyThreshold}`);
    console.log(`   📉 卖出阈值: ${config.sellThreshold}`);
    
    // 监控配置
    console.log('\n📊 监控配置:');
    console.log(`   📝 日志级别: ${config.logLevel}`);
    console.log(`   🔍 详细日志: ${config.enableDetailedLogs ? '✅' : '❌'}`);
    console.log(`   ⏱️  统计更新间隔: ${config.statsUpdateIntervalSeconds}秒`);
    
    // 风险评估
    const riskLevel = this.assessRiskLevel(config);
    console.log(`\n🎯 风险等级: ${this.getRiskLevelDisplay(riskLevel)}`);
    
    console.log('=====================================\n');
  }

  public static assessRiskLevel(config: TradingConfig): 'conservative' | 'moderate' | 'aggressive' | 'high_risk' {
    let riskScore = 0;
    
    // 交易金额风险
    const tradeRatio = config.tradeAmountSol / config.initialCapitalSol;
    if (tradeRatio > 0.15) riskScore += 2;
    else if (tradeRatio > 0.1) riskScore += 1;
    
    // 止损设置风险
    if (config.stopLossPercentage > 30) riskScore += 2;
    else if (config.stopLossPercentage > 20) riskScore += 1;
    
    // 日内损失风险
    const dailyLossRatio = config.maxDailyLossSol / config.initialCapitalSol;
    if (dailyLossRatio > 0.2) riskScore += 2;
    else if (dailyLossRatio > 0.1) riskScore += 1;
    
    // 仓位数量风险
    if (config.maxPositions > 10) riskScore += 1;
    if (config.maxPositions > 15) riskScore += 1;
    
    // 连续亏损容忍度
    if (config.consecutiveLossLimit > 5) riskScore += 1;
    
    // AI阈值风险
    const thresholdDiff = config.buyThreshold - config.sellThreshold;
    if (thresholdDiff < 0.1) riskScore += 1; // 阈值差异过小可能导致频繁交易
    
    if (riskScore >= 6) return 'high_risk';
    if (riskScore >= 4) return 'aggressive';
    if (riskScore >= 2) return 'moderate';
    return 'conservative';
  }

  private static getRiskLevelDisplay(level: 'conservative' | 'moderate' | 'aggressive' | 'high_risk'): string {
    switch (level) {
      case 'conservative': return '🟢 保守型';
      case 'moderate': return '🟡 稳健型';
      case 'aggressive': return '🟠 积极型';
      case 'high_risk': return '🔴 高风险型';
    }
  }

  public static validateEnvironment(): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查环境变量
    const requiredEnvVars = [
      'TELEGRAM_BOT_TOKEN',
      'TELEGRAM_CHAT_ID',
      'HELIUS_API_KEY',
      'JITO_ENDPOINTS',
      'TARGET_WALLET'
    ];

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        errors.push(`缺少环境变量: ${envVar}`);
      }
    }

    // 检查可选但推荐的环境变量
    const recommendedEnvVars = [
      'SOLANA_RPC_URL',
      'GRPC_ENDPOINT'
    ];

    for (const envVar of recommendedEnvVars) {
      if (!process.env[envVar]) {
        warnings.push(`建议设置环境变量: ${envVar}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  public static createDefaultConfig(): TradingConfig {
    return {
      tradeAmountSol: 0.1,
      initialCapitalSol: 5.0,
      maxPositions: 5,
      paperTrading: true,
      slippageTolerance: 0.05,
      maxDailyLossSol: 1.0,
      stopLossPercentage: 15,
      consecutiveLossLimit: 3,
      buyThreshold: 0.7,
      sellThreshold: 0.3,
      logLevel: 'info',
      enableDetailedLogs: true,
      statsUpdateIntervalSeconds: 30
    };
  }
} 