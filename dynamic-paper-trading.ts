import "dotenv/config";
import Client, {
  CommitmentLevel,
  SubscribeRequestFilterTransactions,
} from "@triton-one/yellowstone-grpc";
import { PublicKey, VersionedTransactionResponse } from "@solana/web3.js";
import { Idl } from "@coral-xyz/anchor";
import { SolanaParser } from "@shyft-to/solana-transaction-parser";
import { TransactionFormatter } from "./utils/transaction-formatter";
import { SolanaEventParser } from "./utils/event-parser";
import { bnLayoutFormatter } from "./utils/bn-layout-formatter";
import pumpFunAmmIdl from "./idls/pump_amm_0.1.0.json";
import { BuyPredictor } from "./predictors/buy-predictor";
import { SellPredictor } from "./predictors/sell-predictor";
import { PumpSwapManager } from "./trading/pump-swap-manager";
import { validateConfig, TradingSystemConfig, PartialTradingSystemConfig } from "./trading-config";

// 配置 - 从环境变量读取
const TARGET_WALLET = process.env.TARGET_WALLET || '8J5GUAf7hr3LTPHJSkwrKFDNJPtAXtLHhnNHq6XxTLrW';
const GRPC_ENDPOINT = process.env.ENDPOINT || 'https://solana-yellowstone-grpc.publicnode.com:443';
const GRPC_TOKEN = process.env.X_TOKEN;
const WINDOW_HOURS = parseFloat(process.env.WINDOW_HOURS || '1');
const UPDATE_INTERVAL_MS = parseInt(process.env.UPDATE_INTERVAL_MS || '60000');

console.log('🚀 Dynamic Paper Trading System Starting...');
console.log(`🎯 目标钱包: ${TARGET_WALLET}`);
console.log(`📡 GRPC端点: ${GRPC_ENDPOINT}`);
console.log('');

// Paper Trading 配置
const PAPER_TRADING_CONFIG: PartialTradingSystemConfig = {
  trading: {
    tradeAmountSol: parseFloat(process.env.TRADE_AMOUNT_SOL || '0.05'),
    initialCapitalSol: parseFloat(process.env.INITIAL_CAPITAL_SOL || '2.0'),
    maxPositions: parseInt(process.env.MAX_POSITIONS || '3'),
    paperTrading: true,
    slippageTolerance: parseFloat(process.env.SLIPPAGE_TOLERANCE || '0.03')
  },
  risk: {
    stopLossPercentage: parseFloat(process.env.STOP_LOSS_PERCENTAGE || '0.08'),
    takeProfitPercentage: parseFloat(process.env.TAKE_PROFIT_PERCENTAGE || '0.15'),
    maxDailyLossSol: parseFloat(process.env.MAX_DAILY_LOSS_SOL || '0.5'),
    maxTotalLossSol: parseFloat(process.env.MAX_TOTAL_LOSS_SOL || '1.0'),
  },
  models: {
    buyThreshold: parseFloat(process.env.BUY_THRESHOLD || '0.6'),
    sellThreshold: parseFloat(process.env.SELL_THRESHOLD || '0.65'),
  },
  grpc: {
    endpoint: GRPC_ENDPOINT,
    token: GRPC_TOKEN
  }
};

const AI_CONFIG = {
  predictionInterval: parseInt(process.env.PREDICTION_INTERVAL || '30000'),
  featureWindowSize: parseInt(process.env.FEATURE_WINDOW_SIZE || '20'),
};

// 简化的动态Paper Trading管理器
class SimpleDynamicTrading {
  private grpcClient: Client | null = null;
  private currentStream: any = null;
  private isRunning: boolean = false;
  private currentSubscription: Set<string> = new Set([TARGET_WALLET]);
  private stats = { totalTransactions: 0, pumpFunTransactions: 0, uniqueTokens: 0 };

  public async start(): Promise<void> {
    console.log('🔄 启动动态Paper Trading管理器...');
    this.isRunning = true;
    
    this.grpcClient = new Client(GRPC_ENDPOINT, GRPC_TOKEN, undefined);
    console.log('✅ GRPC客户端创建成功');
    
    await this.handleStream();
  }

  private async handleStream(): Promise<void> {
    console.log('🔗 建立GRPC流连接...');
    const stream = await this.grpcClient!.subscribe();
    console.log('✅ GRPC流连接已建立');
    
    stream.on("data", (data) => {
      this.stats.totalTransactions++;
      if (this.stats.totalTransactions % 10 === 0) {
        console.log(`📊 已处理 ${this.stats.totalTransactions} 笔交易`);
      }
    });

    const subscribeRequest = {
      accounts: {},
      slots: {},
      transactions: {
        wallet_transactions: {
          vote: false,
          failed: false,
          accountInclude: [TARGET_WALLET],
          accountExclude: [],
          accountRequired: [],
        },
      },
      transactionsStatus: {},
      entry: {},
      blocks: {},
      blocksMeta: {},
      accountsDataSlice: [],
      ping: undefined,
      commitment: CommitmentLevel.CONFIRMED,
    };

    console.log('📤 发送订阅请求...');
    stream.write(subscribeRequest, (err: any) => {
      if (err === null || err === undefined) {
        console.log('✅ 订阅成功，开始监听...');
      } else {
        console.error('❌ 订阅失败:', err);
      }
    });

    // 保持连接
    await new Promise(() => {});
  }

  public async stop(): Promise<void> {
    this.isRunning = false;
    if (this.currentStream) {
      this.currentStream.end();
    }
    console.log('🛑 已停止');
  }
}

// 主函数
async function main() {
  const manager = new SimpleDynamicTrading();
  
  process.on('SIGINT', async () => {
    console.log('\n收到停止信号...');
    await manager.stop();
    process.exit(0);
  });
  
  try {
    await manager.start();
  } catch (error) {
    console.error('❌ 启动失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
} 