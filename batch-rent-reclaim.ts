import { 
    Connection, 
    PublicKey, 
    Keypair, 
    TransactionInstruction, 
    VersionedTransaction, 
    TransactionMessage,
    ComputeBudgetProgram,
    SystemProgram,
    LAMPORTS_PER_SOL
} from "@solana/web3.js";
import { 
    TOKEN_PROGRAM_ID,
    createCloseAccountInstruction
} from "@solana/spl-token";
import * as dotenv from "dotenv";
import bs58 from "bs58";
import axios from "axios";

dotenv.config();

// Jito configuration
const JITO_BLOCK_ENGINE_URL = "https://ny.mainnet.block-engine.jito.wtf";
const JITO_TIP_ACCOUNTS = [
    "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
    "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe", 
    "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY",
    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
    "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
    "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
    "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
    "********************************************"
];

const TIP_AMOUNT = 0.00001; // 0.00001 SOL for tip

// Load wallet
const privateKey = process.env.PRIVATE_KEY;
if (!privateKey) {
    throw new Error("PRIVATE_KEY not found in environment variables");
}

const wallet = Keypair.fromSecretKey(bs58.decode(privateKey));
const connection = new Connection("https://solana-rpc.publicnode.com");

// Bundle class for Jito
class Bundle {
    private transactions: VersionedTransaction[];
    private readonly transactionLimit: number;

    constructor(txs: VersionedTransaction[], transactionLimit: number) {
        this.transactions = txs;
        this.transactionLimit = transactionLimit;
    }

    addTipTx(
        keypair: Keypair,
        tipLamports: number,
        tipAccount: PublicKey,
        recentBlockhash: string
    ): Bundle | Error {
        const numTransactions = this.transactions.length + 1;
        if (numTransactions > this.transactionLimit) {
            return new Error(`${numTransactions} exceeds transaction limit of ${this.transactionLimit}`);
        }

        const tipIx = SystemProgram.transfer({
            fromPubkey: keypair.publicKey,
            toPubkey: tipAccount,
            lamports: tipLamports,
        });

        const messageV0 = new TransactionMessage({
            payerKey: keypair.publicKey,
            recentBlockhash: recentBlockhash,
            instructions: [tipIx],
        }).compileToV0Message();

        const tipTx = new VersionedTransaction(messageV0);
        tipTx.sign([keypair]);

        this.transactions.push(tipTx);
        return this;
    }

    getTransactions(): VersionedTransaction[] {
        return this.transactions;
    }
}

// Send bundle to Jito
async function sendBundle(bundle: Bundle): Promise<string> {
    try {
        const transactions = bundle.getTransactions();
        const serializedTxs = transactions.map(tx => 
            Buffer.from(tx.serialize()).toString('base64')
        );

        console.log(`📦 发送bundle到Jito，包含 ${transactions.length} 个交易`);

        const response = await axios.post(`${JITO_BLOCK_ENGINE_URL}/api/v1/bundles`, {
            jsonrpc: "2.0",
            id: 1,
            method: "sendBundle",
            params: [serializedTxs, { encoding: "base64" }]
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (response.data.error) {
            throw new Error(`Jito bundle error: ${response.data.error.message}`);
        }

        return response.data.result;
    } catch (error) {
        console.error(`Error sending bundle: ${error}`);
        throw error;
    }
}

// 获取所有代币账户
async function getAllTokenAccounts(owner: PublicKey): Promise<PublicKey[]> {
    try {
        const tokenAccounts = await connection.getParsedTokenAccountsByOwner(owner, {
            programId: TOKEN_PROGRAM_ID
        });
        
        return tokenAccounts.value.map(account => account.pubkey);
    } catch (error) {
        console.error("获取代币账户失败:", error);
        return [];
    }
}

// 检查账户是否为空（余额为0）
async function getEmptyTokenAccounts(tokenAccounts: PublicKey[]): Promise<PublicKey[]> {
    const emptyAccounts: PublicKey[] = [];
    
    // 批量查询账户信息
    const accountInfos = await connection.getMultipleAccountsInfo(tokenAccounts);
    
    for (let i = 0; i < tokenAccounts.length; i++) {
        const accountInfo = accountInfos[i];
        if (accountInfo && accountInfo.data.length >= 64) {
            // 解析代币账户数据，检查余额（偏移量32-40是amount字段）
            const amount = accountInfo.data.readBigUInt64LE(32);
            if (amount === BigInt(0)) {
                emptyAccounts.push(tokenAccounts[i]);
                console.log(`  空账户: ${tokenAccounts[i].toBase58()}`);
            }
        }
    }
    
    console.log(`找到 ${emptyAccounts.length} 个空代币账户可回收租金`);
    return emptyAccounts;
}

// 创建批量关闭账户交易
async function createBatchCloseAccountsTransaction(
    emptyAccounts: PublicKey[],
    owner: PublicKey,
    recentBlockhash: string
): Promise<VersionedTransaction> {
    const instructions: TransactionInstruction[] = [];
    
    // 添加计算预算
    instructions.push(
        ComputeBudgetProgram.setComputeUnitPrice({ microLamports: 10000 }),
        ComputeBudgetProgram.setComputeUnitLimit({ units: 1400000 }) // 增加限制以支持更多账户
    );
    
    // 为每个空账户添加关闭指令
    for (const account of emptyAccounts) {
        const closeInstruction = createCloseAccountInstruction(
            account,
            owner, // 租金接收者
            owner  // 账户所有者
        );
        instructions.push(closeInstruction);
    }
    
    console.log(`创建批量关闭交易，包含 ${emptyAccounts.length} 个账户`);
    
    // 创建交易
    const messageV0 = new TransactionMessage({
        payerKey: owner,
        recentBlockhash: recentBlockhash,
        instructions: instructions,
    }).compileToV0Message();
    
    const transaction = new VersionedTransaction(messageV0);
    return transaction;
}

// 批量回收租金主函数
async function batchReclaimRent(owner: PublicKey, recentBlockhash: string): Promise<string[]> {
    try {
        console.log("\n🏦 开始批量回收租金...");
        
        // 1. 获取所有代币账户
        const allTokenAccounts = await getAllTokenAccounts(owner);
        console.log(`找到 ${allTokenAccounts.length} 个代币账户`);
        
        if (allTokenAccounts.length === 0) {
            console.log("没有代币账户需要处理");
            return [];
        }
        
        // 2. 筛选出空账户
        const emptyAccounts = await getEmptyTokenAccounts(allTokenAccounts);
        
        if (emptyAccounts.length === 0) {
            console.log("没有空账户需要回收租金");
            return [];
        }
        
        // 3. 批量处理，每次最多28个账户
        const BATCH_SIZE = 28;
        const bundleIds: string[] = [];
        
        for (let i = 0; i < emptyAccounts.length; i += BATCH_SIZE) {
            const batch = emptyAccounts.slice(i, i + BATCH_SIZE);
            console.log(`\n处理第 ${Math.floor(i / BATCH_SIZE) + 1} 批，包含 ${batch.length} 个账户`);
            
            // 创建批量关闭交易
            const transaction = await createBatchCloseAccountsTransaction(batch, owner, recentBlockhash);
            transaction.sign([wallet]);
            
            // 创建bundle并添加tip
            const bundle = new Bundle([transaction], 4);
            const tipAccount = new PublicKey(JITO_TIP_ACCOUNTS[Math.floor(Math.random() * JITO_TIP_ACCOUNTS.length)]);
            const tipResult = bundle.addTipTx(
                wallet,
                Math.floor(TIP_AMOUNT * LAMPORTS_PER_SOL),
                tipAccount,
                recentBlockhash
            );

            if (tipResult instanceof Error) {
                throw tipResult;
            }

            // 发送bundle
            const bundleId = await sendBundle(bundle);
            bundleIds.push(bundleId);
            
            console.log(`✅ 第 ${Math.floor(i / BATCH_SIZE) + 1} 批Bundle已发送: ${bundleId}`);
            
            // 批次间等待，避免过快发送
            if (i + BATCH_SIZE < emptyAccounts.length) {
                console.log("等待2秒后处理下一批...");
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
        const totalRentReclaimed = emptyAccounts.length * 0.********; // 每个账户约0.******** SOL租金
        console.log(`\n🎉 批量租金回收完成!`);
        console.log(`总共关闭 ${emptyAccounts.length} 个账户`);
        console.log(`预计回收租金: ${totalRentReclaimed.toFixed(6)} SOL`);
        console.log(`发送了 ${bundleIds.length} 个Bundle`);
        
        return bundleIds;
        
    } catch (error) {
        console.error("❌ 批量租金回收错误:", error);
        throw error;
    }
}

// 主函数
async function main(): Promise<void> {
    try {
        console.log("🏦 批量租金回收工具");
        console.log(`钱包地址: ${wallet.publicKey.toBase58()}`);
        
        // 获取当前SOL余额
        const balance = await connection.getBalance(wallet.publicKey);
        console.log(`当前SOL余额: ${balance / LAMPORTS_PER_SOL} SOL`);
        
        // 获取blockhash
        console.log("\n📋 获取Blockhash...");
        const latestBlockhash = await connection.getLatestBlockhash();
        console.log(`✅ Blockhash: ${latestBlockhash.blockhash}`);
        
        // 执行批量租金回收
        const bundleIds = await batchReclaimRent(wallet.publicKey, latestBlockhash.blockhash);
        
        if (bundleIds.length > 0) {
            console.log(`\n🎉 批量租金回收完成!`);
            console.log(`发送的Bundle IDs:`);
            bundleIds.forEach((id, index) => {
                console.log(`  ${index + 1}. ${id}`);
            });
            
            // 等待一段时间后检查最终余额
            console.log("\n⏳ 等待10秒后检查最终余额...");
            await new Promise(resolve => setTimeout(resolve, 10000));
            
            const finalBalance = await connection.getBalance(wallet.publicKey);
            const gainedSOL = (finalBalance - balance) / LAMPORTS_PER_SOL;
            console.log(`最终SOL余额: ${finalBalance / LAMPORTS_PER_SOL} SOL`);
            console.log(`实际回收租金: ${gainedSOL.toFixed(6)} SOL`);
        } else {
            console.log("\n✅ 没有需要回收的租金");
        }
        
    } catch (error) {
        console.error("❌ 批量租金回收错误:", error);
    }
}

// Run if called directly
if (require.main === module) {
    main();
} 