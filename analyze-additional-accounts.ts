import { Connection, PublicKey } from "@solana/web3.js";

const connection = new Connection("https://solana-rpc.publicnode.com");

// The two mysterious additional accounts from the successful transaction
const ACCOUNT_1 = new PublicKey("2MHcrhQLydGctT7qEix8yo7dhn2SFRzevftoToLRzWeb");
const ACCOUNT_2 = new PublicKey("Cg9MPA27Hekj6qCSHJR5hXkvP2sMYsRSaZdvvU926Xx9");

// Known accounts for comparison
const MACARON_MINT = new PublicKey("BkLVwkdBXTDuM9PzALPvxVyymwmNTcNNu5KbYnZEpump");
const MACARON_POOL = new PublicKey("26nyYkndRG32GRF84gCdjoY5xUSEjAY9wJnayrLc7Xp9");
const WSOL_MINT = new PublicKey("So11111111111111111111111111111111111111112");
const PROTOCOL_FEE_RECIPIENT = new PublicKey("FWsW1xNtWscwNmKv6wVsU1iTzRN6wmmk3MjxRP5tT7hz");

async function analyzeAccount(account: PublicKey, name: string) {
    try {
        console.log(`\n=== ANALYZING ${name} ===`);
        console.log(`Address: ${account.toBase58()}`);
        
        // Get account info
        const accountInfo = await connection.getAccountInfo(account);
        
        if (!accountInfo) {
            console.log("❌ Account not found");
            return;
        }
        
        console.log(`Owner: ${accountInfo.owner.toBase58()}`);
        console.log(`Data length: ${accountInfo.data.length} bytes`);
        console.log(`Lamports: ${accountInfo.lamports}`);
        console.log(`Executable: ${accountInfo.executable}`);
        console.log(`Rent epoch: ${accountInfo.rentEpoch}`);
        
        // Check if it's a token account
        if (accountInfo.owner.toBase58() === "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA" && accountInfo.data.length === 165) {
            console.log("🪙 This is a TOKEN ACCOUNT");
            
            // Parse token account data
            const mint = new PublicKey(accountInfo.data.slice(0, 32));
            const owner = new PublicKey(accountInfo.data.slice(32, 64));
            const amount = accountInfo.data.readBigUInt64LE(64);
            
            console.log(`  Mint: ${mint.toBase58()}`);
            console.log(`  Owner: ${owner.toBase58()}`);
            console.log(`  Amount: ${amount}`);
            
            // Check what token this is
            if (mint.equals(MACARON_MINT)) {
                console.log("  🍪 This is a MACARON token account!");
            } else if (mint.equals(WSOL_MINT)) {
                console.log("  💰 This is a WSOL token account!");
            } else {
                console.log(`  🔍 Unknown token: ${mint.toBase58()}`);
            }
        }
        
        // Check if it's a pool account
        if (accountInfo.data.length === 211) {
            console.log("🏊 This might be a POOL ACCOUNT");
            
            // Try to parse pool data
            try {
                const mint1 = new PublicKey(accountInfo.data.slice(43, 75));
                const mint2 = new PublicKey(accountInfo.data.slice(75, 107));
                
                console.log(`  Mint 1: ${mint1.toBase58()}`);
                console.log(`  Mint 2: ${mint2.toBase58()}`);
            } catch (error) {
                console.log("  ❌ Failed to parse pool data");
            }
        }
        
        // Check if it's related to protocol fees
        if (account.equals(PROTOCOL_FEE_RECIPIENT)) {
            console.log("💰 This is the PROTOCOL FEE RECIPIENT");
        }
        
        // Show first 64 bytes of data for analysis
        if (accountInfo.data.length > 0) {
            const preview = accountInfo.data.slice(0, Math.min(64, accountInfo.data.length));
            console.log(`Data preview (first ${preview.length} bytes):`);
            console.log(Array.from(preview).map(b => b.toString(16).padStart(2, '0')).join(' '));
        }
        
    } catch (error) {
        console.error(`Error analyzing ${name}:`, error);
    }
}

async function main() {
    console.log("🔍 Analyzing the mysterious additional accounts from the successful transaction...");
    
    await analyzeAccount(ACCOUNT_1, "ACCOUNT_1 (2MHcrhQLydGctT7qEix8yo7dhn2SFRzevftoToLRzWeb)");
    await analyzeAccount(ACCOUNT_2, "ACCOUNT_2 (Cg9MPA27Hekj6qCSHJR5hXkvP2sMYsRSaZdvvU926Xx9)");
    
    // Also analyze some known accounts for comparison
    console.log("\n" + "=".repeat(60));
    console.log("COMPARISON WITH KNOWN ACCOUNTS");
    console.log("=".repeat(60));
    
    await analyzeAccount(MACARON_POOL, "MACARON_POOL");
    await analyzeAccount(PROTOCOL_FEE_RECIPIENT, "PROTOCOL_FEE_RECIPIENT");
}

main().catch(console.error); 