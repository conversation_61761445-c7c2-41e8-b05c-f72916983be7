# 调试标志说明

系统现在支持通过环境变量控制各种调试信息的输出，以减少日志噪音，让关键信息更容易看到。

## 可用的调试标志

### 价格相关调试
- `DEBUG_PRICE_MATCHING=true` - 显示价格匹配的详细信息
- `DEBUG_PRICE_RECORDING=true` - 显示价格记录的详细信息

### V3预测器调试
- `DEBUG_V3_TRANSACTIONS=true` - 显示V3预测器添加交易的信息
- `DEBUG_V3_PREDICTOR_DETAILS=true` - 显示V3预测器内部详细信息（标准化等）
- `DEBUG_V3_QUEUE_STATUS=true` - 显示V3预测器队列状态

### 性能调试
- `DEBUG_PREDICTION_PERFORMANCE=true` - 显示预测性能统计
- `DEBUG_PERFORMANCE_TIMING=true` - 显示详细的性能计时

### 持仓和交易调试
- `DEBUG_POSITION_CHECKS=true` - 显示持仓状态检查详情
- `DEBUG_TELEGRAM_NOTIFICATIONS=true` - 显示Telegram通知的详细调试信息

## 使用方法

### 启动时设置环境变量
```bash
# 启用价格匹配调试
DEBUG_PRICE_MATCHING=true npm start

# 启用多个调试标志
DEBUG_V3_TRANSACTIONS=true DEBUG_PREDICTION_PERFORMANCE=true npm start

# 启用所有调试信息（不推荐，会产生大量日志）
DEBUG_PRICE_MATCHING=true DEBUG_PRICE_RECORDING=true DEBUG_V3_TRANSACTIONS=true DEBUG_V3_PREDICTOR_DETAILS=true DEBUG_V3_QUEUE_STATUS=true DEBUG_PREDICTION_PERFORMANCE=true DEBUG_PERFORMANCE_TIMING=true DEBUG_POSITION_CHECKS=true DEBUG_TELEGRAM_NOTIFICATIONS=true npm start
```

### 运行时动态设置
```bash
# 在运行的Node.js进程中设置
process.env.DEBUG_PRICE_MATCHING = 'true';
```

## 默认行为

如果没有设置任何调试标志，系统将只显示：
- 关键的交易决策信息
- 错误和警告信息
- 基本的预测结果
- 重要的系统状态更新

这样可以让日志更清洁，更容易找到重要信息。

## 建议的调试组合

### 调试价格问题
```bash
DEBUG_PRICE_MATCHING=true DEBUG_PRICE_RECORDING=true npm start
```

### 调试预测性能
```bash
DEBUG_PREDICTION_PERFORMANCE=true DEBUG_PERFORMANCE_TIMING=true npm start
```

### 调试V3预测器
```bash
DEBUG_V3_TRANSACTIONS=true DEBUG_V3_QUEUE_STATUS=true npm start
```

### 调试Telegram通知
```bash
DEBUG_TELEGRAM_NOTIFICATIONS=true npm start
``` 