# 🤖 AI自动交易系统使用指南

## 📋 目录
- [快速启动](#快速启动)
- [配置说明](#配置说明)
- [交易程序启动](#交易程序启动)
- [关键配置项](#关键配置项)
- [安全须知](#安全须知)
- [监控和调试](#监控和调试)

## 🚀 快速启动

### 1. 启动交易机器人
```bash
# 启动独立的交易机器人（推荐）
npx ts-node trading-bot.ts

# 或者启动包含GRPC监听的完整系统
npx ts-node index.ts
```

### 2. 主要文件说明
- **`trading-bot.ts`** - 🎯 **主要交易程序启动文件**
- **`index.ts`** - GRPC数据监听器 + 交易程序
- **`trading-config.ts`** - 统一配置文件
- **`example.env`** - 环境变量配置示例

## ⚙️ 配置说明

### 环境变量配置
复制 `example.env` 为 `.env` 并修改配置：

```bash
cp example.env .env
nano .env  # 编辑配置文件
```

### 关键环境变量
```env
# 🔑 钱包私钥（真实交易必需）
PRIVATE_KEY=your_base64_private_key_here

# 📊 监控的Token地址
TARGET_TOKEN_ADDRESSES=9BB6NFEcjBCtnNLFko2FqVQBq8HHM13kCyYcdQbgpump

# 📝 纸上交易模式（安全测试）
PAPER_TRADING=true
```

## 💰 交易金额配置

### 代码中的配置（trading-bot.ts）
```typescript
const CUSTOM_CONFIG: PartialTradingSystemConfig = {
  trading: {
    tradeAmountSol: 0.05,        // 🎯 每次交易金额（SOL）
    initialCapitalSol: 2.0,      // 💰 初始资金（SOL）
    maxPositions: 3,             // 📊 最大同时持仓数
    paperTrading: true,          // 📝 纸上交易模式
    slippageTolerance: 0.03      // 📈 滑点容忍度（3%）
  }
};
```

### 如何修改交易金额
1. **修改 `trading-bot.ts` 中的 `CUSTOM_CONFIG`**
2. **或者设置环境变量**
   ```env
   TRADE_AMOUNT_SOL=0.1
   INITIAL_CAPITAL_SOL=3.0
   ```

## 🛡️ 累计止损配置

### 止损设置（防止过度亏损）
```typescript
risk: {
  maxTotalLossSol: 0.5,       // 🔴 累计最大亏损0.5 SOL（达到后停止交易）
  maxDailyLossSol: 0.2,       // 📅 日内最大亏损0.2 SOL
  stopLossPercentage: 0.08,   // 📉 单笔止损8%
  takeProfitPercentage: 0.15, // 📈 单笔止盈15%
}
```

### 关键说明
- **`maxTotalLossSol`**: 🔴 **累计亏损达到此值后，系统将自动停止所有交易**
- **`maxDailyLossSol`**: 📅 **单日亏损限制**
- **`stopLossPercentage`**: 📉 **单笔交易止损比例**

## 🔑 私钥配置

### 1. 环境变量方式（推荐）
```env
PRIVATE_KEY=your_base64_private_key_here
```

### 2. 代码中配置
```typescript
wallet: {
  privateKey: "your_base64_private_key_here",
  rpcEndpoint: "https://api.mainnet-beta.solana.com"
}
```

### 私钥格式
- **格式**: Base64编码的私钥
- **获取方式**: 从Phantom钱包或其他Solana钱包导出
- **安全**: 请妥善保管，不要泄露

## 🎮 启动方式对比

### 方式1: 独立交易机器人（推荐）
```bash
npx ts-node trading-bot.ts
```
**特点**:
- ✅ 纯交易逻辑，简单直接
- ✅ 自动配置管理
- ✅ 实时统计显示
- ✅ 优雅关闭处理

### 方式2: 完整监听+交易系统
```bash
npx ts-node index.ts
```
**特点**:
- ✅ 包含GRPC数据监听
- ✅ 实时交易数据处理
- ✅ 更完整的数据流
- ⚠️ 配置更复杂

## 📊 配置优先级

配置加载顺序（后者覆盖前者）：
1. **默认配置** (`DEFAULT_CONFIG`)
2. **环境变量** (`.env`文件)
3. **代码配置** (`CUSTOM_CONFIG`)

## 🔒 安全模式

### 纸上交易模式（强烈推荐测试）
```typescript
trading: {
  paperTrading: true  // 🛡️ 启用纸上交易，不会真实花费资金
}
```

### 真实交易模式
```typescript
trading: {
  paperTrading: false  // ⚠️ 真实交易模式，需要设置私钥
}
```

## 📈 监控和调试

### 实时统计显示
```
📊 实时交易统计:
运行时长: 0h 5m 23s
总交易: 3 | 胜率: 66.7% | 总盈亏: 0.0250 SOL
当前余额: 2.0250 SOL | 活跃仓位: 1 | 最大回撤: 1.25%
```

### 日志级别
```typescript
monitoring: {
  logLevel: 'info',           // debug, info, warn, error
  enableDetailedLogs: true,   // 详细日志
  statsUpdateIntervalSeconds: 15  // 统计更新间隔
}
```

## ⚠️ 重要安全提醒

### 🔴 资金安全
1. **首次使用务必启用纸上交易模式**
2. **累计止损设置不能超过你能承受的损失**
3. **私钥安全，不要泄露或上传到公共代码库**

### 🔴 测试流程
1. **纸上交易测试** → 验证策略有效性
2. **小额真实交易** → 验证系统稳定性  
3. **逐步增加资金** → 正式运行

### 🔴 风险控制
```typescript
risk: {
  maxTotalLossSol: 0.1,       // 🔴 设置一个你能承受的最大亏损
  maxDailyLossSol: 0.05,      // 🔴 日内止损
  stopLossPercentage: 0.05,   // 🔴 单笔止损5%
}
```

## 🛠️ 故障排除

### 常见问题
1. **模型文件未找到**: 确保 `.cbm` 文件在正确位置
2. **私钥格式错误**: 检查Base64格式是否正确
3. **网络连接问题**: 检查RPC端点是否可访问
4. **余额不足**: 确保钱包有足够的SOL用于交易和Gas费

### 调试技巧
```typescript
monitoring: {
  logLevel: 'debug',          // 启用详细调试日志
  enableDetailedLogs: true    // 显示更多信息
}
```

## 📞 支持

如遇问题，请检查：
1. **配置文件格式是否正确**
2. **环境变量是否设置**
3. **模型文件是否存在**
4. **网络连接是否正常**

---

**⚠️ 风险提示**: 加密货币交易具有高风险，可能导致资金损失。请谨慎投资，做好风险管理。 