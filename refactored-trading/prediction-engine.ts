import { TokenTradingInstance, TradeDecision, TransactionData } from "./types-and-interfaces";
import { AI_CONFIG, REAL_TRADING_CONFIG, TARGET_WALLET } from "./trading-constants";

/**
 * 预测引擎 - 负责AI驱动的买入/卖出预测和交易决策
 */
export class PredictionEngine {
  private predictionLocks: Set<string> = new Set();
  private tokenInstances: Map<string, TokenTradingInstance>;

  constructor(tokenInstances: Map<string, TokenTradingInstance>) {
    this.tokenInstances = tokenInstances;
  }

  // 为特定Token进行预测
  async makePredictionForToken(tokenAddress: string): Promise<{ buyPrediction: number, sellPrediction: number } | null> {
    // 🔥 防止重复预测 - 使用锁机制
    if (this.predictionLocks.has(tokenAddress)) {
      console.log(`⏳ Token ${tokenAddress.slice(0, 8)}... 预测正在进行中，跳过`);
      return null;
    }

    try {
      // 🔥 加锁
      this.predictionLocks.add(tokenAddress);
      
      const instance = this.tokenInstances.get(tokenAddress);
      if (!instance || !instance.isActive) {
        console.log(`⚠️ Token ${tokenAddress.slice(0, 8)}... 实例不存在或已停用`);
        return null;
      }

      // 🔥 检查预测间隔
      const now = new Date();
      const timeSinceLastPrediction = now.getTime() - instance.lastPrediction.getTime();
      
      if (timeSinceLastPrediction < AI_CONFIG.predictionInterval) {
        const remainingTime = AI_CONFIG.predictionInterval - timeSinceLastPrediction;
        console.log(`⏰ Token ${tokenAddress.slice(0, 8)}... 距离下次预测还有 ${(remainingTime / 1000).toFixed(1)}s`);
        return null;
      }

      // 🔥 准备特征数据
      const newTransactions = this.getNewTransactionsForToken(tokenAddress, instance);
      
      if (newTransactions.length === 0) {
        console.log(`📊 Token ${tokenAddress.slice(0, 8)}... 没有新交易数据，跳过预测`);
        return null;
      }

      console.log(`🔮 开始AI预测: ${tokenAddress.slice(0, 8)}... (新交易: ${newTransactions.length})`);

      // 🔥 更新特征窗口
      instance.featureWindow.push(...newTransactions);
      
      // 保持特征窗口大小
      if (instance.featureWindow.length > AI_CONFIG.featureWindowSize) {
        instance.featureWindow = instance.featureWindow.slice(-AI_CONFIG.featureWindowSize);
      }

      // 🔥 执行买入预测
      const buyPrediction = await this.executeBuyPrediction(instance);
      
      // 🔥 执行卖出预测
      const sellPrediction = await this.executeSellPrediction(instance);

      // 🔥 更新预测时间
      instance.lastPrediction = now;

      console.log(`✅ AI预测完成: ${tokenAddress.slice(0, 8)}... 买入=${(buyPrediction * 100).toFixed(1)}% 卖出=${(sellPrediction * 100).toFixed(1)}%`);

      return { buyPrediction, sellPrediction };

    } catch (error) {
      console.error(`❌ 预测失败 ${tokenAddress.slice(0, 8)}...:`, error);
      return null;
    } finally {
      // 🔥 解锁
      this.predictionLocks.delete(tokenAddress);
    }
  }

  // 执行买入预测
  private async executeBuyPrediction(instance: TokenTradingInstance): Promise<number> {
    try {
      console.log(`🤖 执行买入预测: ${instance.address.slice(0, 8)}...`);
      
      // 调用买入预测器
      const buyPredictionResult = await instance.buyPredictor.predictBuy();
      
      if (buyPredictionResult && typeof buyPredictionResult.prediction === 'number') {
        console.log(`📈 买入预测结果: ${(buyPredictionResult.prediction * 100).toFixed(1)}%`);
        return buyPredictionResult.prediction;
      }
      
      return 0;
    } catch (error) {
      console.error(`❌ 买入预测失败: ${instance.address.slice(0, 8)}...`, error);
      return 0;
    }
  }

  // 执行卖出预测
  private async executeSellPrediction(instance: TokenTradingInstance): Promise<number> {
    try {
      console.log(`🤖 执行卖出预测: ${instance.address.slice(0, 8)}...`);
      
      // 调用卖出预测器
      const sellPredictionResult = await instance.sellPredictor.predictSell();
      
      if (sellPredictionResult && typeof sellPredictionResult.prediction === 'number') {
        console.log(`📉 卖出预测结果: ${(sellPredictionResult.prediction * 100).toFixed(1)}%`);
        return sellPredictionResult.prediction;
      }
      
      return 0;
    } catch (error) {
      console.error(`❌ 卖出预测失败: ${instance.address.slice(0, 8)}...`, error);
      return 0;
    }
  }

  // 制定交易决策
  makeTradeDecision(
    tokenAddress: string, 
    buyPrediction: number, 
    sellPrediction: number, 
    instance: TokenTradingInstance
  ): TradeDecision {
    const buyThreshold = REAL_TRADING_CONFIG.models?.buyThreshold || 0.7;
    const sellThreshold = REAL_TRADING_CONFIG.models?.sellThreshold || 0.6;
    
    // 检查是否有持仓
    const hasPosition = instance.currentHolding && instance.currentHolding.amount > 0;
    
    // 买入决策
    if (!hasPosition && buyPrediction >= buyThreshold) {
      return {
        action: 'buy',
        confidence: buyPrediction,
        reason: `AI买入信号强度${(buyPrediction * 100).toFixed(1)}% >= ${(buyThreshold * 100).toFixed(1)}%阈值`,
        amount: REAL_TRADING_CONFIG.trading?.tradeAmountSol || 0.0001
      };
    }
    
    // 卖出决策
    if (hasPosition && sellPrediction >= sellThreshold) {
      return {
        action: 'sell',
        confidence: sellPrediction,
        reason: `AI卖出信号强度${(sellPrediction * 100).toFixed(1)}% >= ${(sellThreshold * 100).toFixed(1)}%阈值`,
        amount: instance.currentHolding?.amount || 0
      };
    }
    
    // 持有决策
    return {
      action: 'hold',
      confidence: Math.max(buyPrediction, sellPrediction),
      reason: `买入${(buyPrediction * 100).toFixed(1)}%/卖出${(sellPrediction * 100).toFixed(1)}%信号均未达到阈值`
    };
  }

  // 初始化Token预测器数据 - 完全按照原文件
  async initializeTokenPredictorData(tokenAddress: string, instance: TokenTradingInstance): Promise<void> {
    const allHistoricalTransactions = this.getTokenSpecificTransactions(tokenAddress);
    
    if (allHistoricalTransactions.length > 0) {
      for (const tx of allHistoricalTransactions) {
        const featureData = {
          timestamp: tx.timestamp instanceof Date ? tx.timestamp : new Date(tx.timestamp),
          action: tx.action,
          sol_amount: tx.sol_amount,
          usd_amount: tx.usd_amount,
          is_target_wallet: (tx.wallet || '').includes(TARGET_WALLET.slice(0, 8)),
          wallet: tx.wallet || 'unknown',
          block_number: tx.block_number
        };
        
        instance.buyPredictor.addTransaction(featureData);
        instance.sellPredictor.addTransaction(featureData);
      }
      
      const latestTransaction = allHistoricalTransactions[allHistoricalTransactions.length - 1];
      instance.lastProcessedTransactionTime = latestTransaction.timestamp;
    } else {
      instance.lastProcessedTransactionTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }
  }

  // 获取Token的新交易数据
  private getNewTransactionsForToken(tokenAddress: string, instance: TokenTradingInstance): TransactionData[] {
    try {
      // 这里应该从某个全局交易数据源获取新交易
      // 为了简化，返回空数组，实际实现需要从流处理器获取数据
      return [];
    } catch (error) {
      console.error(`❌ 获取新交易失败:`, error);
      return [];
    }
  }

  // 获取Token特定的历史交易
  private getTokenSpecificTransactions(tokenAddress: string): TransactionData[] {
    try {
      // 这里应该从历史数据中获取该Token的交易
      // 为了简化，返回空数组，实际实现需要从数据存储获取
      return [];
    } catch (error) {
      console.error(`❌ 获取历史交易失败:`, error);
      return [];
    }
  }

  // 批量预测多个Token
  async batchPredict(tokenAddresses: string[]): Promise<Map<string, { buyPrediction: number, sellPrediction: number }>> {
    const results = new Map();
    
    // 并发执行预测，但限制并发数
    const batchSize = 3;
    for (let i = 0; i < tokenAddresses.length; i += batchSize) {
      const batch = tokenAddresses.slice(i, i + batchSize);
      
      const batchPromises = batch.map(async (tokenAddress) => {
        const prediction = await this.makePredictionForToken(tokenAddress);
        if (prediction) {
          results.set(tokenAddress, prediction);
        }
      });
      
      await Promise.all(batchPromises);
    }
    
    return results;
  }

  // 获取预测统计信息
  getPredictionStats(): {
    totalPredictions: number;
    activePredictions: number;
    avgBuyPrediction: number;
    avgSellPrediction: number;
  } {
    let totalPredictions = 0;
    let activePredictions = 0;
    let totalBuyPredictions = 0;
    let totalSellPredictions = 0;
    
    for (const instance of this.tokenInstances.values()) {
      if (instance.lastPrediction.getTime() > 0) {
        totalPredictions++;
        
        // 检查是否在最近时间内有预测
        const timeSinceLastPrediction = Date.now() - instance.lastPrediction.getTime();
        if (timeSinceLastPrediction < AI_CONFIG.predictionInterval * 2) {
          activePredictions++;
        }
      }
    }
    
    return {
      totalPredictions,
      activePredictions,
      avgBuyPrediction: totalPredictions > 0 ? totalBuyPredictions / totalPredictions : 0,
      avgSellPrediction: totalPredictions > 0 ? totalSellPredictions / totalPredictions : 0
    };
  }

  // 清理预测锁
  clearPredictionLocks(): void {
    this.predictionLocks.clear();
    console.log('🧹 预测锁已清理');
  }
} 