import { 
  TokenTradingInstance, 
  TokenActivity, 
  MonitoringQueueItem, 
  SellOnlyBufferItem,
  TokenStats,
  CurrentPosition
} from "./types-and-interfaces";
import { BuyPredictor } from '../predictors/buy-predictor';
import { SellPredictor } from "../predictors/sell-predictor";
import { MAX_MONITORED_TOKENS } from "./trading-constants";

/**
 * Token管理器 - 负责管理Token实例、监控队列和相关操作
 */
export class TokenManager {
  private tokenInstances: Map<string, TokenTradingInstance> = new Map();
  private tokenActivities: Map<string, TokenActivity> = new Map();
  private tokenMonitoringQueue: MonitoringQueueItem[] = [];
  private sellOnlyBufferQueue: Map<string, SellOnlyBufferItem> = new Map();
  private tokenSymbolCache: Map<string, string> = new Map();

  // 创建Token实例
  async createTokenInstance(tokenAddress: string): Promise<TokenTradingInstance> {
    console.log(`🔧 创建Token实例: ${tokenAddress.slice(0, 8)}...`);
    
    const activity: TokenActivity = this.tokenActivities.get(tokenAddress) || {
      address: tokenAddress,
      firstSeen: new Date(),
      lastSeen: new Date(),
      transactionCount: 0,
      actions: []
    };

    const instance: TokenTradingInstance = {
      address: tokenAddress,
      activity,
      buyPredictor: new BuyPredictor(tokenAddress),
      sellPredictor: new SellPredictor(tokenAddress),
      featureWindow: [],
      lastPrediction: new Date(0),
      isActive: true,
      isTrading: false,
      lastTradeTime: new Date(0),
      tradingHistory: [],
      currentHolding: null,
      stats: {
        totalTrades: 0,
        successfulTrades: 0,
        totalPnL: 0,
        currentPosition: 0
      },
      pendingTransactions: new Map(),
      poolExtracted: false
    };

    this.tokenInstances.set(tokenAddress, instance);
    console.log(`✅ Token实例创建完成: ${tokenAddress.slice(0, 8)}...`);
    
    return instance;
  }

  // 获取Token实例
  getTokenInstance(tokenAddress: string): TokenTradingInstance | undefined {
    return this.tokenInstances.get(tokenAddress);
  }

  // 获取所有Token实例
  getAllTokenInstances(): Map<string, TokenTradingInstance> {
    return this.tokenInstances;
  }

  // 获取活跃Token列表
  getActiveTokens(): string[] {
    return Array.from(this.tokenInstances.keys()).filter(address => {
      const instance = this.tokenInstances.get(address);
      return instance?.isActive;
    });
  }

  // 管理Token监控队列
  async manageTokenMonitoringQueue(newTokenAddress: string): Promise<void> {
    // 检查是否已经在监控
    if (this.tokenInstances.has(newTokenAddress)) {
      return;
    }

    // 检查是否已经在队列中
    const existingInQueue = this.tokenMonitoringQueue.find(item => item.address === newTokenAddress);
    if (existingInQueue) {
      return;
    }

    // 添加到监控队列
    this.tokenMonitoringQueue.push({
      address: newTokenAddress,
      addedTime: new Date()
    });

    console.log(`📝 Token已添加到监控队列: ${newTokenAddress.slice(0, 8)}... (队列长度: ${this.tokenMonitoringQueue.length})`);

    // 如果超过最大监控数量，移除最老的Token
    if (this.tokenMonitoringQueue.length > MAX_MONITORED_TOKENS) {
      const oldestToken = this.tokenMonitoringQueue.shift();
      if (oldestToken) {
        await this.removeTokenFromMonitoring(oldestToken.address, "队列容量限制");
      }
    }

    // 为新Token创建实例
    try {
      await this.createTokenInstance(newTokenAddress);
    } catch (error) {
      console.error(`❌ 创建Token实例失败: ${newTokenAddress.slice(0, 8)}...`, error);
    }
  }

  // 从监控中移除Token
  async removeTokenFromMonitoring(tokenAddress: string, reason: string): Promise<void> {
    console.log(`🗑️ 移除Token监控: ${tokenAddress.slice(0, 8)}... (原因: ${reason})`);
    
    // 从实例中移除
    const instance = this.tokenInstances.get(tokenAddress);
    if (instance) {
      instance.isActive = false;
      this.tokenInstances.delete(tokenAddress);
    }
    
    // 从监控队列中移除
    const queueIndex = this.tokenMonitoringQueue.findIndex(item => item.address === tokenAddress);
    if (queueIndex !== -1) {
      this.tokenMonitoringQueue.splice(queueIndex, 1);
    }
    
    // 从活动记录中移除
    this.tokenActivities.delete(tokenAddress);
  }

  // 添加到卖出缓冲区
  addToSellOnlyBuffer(tokenAddress: string, reason: string): void {
    this.sellOnlyBufferQueue.set(tokenAddress, {
      address: tokenAddress,
      addedTime: new Date(),
      reason,
      lastCheckTime: new Date()
    });
    
    console.log(`🔄 Token已添加到卖出缓冲区: ${tokenAddress.slice(0, 8)}... (原因: ${reason})`);
  }

  // 管理卖出缓冲队列
  async manageSellOnlyBufferQueue(): Promise<void> {
    const currentTime = new Date();
    const bufferDuration = 5 * 60 * 1000; // 5分钟缓冲时间

    for (const [tokenAddress, bufferItem] of this.sellOnlyBufferQueue.entries()) {
      const timeSinceAdded = currentTime.getTime() - bufferItem.addedTime.getTime();
      
      if (timeSinceAdded >= bufferDuration) {
        // 缓冲时间已过，检查是否仍有持仓
        const instance = this.tokenInstances.get(tokenAddress);
        
        if (!instance || !instance.currentHolding || instance.currentHolding.amount === 0) {
          // 没有持仓，可以完全移除
          await this.completelyRemoveToken(tokenAddress, "卖出缓冲期结束，无持仓");
          this.sellOnlyBufferQueue.delete(tokenAddress);
        } else {
          // 仍有持仓，更新检查时间
          bufferItem.lastCheckTime = currentTime;
        }
      }
    }
  }

  // 完全移除Token
  private async completelyRemoveToken(tokenAddress: string, reason: string): Promise<void> {
    console.log(`🗑️ 完全移除Token: ${tokenAddress.slice(0, 8)}... (原因: ${reason})`);
    
    // 从所有数据结构中移除
    this.tokenInstances.delete(tokenAddress);
    this.tokenActivities.delete(tokenAddress);
    this.sellOnlyBufferQueue.delete(tokenAddress);
    
    // 从监控队列中移除
    const queueIndex = this.tokenMonitoringQueue.findIndex(item => item.address === tokenAddress);
    if (queueIndex !== -1) {
      this.tokenMonitoringQueue.splice(queueIndex, 1);
    }
  }

  // 获取Token统计信息
  getTokenStats(): TokenStats[] {
    return Array.from(this.tokenInstances.values()).map(instance => ({
      tokenAddress: instance.address,
      totalTrades: instance.stats.totalTrades,
      totalPnL: instance.stats.totalPnL,
      transactionCount: instance.activity.transactionCount,
      winRate: instance.stats.totalTrades > 0 ? 
        (instance.stats.successfulTrades / instance.stats.totalTrades) * 100 : 0
    }));
  }

  // 获取当前持仓信息
  getCurrentPositions(): CurrentPosition[] {
    const positions: CurrentPosition[] = [];
    
    for (const instance of this.tokenInstances.values()) {
      if (instance.currentHolding && instance.currentHolding.amount > 0) {
        // 这里需要获取当前价格来计算PnL
        const currentPrice = 0; // 这应该从价格服务获取
        const holdingTime = Date.now() - instance.currentHolding.buyTime.getTime();
        
        positions.push({
          tokenAddress: instance.address,
          position: instance.currentHolding.amount,
          pnl: 0, // 需要计算
          buyPrice: instance.currentHolding.buyPrice,
          currentPrice: currentPrice,
          holdingTime: holdingTime
        });
      }
    }
    
    return positions;
  }

  // 获取卖出缓冲区信息
  getSellOnlyBufferInfo(): Array<{
    tokenAddress: string;
    reason: string;
    addedTime: Date;
    bufferDurationMinutes: number;
    hasPosition: boolean;
  }> {
    return Array.from(this.sellOnlyBufferQueue.values()).map(item => {
      const instance = this.tokenInstances.get(item.address);
      const hasPosition = !!(instance?.currentHolding && instance.currentHolding.amount > 0);
      const bufferDurationMinutes = (Date.now() - item.addedTime.getTime()) / (1000 * 60);
      
      return {
        tokenAddress: item.address,
        reason: item.reason,
        addedTime: item.addedTime,
        bufferDurationMinutes,
        hasPosition
      };
    });
  }

  // 更新Token活动
  updateTokenActivity(tokenAddress: string, action: 'buy' | 'sell', amount: number): void {
    let activity = this.tokenActivities.get(tokenAddress);
    
    if (!activity) {
      activity = {
        address: tokenAddress,
        firstSeen: new Date(),
        lastSeen: new Date(),
        transactionCount: 0,
        actions: []
      };
      this.tokenActivities.set(tokenAddress, activity);
    }
    
    activity.lastSeen = new Date();
    activity.transactionCount++;
    activity.actions.push({
      type: action,
      amount,
      timestamp: new Date()
    });
    
    // 保持最近的100个动作
    if (activity.actions.length > 100) {
      activity.actions = activity.actions.slice(-100);
    }
  }

  // 获取Token符号缓存
  getTokenSymbol(tokenAddress: string): string | undefined {
    return this.tokenSymbolCache.get(tokenAddress);
  }

  // 设置Token符号缓存
  setTokenSymbol(tokenAddress: string, symbol: string): void {
    this.tokenSymbolCache.set(tokenAddress, symbol);
  }

  // 获取监控队列长度
  getMonitoringQueueLength(): number {
    return this.tokenMonitoringQueue.length;
  }

  // 获取卖出缓冲队列大小
  getSellOnlyBufferSize(): number {
    return this.sellOnlyBufferQueue.size;
  }
} 