import { <PERSON>Key, VersionedTransactionResponse } from "@solana/web3.js";
import { TokenTradingInstance, StreamEvent, PriceHistoryEntry, TransactionData } from "./types-and-interfaces";
import { TARGET_WALLET, MY_WALLET, PUMP_FUN_AMM_PROGRAM_ID, MIN_TRANSACTION_SOL } from "./trading-constants";
import { PoolManager } from "./pool-manager";

/**
 * 交易处理器 - 负责处理特定类型的交易和数据提取
 */
export class TransactionProcessor {
  private tokenInstances: Map<string, TokenTradingInstance>;
  private poolManager: PoolManager;
  private realPriceData: Map<string, PriceHistoryEntry[]> = new Map();
  private processingQueue: Array<{ txn: any; data: any; parsedTxn: any }> = [];
  private isProcessing: boolean = false;
  
  // 🔥 事件驱动预测回调
  private predictionCallback?: (tokenAddress: string) => Promise<void>;

  constructor(
    tokenInstances: Map<string, TokenTradingInstance>,
    poolManager: PoolManager
  ) {
    this.tokenInstances = tokenInstances;
    this.poolManager = poolManager;
  }

  // 处理流事件中的交易
  async processStreamTransaction(event: StreamEvent): Promise<void> {
    try {
      console.log(`🔄 TransactionProcessor收到事件: ${event.type}`);
      
      const { 
        txn, 
        parsedTxn, 
        originalData, 
        isTargetWalletInvolved, 
        isMyWalletInvolved, 
        monitoredTokensInvolved 
      } = event.data;

      // 🔥 根据事件类型处理不同的交易
      if (event.type === 'targetWalletTransaction') {
        // 目标钱包交易 - 用于发现新Token
        console.log('🎯 处理目标钱包交易事件');
        await this.processTargetWalletTransaction(txn, originalData, parsedTxn);
        
      } else if (event.type === 'myWalletTransaction') {
        // 我的钱包交易 - 确认交易状态和金额
        console.log('💼 处理我的钱包交易事件');
        await this.processMyWalletTransaction(txn, originalData, parsedTxn);
        
      } else if (event.type === 'targetTokenTransaction') {
        // 目标Token交易 - 事件驱动预测
        console.log('🪙 处理目标Token交易事件');
        const tokenFilters = event.data.tokenFilters || [];
        await this.processTargetTokenActivity(txn, originalData, parsedTxn, tokenFilters);
        
      } else if (event.type === 'transaction') {
        // 兼容旧的处理方式
        console.log('🔄 处理通用交易事件');
        
        // 🔥 优先处理自己钱包的交易（获取真实交易数据）
        if (isMyWalletInvolved) {
          await this.processMyWalletTransaction(txn, originalData, parsedTxn);
          return;
        }
        
        // 如果涉及监控的token但不涉及目标钱包，也要记录
        if (monitoredTokensInvolved && monitoredTokensInvolved.length > 0 && !isTargetWalletInvolved) {
          await this.processMonitoredTokenTransaction(txn, originalData, monitoredTokensInvolved, parsedTxn);
          return;
        }
        
        if (isTargetWalletInvolved) {
          // 处理目标钱包交易
          await this.processTargetWalletTransaction(txn, originalData, parsedTxn);
        }
      } else {
        console.log(`⚠️ 未知事件类型: ${event.type}`);
      }

    } catch (error) {
      console.error('❌ 处理流交易事件失败:', error);
    }
  }

  // 🔥 新增：处理目标Token活动（事件驱动预测）
  private async processTargetTokenActivity(txn: any, data: any, parsedTxn: any, tokenFilters: string[]): Promise<void> {
    console.log(`🪙 目标Token活动 - 涉及${tokenFilters.length}个filter`);
    console.log(`🔍 当前监控Token数量: ${this.tokenInstances.size}`);
    
    // 🔥 调试：显示当前监控的Token列表
    if (this.tokenInstances.size > 0) {
      console.log(`📋 监控Token列表:`);
      for (const [address] of this.tokenInstances) {
        console.log(`   🪙 ${address.slice(0, 8)}...`);
      }
    } else {
      console.log(`⚠️ 当前没有监控任何Token - 这可能是问题所在！`);
    }
    
    try {
      // 🔥 修复：安全提取账户列表，兼容不同的GRPC数据结构
      let accountKeys: string[] = [];
      
      try {
        // 尝试从不同的路径提取账户列表
        if (txn?.transaction?.transaction?.message?.accountKeys) {
          // GRPC V0格式：嵌套的transaction结构
          accountKeys = txn.transaction.transaction.message.accountKeys.map((key: any) => {
            if (typeof key === 'string') return key;
            if (key?.type === 'Buffer' && key?.data) {
              // 🔥 修复：正确转换Buffer为Base58地址
              const bs58 = require('bs58');
              return bs58.encode(Buffer.from(key.data));
            }
            if (key?.toBase58) return key.toBase58();
            return key.toString();
          });
        } else if (txn?.transaction?.message?.staticAccountKeys) {
          // Legacy格式：staticAccountKeys
          accountKeys = txn.transaction.message.staticAccountKeys.map((key: any) => {
            if (typeof key === 'string') return key;
            if (key?.toBase58) return key.toBase58();
            return key.toString();
          });
        } else if (txn?.transaction?.message?.accountKeys) {
          // Legacy格式：accountKeys
          accountKeys = txn.transaction.message.accountKeys.map((key: any) => {
            if (typeof key === 'string') return key;
            if (key?.toBase58) return key.toBase58();
            return key.toString();
          });
        }
        
        console.log(`🔍 提取到${accountKeys.length}个账户地址`);
        
      } catch (keyError) {
        console.log('⚠️ 提取账户列表时出错:', keyError.message);
        accountKeys = [];
      }
      
      // 从监控Token中找到在此交易中涉及的Token
      const monitoredTokens: string[] = [];
      for (const [fullAddress] of this.tokenInstances) {
        if (accountKeys.includes(fullAddress)) {
          monitoredTokens.push(fullAddress);
        }
      }
      
      if (monitoredTokens.length > 0) {
        console.log(`📊 处理监控Token交易: ${monitoredTokens.map(addr => addr.slice(0, 8)).join(', ')}`);
        await this.processMonitoredTokenTransaction(txn, data, monitoredTokens, parsedTxn);
      } else {
        console.log('⚠️ 此交易未涉及任何监控Token');
      }
      
    } catch (error) {
      console.error('❌ 处理Token活动失败:', error);
    }
  }

  // 异步处理队列
  private async processQueueAsync(): Promise<void> {
    if (this.isProcessing || this.processingQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      while (this.processingQueue.length > 0) {
        const item = this.processingQueue.shift()!;
        await this.processTransaction(item.txn, item.data, item.parsedTxn);
      }
    } catch (error) {
      console.error('❌ 处理交易队列时出错:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  // 处理单个交易
  private async processTransaction(txn: any, data: any, parsedTxn: any): Promise<void> {
    try {
      // 检查是否涉及目标钱包
      const isTargetWallet = await this.isWalletInvolvedInTransaction(txn, TARGET_WALLET);
      
      // 检查是否涉及我的钱包
      const isMyWallet = await this.isWalletInvolvedInTransaction(txn, MY_WALLET);
      
      // 检查是否涉及监控的Token
      const monitoredTokensInvolved = this.getInvolvedMonitoredTokens(parsedTxn);

      // 处理目标钱包交易
      if (isTargetWallet) {
        await this.processTargetWalletTransaction(txn, data, parsedTxn);
      }

      // 处理我的钱包交易
      if (isMyWallet) {
        await this.processMyWalletTransaction(txn, data, parsedTxn);
      }

      // 处理监控Token的交易
      if (monitoredTokensInvolved.length > 0) {
        await this.processMonitoredTokenTransaction(txn, data, monitoredTokensInvolved, parsedTxn);
      }

    } catch (error) {
      console.error('❌ 处理交易时出错:', error);
    }
  }

  // 🔥 处理目标钱包交易 - 与原文件完全相同的格式
  private async processTargetWalletTransaction(txn: any, data: any, parsedTxn: any): Promise<void> {
    // 🔥 修复：安全访问签名信息，兼容不同的数据结构
    let targetSignature = 'unknown';
    try {
      if (txn?.transaction?.signatures && Array.isArray(txn.transaction.signatures) && txn.transaction.signatures.length > 0) {
        // V0格式：signatures数组
        targetSignature = txn.transaction.signatures[0];
      } else if (txn?.transaction?.signature) {
        // GRPC格式：单个signature对象
        if (txn.transaction.signature.type === 'Buffer' && txn.transaction.signature.data) {
          targetSignature = Buffer.from(txn.transaction.signature.data).toString('base64');
        } else if (typeof txn.transaction.signature === 'string') {
          targetSignature = txn.transaction.signature;
        }
      } else if (txn?.signature) {
        // 其他格式：顶级signature字段
        targetSignature = txn.signature;
      }
    } catch (error) {
      console.log('⚠️ 无法提取交易签名:', error.message);
      targetSignature = 'signature_extraction_failed';
    }
    
    const slot = data.transaction?.slot || data.slot || 'unknown';
    const timestamp = new Date();

    if (process.env.DEBUG_TRANSACTION_PARSING === 'true') {
      console.log(`\n🎯 发现目标钱包交易:`);
      console.log(`   钱包: ${TARGET_WALLET.slice(0, 8)}...`);
      console.log(`   交易ID: ${targetSignature}`);
      console.log(`   时间: ${timestamp.toLocaleString()}`);
    }

    try {
      // 提取token信息
      const tokenInfo = await this.extractTokenInfoFromTransaction(parsedTxn);
      if (tokenInfo) {
        for (const token of tokenInfo.tokens) {
          // 过滤小额交易
          if (token.solAmount && token.solAmount < MIN_TRANSACTION_SOL) {
            const tokenDisplay = await this.formatTokenDisplay(token.address);
            console.log(`🚫 跳过小额交易Token: ${tokenDisplay} (${token.solAmount.toFixed(6)} SOL)`);
            continue;
          }

          const tokenDisplay = await this.formatTokenDisplay(token.address);
          console.log(`   🪙 Token: ${tokenDisplay} (${token.action})`);
          console.log(`   💰 金额: ${token.solAmount?.toFixed(4) || 'N/A'} SOL`);
          console.log(`   🔢 Token数量: ${token.tokenAmount?.toFixed(2) || 'N/A'} tokens`);
          
          // 记录真实价格数据
          if (token.solAmount && token.tokenAmount && token.tokenAmount > 0) {
            this.recordRealPrice(
              token.address,
              token.solAmount,
              token.tokenAmount,
              token.action as 'buy' | 'sell',
              'stream'
            );
          }
          
          // 如果是已监控的token，特别标记
          if (this.tokenInstances.has(token.address)) {
            console.log(`   🔥 这是已监控的Token - 将更新交易历史并立即AI预测！`);
          } else {
            // 🔥 新Token发现 - 通知主控制器
            console.log(`   🆕 发现新Token，准备添加到监控`);
            if (this.tokenDiscoveryCallback) {
              try {
                await this.tokenDiscoveryCallback({
                  address: token.address,
                  action: token.action,
                  solAmount: token.solAmount
                });
              } catch (error) {
                console.error(`❌ Token发现回调失败:`, error);
              }
            }
          }
          
          // 🔥 从交易数据中提取并存储Pool信息（在此省略具体实现）
          
          // 🍃 目标钱包交易：仅收集token信息，不触发AI预测
          console.log(`📊 目标钱包交易数据已收集: ${tokenDisplay} ${token.action} ${token.solAmount?.toFixed(4)}SOL`);
        }
      }

      if (process.env.DEBUG_TRANSACTION_PARSING === 'true') {
        console.log(`   🔗 交易链接: https://translator.shyft.to/tx/${targetSignature}`);
        console.log('-'.repeat(80));
      }
    } catch (error) {
      console.error('处理目标钱包交易时发生错误:', error);
    }
  }

  // 简化的Token显示格式化函数
  private async formatTokenDisplay(tokenAddress: string): Promise<string> {
    return `${tokenAddress.slice(0, 8)}...${tokenAddress.slice(-4)}`;
  }

  // 🔥 处理监控Token的其他钱包交易 - 事件驱动预测
  private async processMonitoredTokenTransaction(
    txn: any, 
    data: any, 
    monitoredTokensInvolved: string[], 
    parsedTxn: any
  ): Promise<void> {
    console.log(`🎯 处理监控token交易 - 涉及${monitoredTokensInvolved.length}个token`);
    
    for (const tokenAddress of monitoredTokensInvolved) {
      console.log(`📊 更新token交易数据: ${tokenAddress.slice(0, 8)}...`);
      
      try {
        // 从交易中提取详细信息
        const extractedInfo = await this.extractTokenInfoFromTransaction(parsedTxn);
        if (extractedInfo && extractedInfo.tokens) {
          for (const tokenInfo of extractedInfo.tokens) {
            if (tokenInfo.address === tokenAddress && tokenInfo.solAmount && tokenInfo.tokenAmount) {
              // 记录价格数据
              this.recordRealPrice(
                tokenAddress,
                tokenInfo.solAmount,
                tokenInfo.tokenAmount,
                tokenInfo.action as 'buy' | 'sell',
                'stream'
              );

              // 🔥 关键修复：调用processTokenTransaction传递市场交易数据给subscriptionManager
              await this.processTokenTransaction(tokenAddress, {
                signature: 'stream_data',
                timestamp: new Date(),
                data: {
                  action: tokenInfo.action === 'buy' ? 1 : tokenInfo.action === 'sell' ? 0 : 2,
                  sol_amount: tokenInfo.solAmount,
                  usd_amount: tokenInfo.usdAmount || 0,
                  is_target_wallet: false,
                  wallet: 'market_participant',
                  block_number: txn.slot || 0
                }
              });
              
              console.log(`📈 记录交易数据: ${tokenInfo.action} ${tokenInfo.tokenAmount.toFixed(2)} tokens, ${tokenInfo.solAmount.toFixed(4)} SOL`);
            }
          }
        }
        
        // 🔥 关键：立即触发事件驱动的AI预测！（与原文件完全一致）
        await this.triggerEventDrivenPrediction(tokenAddress);
        
      } catch (error) {
        console.error(`❌ 处理监控token交易失败 ${tokenAddress.slice(0, 8)}...:`, error);
      }
    }
  }

  // 🔥 新增：事件驱动的AI预测触发 - 与原文件的makePredictionForToken调用一致
  private async triggerEventDrivenPrediction(tokenAddress: string): Promise<void> {
    try {
      console.log(`🔮 触发事件驱动预测: ${tokenAddress.slice(0, 8)}...`);
      
      // 调用事件发射器，通过回调机制触发 PredictionEngine 的 makePredictionForToken 方法
      await this.emitPredictionEvent(tokenAddress);
      
    } catch (error) {
      console.error(`❌ 触发预测失败 ${tokenAddress.slice(0, 8)}...:`, error);
    }
  }

  // 🔥 设置预测回调函数
  public setPredictionCallback(callback: (tokenAddress: string) => Promise<void>): void {
    this.predictionCallback = callback;
    console.log('✅ 预测回调函数已设置');
  }

  // 🔥 新增：Token发现回调函数
  private tokenDiscoveryCallback?: (tokenInfo: { address: string; action: string; solAmount?: number }) => Promise<void>;

  public setTokenDiscoveryCallback(callback: (tokenInfo: { address: string; action: string; solAmount?: number }) => Promise<void>): void {
    this.tokenDiscoveryCallback = callback;
    console.log('✅ Token发现回调函数已设置');
  }

  // 预测事件发射器 - 调用回调函数触发预测
  private async emitPredictionEvent(tokenAddress: string): Promise<void> {
    console.log(`📢 发送预测事件: ${tokenAddress.slice(0, 8)}...`);
    
    if (this.predictionCallback) {
      try {
        await this.predictionCallback(tokenAddress);
      } catch (error) {
        console.error(`❌ 预测回调执行失败 ${tokenAddress.slice(0, 8)}...:`, error);
      }
    } else {
      console.log(`⚠️ 预测回调未设置，跳过预测: ${tokenAddress.slice(0, 8)}...`);
    }
  }

  // 处理我的钱包交易
  private async processMyWalletTransaction(txn: any, data: any, parsedTxn: any): Promise<void> {
    console.log(`💼 处理我的钱包交易`);
    
    try {
      // 从交易中提取Token信息
      const extractedInfo = await this.extractTokenInfoFromTransaction(parsedTxn);
      if (!extractedInfo || !extractedInfo.tokens || extractedInfo.tokens.length === 0) {
        console.log(`⚠️ 无法从我的钱包交易中提取Token信息`);
        return;
      }

      for (const tokenInfo of extractedInfo.tokens) {
        console.log(`📊 我的钱包交易Token: ${tokenInfo.address.slice(0, 8)}... ${tokenInfo.action} ${tokenInfo.solAmount?.toFixed(4)}SOL`);
        
        if (tokenInfo.solAmount && tokenInfo.solAmount >= MIN_TRANSACTION_SOL) {
          // 记录价格数据
          if (tokenInfo.solAmount && tokenInfo.tokenAmount) {
            this.recordRealPrice(
              tokenInfo.address,
              tokenInfo.solAmount,
              tokenInfo.tokenAmount,
              tokenInfo.action as 'buy' | 'sell',
              'own_trade'
            );
          }

          // 更新实际Token余额
          const instance = this.tokenInstances.get(tokenInfo.address);
          if (instance) {
            console.log(`🔄 更新Token余额: ${tokenInfo.address.slice(0, 8)}...`);
            // 这里应该调用更新余额的方法
            // await this.updateRealTokenBalance(tokenInfo.address, instance);
          }
        }
      }

    } catch (error) {
      console.error('❌ 处理我的钱包交易失败:', error);
    }
  }

  // 解码Pump.fun交易
  private decodePumpFunTxn(tx: VersionedTransactionResponse): any {
    try {
      if (!tx || !tx.meta || !tx.transaction) {
        return null;
      }

      // 简化的解码逻辑
      const instructions = [];
      const logs = tx.meta.logMessages || [];
      
      // 检查是否包含Pump.fun相关日志
      const hasPumpFunLogs = logs.some(log => 
        log.includes('Pump') || 
        log.includes('AMM') ||
        log.toLowerCase().includes('swap')
      );

      if (!hasPumpFunLogs) {
        return null;
      }

      return {
        signature: tx.transaction.signatures?.[0] || '',
        instructions: instructions,
        logs: logs,
        events: [],
        accounts: tx.meta.loadedAddresses || { writable: [], readonly: [] }
      };

    } catch (error) {
      console.error('❌ 解码交易失败:', error);
      return null;
    }
  }

  // 🔥 修复：检查钱包是否参与交易 - 兼容GRPC数据格式
  private async isWalletInvolvedInTransaction(tx: any, wallet: string): Promise<boolean> {
    try {
      console.log(`🔍 检查钱包参与交易: ${wallet.slice(0, 8)}...`);
      
      if (!tx) {
        console.log('❌ 交易对象为空');
        return false;
      }

      // 🔥 安全提取账户列表，兼容GRPC嵌套结构
      let accountKeys: string[] = [];
      
      try {
        // 尝试GRPC嵌套格式
        if (tx?.transaction?.transaction?.message?.accountKeys) {
          console.log('📨 使用GRPC嵌套格式提取账户');
          accountKeys = tx.transaction.transaction.message.accountKeys.map((key: any) => {
            if (typeof key === 'string') return key;
            if (key?.type === 'Buffer' && key?.data) {
              const bs58 = require('bs58');
              return bs58.encode(Buffer.from(key.data));
            }
            if (key?.toBase58) return key.toBase58();
            return key.toString();
          });
        } 
        // 尝试标准格式
        else if (tx?.transaction?.message?.staticAccountKeys) {
          console.log('📨 使用标准staticAccountKeys格式');
          accountKeys = tx.transaction.message.staticAccountKeys.map((key: any) => {
            if (typeof key === 'string') return key;
            if (key?.toBase58) return key.toBase58();
            return key.toString();
          });
        }
        // 尝试Legacy格式
        else if (tx?.transaction?.message?.accountKeys) {
          console.log('📨 使用Legacy accountKeys格式');
          accountKeys = tx.transaction.message.accountKeys.map((key: any) => {
            if (typeof key === 'string') return key;
            if (key?.toBase58) return key.toBase58();
            return key.toString();
          });
        }
        
        console.log(`📋 提取到${accountKeys.length}个账户地址`);
        
      } catch (keyError) {
        console.log('⚠️ 提取账户列表失败:', keyError.message);
        accountKeys = [];
      }

      const isInvolved = accountKeys.includes(wallet);
      console.log(`🎯 钱包 ${wallet.slice(0, 8)}... 是否参与: ${isInvolved ? '✅ 是' : '❌ 否'}`);
      
      return isInvolved;

    } catch (error) {
      console.error('❌ 检查钱包参与交易失败:', error.message);
      return false;
    }
  }

  // 提取交易中的Token信息
  private async extractTokenInfoFromTransaction(parsedTxn: any): Promise<{ tokens: Array<{ address: string, action: 'buy' | 'sell' | 'unknown', solAmount?: number, tokenAmount?: number, usdAmount?: number }> } | null> {
    const tokens: Array<{ address: string, action: 'buy' | 'sell' | 'unknown', solAmount?: number, tokenAmount?: number, usdAmount?: number }> = [];

    try {
      // 🔥 修复：检查parsedTxn.events是否已经是提取后的token信息
      if (parsedTxn.events && Array.isArray(parsedTxn.events)) {
        console.log(`📋 解析事件 (${parsedTxn.events.length}个):`);
        
        // 🔥 检查events格式：是原始事件还是已提取的token信息
        const firstEvent = parsedTxn.events[0];
        if (firstEvent && typeof firstEvent.address === 'string') {
          // 🔥 已提取的token信息格式 - 直接使用StreamHandler的提取结果
          console.log(`✅ 检测到已提取的token信息格式`);
          for (const tokenInfo of parsedTxn.events) {
            console.log(`   🪙 Token: ${tokenInfo.address?.slice(0, 8)}... (${tokenInfo.action})`);
            console.log(`   💰 金额: ${tokenInfo.solAmount?.toFixed(4) || 'N/A'} SOL`);
            
            // 过滤小额交易
            if (tokenInfo.solAmount && tokenInfo.solAmount < MIN_TRANSACTION_SOL) {
              console.log(`🚫 过滤小额交易: ${tokenInfo.solAmount.toFixed(6)} SOL < ${MIN_TRANSACTION_SOL} SOL`);
              continue;
            }
            
            if (tokenInfo.address && tokenInfo.solAmount && tokenInfo.solAmount > 0) {
              tokens.push({
                address: tokenInfo.address,
                action: tokenInfo.action || 'unknown',
                solAmount: tokenInfo.solAmount,
                tokenAmount: tokenInfo.tokenAmount || 0,
                usdAmount: tokenInfo.usdAmount || 0
              });
              console.log(`✅ 添加交易: ${tokenInfo.address.slice(0, 8)}... ${tokenInfo.action} ${tokenInfo.solAmount.toFixed(4)}SOL`);
            }
          }
        } else {
          // 🔥 原始事件格式 - 使用原来的解析逻辑
          console.log(`🔧 检测到原始事件格式，开始解析...`);
          for (const event of parsedTxn.events) {
            console.log(`   事件: ${event.name || '未知'}`);
            
            // 🔥 修复：安全检查事件数据结构
            if (!event || !event.data) {
              console.log(`   ⚠️ 事件数据不完整，跳过处理`);
              continue;
            }
          
          let action: 'buy' | 'sell' | 'unknown' = 'unknown';
          let solAmount = 0;
          let tokenAmount = 0;
          let tokenAddress = 'unknown';

          try {
            if (event.name === 'BuyEvent') {
              action = 'buy';
              solAmount = parseFloat(event.data.quote_amount_in || 0) / 1e9;
              tokenAmount = parseFloat(event.data.base_amount_out || 0) / 1e6;
            } else if (event.name === 'SellEvent') {
              action = 'sell';
              solAmount = parseFloat(event.data.quote_amount_out || 0) / 1e9;
              tokenAmount = parseFloat(event.data.base_amount_in || 0) / 1e6;
            }

            // 获取token地址 - 安全访问
            if (event.data && event.data.base_mint) {
              tokenAddress = event.data.base_mint;
              console.log(`   事件base_mint: ${tokenAddress}`);
            }
          } catch (eventError) {
            console.log(`   ❌ 解析事件数据失败: ${eventError.message}`);
            continue;
          }

          // 过滤小额交易
          if (solAmount < MIN_TRANSACTION_SOL) {
            console.log(`🚫 过滤小额交易: ${solAmount.toFixed(6)} SOL < ${MIN_TRANSACTION_SOL} SOL (${action})`);
            continue;
          }

          // 只有当我们有有效的token地址和交易数据时才添加
          if (tokenAddress !== 'unknown' && solAmount > 0) {
            // 🔥 修复：使用真实的SOL价格
            let usdAmount = 0;
            try {
              // 这里应该有价格服务，暂时使用固定值
              usdAmount = solAmount * 240; // 假设SOL价格为$240
            } catch (error) {
              console.log(`⚠️ 无法获取实时SOL价格`);
              usdAmount = solAmount * 240;
            }

            tokens.push({
              address: tokenAddress,
              action,
              solAmount,
              tokenAmount,
              usdAmount
            });
            
            console.log(`✅ 添加交易: ${tokenAddress.slice(0, 8)}... ${action} ${solAmount.toFixed(4)}SOL`);
          } else {
            console.log(`⚠️ 跳过无效交易: address=${tokenAddress}, solAmount=${solAmount}`);
          }
          }
        }
      }

      return { tokens };

    } catch (error) {
      console.error('❌ 提取Token信息失败:', error);
      return null;
    }
  }

  // 获取涉及的监控Token
  private getInvolvedMonitoredTokens(parsedTxn: any): string[] {
    const involvedTokens = [];
    
    try {
      // 从解析的交易中提取Token地址
      // 这里需要根据实际的交易格式来实现
      if (parsedTxn && parsedTxn.accounts) {
        // 简化实现
        for (const [tokenAddress] of this.tokenInstances) {
          if (parsedTxn.logs && parsedTxn.logs.some((log: string) => log.includes(tokenAddress.slice(0, 8)))) {
            involvedTokens.push(tokenAddress);
          }
        }
      }
    } catch (error) {
      console.error('❌ 获取涉及Token失败:', error);
    }

    return involvedTokens;
  }

  // 更新Token持仓
  private async updateTokenHolding(instance: TokenTradingInstance, token: any, signature: string): Promise<void> {
    try {
      if (token.action === 'buy') {
        instance.currentHolding = {
          amount: token.tokenAmount || 0,
          buyPrice: token.solAmount ? token.solAmount / token.tokenAmount : 0,
          buyTime: new Date(),
          buySolAmount: token.solAmount || 0,
          buyGasFee: 0,
          buyPlatformFee: 0,
          totalBuyCost: token.solAmount || 0,
          bundleId: signature
        };
      } else if (token.action === 'sell' && instance.currentHolding) {
        // 清空持仓
        instance.currentHolding = null;
      }
    } catch (error) {
      console.error('❌ 更新持仓失败:', error);
    }
  }

  // 记录交易历史
  private recordTradeHistory(instance: TokenTradingInstance, token: any, parsedTxn: any): void {
    try {
      instance.tradingHistory.push({
        type: token.action,
        timestamp: new Date(),
        tokenAmount: token.tokenAmount || 0,
        solAmount: token.solAmount || 0,
        price: token.solAmount && token.tokenAmount ? token.solAmount / token.tokenAmount : 0,
        prediction: 0, // 需要从预测结果获取
        bundleId: parsedTxn.signature
      });

      // 保持历史记录大小
      if (instance.tradingHistory.length > 100) {
        instance.tradingHistory = instance.tradingHistory.slice(-100);
      }
    } catch (error) {
      console.error('❌ 记录交易历史失败:', error);
    }
  }

  // 更新Token活动
  private updateTokenActivity(instance: TokenTradingInstance, parsedTxn: any): void {
    try {
      instance.activity.lastSeen = new Date();
      instance.activity.transactionCount += 1;
      instance.lastProcessedTransactionTime = new Date();
    } catch (error) {
      console.error('❌ 更新Token活动失败:', error);
    }
  }

  // 从事件更新池子余额
  private updatePoolBalanceFromEvents(instance: TokenTradingInstance, parsedTxn: any): void {
    try {
      if (!instance.realPoolData) {
        return;
      }

      // 从事件中提取余额变化
      // 这里需要根据实际的事件格式来实现
      console.log(`📊 更新池子余额: ${instance.address.slice(0, 8)}...`);
      
    } catch (error) {
      console.error('❌ 更新池子余额失败:', error);
    }
  }

  // 处理Token交易
  private async processTokenTransaction(tokenAddress: string, transaction: { signature: string; timestamp: Date; data: any }): Promise<void> {
    try {
      const instance = this.tokenInstances.get(tokenAddress);
      if (!instance) {
        return;
      }

      // 更新最后处理时间
      instance.lastProcessedTransactionTime = transaction.timestamp;
      
      console.log(`🔄 处理Token交易: ${tokenAddress.slice(0, 8)}... ${transaction.signature.slice(0, 8)}...`);

    } catch (error) {
      console.error('❌ 处理Token交易失败:', error);
    }
  }

  // 记录实时价格
  private recordRealPrice(
    tokenAddress: string, 
    solAmount: number, 
    tokenAmount: number, 
    action: 'buy' | 'sell',
    source: 'stream' | 'own_trade' = 'stream'
  ): void {
    try {
      if (!this.realPriceData.has(tokenAddress)) {
        this.realPriceData.set(tokenAddress, []);
      }

      const priceData = this.realPriceData.get(tokenAddress)!;
      const price = tokenAmount > 0 ? solAmount / tokenAmount : 0;

      priceData.push({
        price,
        timestamp: new Date(),
        solAmount,
        tokenAmount,
        action,
        source
      });

      // 保持价格历史大小
      if (priceData.length > 1000) {
        priceData.splice(0, priceData.length - 1000);
      }

      console.log(`💰 记录价格: ${tokenAddress.slice(0, 8)}... ${action} 价格=${price.toFixed(8)} SOL`);

    } catch (error) {
      console.error('❌ 记录价格失败:', error);
    }
  }

  // 获取Token价格历史
  getPriceHistory(tokenAddress: string): PriceHistoryEntry[] {
    return this.realPriceData.get(tokenAddress) || [];
  }

  // 获取最新价格
  getLatestPrice(tokenAddress: string): number | undefined {
    const priceData = this.realPriceData.get(tokenAddress);
    if (!priceData || priceData.length === 0) {
      return undefined;
    }
    return priceData[priceData.length - 1].price;
  }

  // 清理处理队列
  clearProcessingQueue(): void {
    this.processingQueue = [];
    console.log('🧹 交易处理队列已清理');
  }

  // 获取处理统计
  getProcessingStats(): {
    queueSize: number;
    isProcessing: boolean;
    totalTokens: number;
    tokensWithPriceData: number;
  } {
    return {
      queueSize: this.processingQueue.length,
      isProcessing: this.isProcessing,
      totalTokens: this.tokenInstances.size,
      tokensWithPriceData: this.realPriceData.size
    };
  }
} 