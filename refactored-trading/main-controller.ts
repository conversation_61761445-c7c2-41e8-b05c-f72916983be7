import { PublicKey } from "@solana/web3.js";
import { TokenTradingInstance, TokenStats, CurrentPosition, SellOnlyBufferInfo, SystemStats } from "./types-and-interfaces";
import { PoolManager } from "./pool-manager";
import { TradeExecutor } from "./trade-executor";
import { TokenManager } from "./token-manager";
import { PredictionEngine } from "./prediction-engine";
import { StreamHandler } from "./stream-handler";
import { TransactionProcessor } from "./transaction-processor";
import { 
  REAL_TRADING_CONFIG, 
  MAX_MONITORED_TOKENS, 
  TARGET_WALLET, 
  MY_WALLET, 
  STATUS_UPDATE_INTERVAL,
  PREDICTION_INTERVAL_MS,
  TOKEN_STATS_UPDATE_INTERVAL,
  connection,
  wallet,
  RPC_ENDPOINT
} from "./trading-constants";

/**
 * 主控制器 - 协调所有模块，提供与原始RealTradingWithTracker相同的接口
 */
export class MainController {
  // 核心模块
  private poolManager: PoolManager;
  private tradeExecutor: TradeExecutor;
  private tokenManager: TokenManager;
  private predictionEngine: PredictionEngine;
  private streamHandler: StreamHandler;
  private transactionProcessor: TransactionProcessor;
  
  // 🔥 简化版本的组件占位符
  private subscriptionManager: any = null;
  private riskManager: any = null;
  private telegramNotifier: any = null;
  private priceService: any = null;

  // 状态管理
  public isRunning: boolean = false;
  private statusMonitorTimer: NodeJS.Timeout | null = null;
  private predictionTimer: NodeJS.Timeout | null = null;
  private tokenStatsTimer: NodeJS.Timeout | null = null;
  private blockhashUpdateTimer: NodeJS.Timeout | null = null;

  // 数据存储
  private tokenInstances: Map<string, TokenTradingInstance> = new Map();
  private stats = {
    totalTrades: 0,
    successfulTrades: 0,
    totalPnL: 0,
    dailyPnL: 0,
    startTime: new Date(),
    lastResetTime: new Date()
  };

  // 🔥 添加原版本的关键状态
  private currentBlockhash: string = '';
  private lastBlockhashUpdate: Date = new Date(0);
  private currentTokenSubscriptions: Set<string> = new Set();

  constructor() {
    console.log('🎯 初始化主控制器...');
    
    // 🔥 按照原版本的顺序初始化所有组件
    this.initializeComponents();
    this.setupEventHandlers();
    
    console.log('✅ 主控制器初始化完成');
  }

  // 🔥 按照原版本的顺序初始化组件
  private initializeComponents(): void {
    console.log('🔧 初始化核心组件...');
    
    // 初始化核心模块
    this.poolManager = new PoolManager(this.tokenInstances);
    this.tradeExecutor = new TradeExecutor(this.poolManager);
    this.tokenManager = new TokenManager();
    this.predictionEngine = new PredictionEngine(this.tokenInstances);
    this.streamHandler = new StreamHandler();
    this.transactionProcessor = new TransactionProcessor(this.tokenInstances, this.poolManager);
    
    // 🔥 建立事件驱动预测的通信机制
    this.setupEventDrivenPrediction();
    
    // 🔥 初始化简化版本的组件（提供必要的方法）
    console.log('🛡️ 初始化风险管理器...');
    this.riskManager = { 
      initialized: true,
      start: async () => console.log('✅ 风险管理器启动完成'),
      stop: async () => console.log('✅ 风险管理器已停止')
    };
    
    console.log('📱 初始化Telegram通知服务...');
    this.telegramNotifier = { 
      initialized: true,
      start: async () => console.log('✅ Telegram通知服务启动完成'),
      stop: async () => console.log('✅ Telegram通知服务已停止')
    };
    
    console.log('💰 初始化价格服务...');
    this.priceService = { 
      initialized: true,
      start: async () => console.log('✅ 价格服务启动完成'),
      stop: async () => console.log('✅ 价格服务已停止')
    };
    
    console.log('🎯 初始化动态订阅管理器...');
    this.subscriptionManager = { 
      initialized: true,
      start: async () => console.log('✅ 动态订阅管理器启动完成'),
      stop: async () => console.log('✅ 动态订阅管理器已停止')
    };
    
    console.log('✅ 所有组件初始化完成');
  }

  // 🔥 建立事件驱动预测的通信机制
  private setupEventDrivenPrediction(): void {
    console.log('🔗 建立事件驱动预测通信机制...');
    
    // 为 TransactionProcessor 提供预测触发回调
    if (this.transactionProcessor && typeof (this.transactionProcessor as any).setPredictionCallback === 'function') {
      (this.transactionProcessor as any).setPredictionCallback(async (tokenAddress: string) => {
        await this.triggerImmediatePrediction(tokenAddress);
      });
      console.log('✅ 预测回调函数已设置');
    }

    // 🔥 为 TransactionProcessor 提供Token发现回调
    if (this.transactionProcessor && typeof (this.transactionProcessor as any).setTokenDiscoveryCallback === 'function') {
      (this.transactionProcessor as any).setTokenDiscoveryCallback(async (tokenInfo: { address: string; action: string; solAmount?: number }) => {
        await this.processTargetWalletDiscovery(tokenInfo);
      });
      console.log('✅ Token发现回调函数已设置');
    }
    
    console.log('✅ 事件驱动预测通信机制已建立');
  }

  // 🔥 立即触发AI预测 - 与原文件的makePredictionForToken完全一致
  private async triggerImmediatePrediction(tokenAddress: string): Promise<void> {
    try {
      console.log(`🔮 立即执行AI预测: ${tokenAddress.slice(0, 8)}...`);
      
      // 调用 PredictionEngine 的预测方法
      if (this.predictionEngine && typeof (this.predictionEngine as any).makePredictionForToken === 'function') {
        await (this.predictionEngine as any).makePredictionForToken(tokenAddress);
      } else {
        console.log(`⚠️ PredictionEngine 未准备好，跳过预测: ${tokenAddress.slice(0, 8)}...`);
      }
      
    } catch (error) {
      console.error(`❌ 立即预测失败 ${tokenAddress.slice(0, 8)}...:`, error);
    }
  }

  // 设置事件处理器
  private setupEventHandlers(): void {
    // 流处理器事件
    this.streamHandler.onTransaction((event) => {
      this.transactionProcessor.processStreamTransaction(event);
    });

    // 🔥 添加目标Token交易事件处理器
    this.streamHandler.onTargetTokenTransaction((event) => {
      this.transactionProcessor.processStreamTransaction(event);
    });

  }

  // 启动系统
  public async start(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ 系统已在运行中');
      return;
    }

    try {
      console.log('🚀 启动重构版交易系统...');
      
      // 🔥 Step 1: 启动Telegram服务
      console.log('📱 启动Telegram通知服务...');
      await this.startTelegramService();
      
      // 🔥 Step 2: 启动动态订阅管理器
      console.log('🎯 启动动态订阅管理器...');
      await this.subscriptionManager.start();
      console.log('✅ 动态订阅管理器已启动，开始监控钱包:', TARGET_WALLET);
      
      // 🔥 Step 3: 启动流处理器
      console.log('🌊 正在启动流处理器...');
      console.log('🚀 开始连接到Yellowstone gRPC流...');
      console.log('🎯 目标钱包:', TARGET_WALLET);
      await this.streamHandler.start();
      console.log('✅ 流处理器启动完成');
      
      // 🔥 Step 4: 设置事件驱动预测机制 - 重要！
      console.log('🔗 设置事件驱动预测机制...');
      this.setupEventDrivenPrediction();
      console.log('✅ 事件驱动预测机制已设置');
      
      // 🔥 Step 5: 启动定时器
      console.log('⏰ 正在启动定时器...');
      this.startTimers();
      console.log('✅ 定时器启动完成');
      
      // 🔥 Step 6: 启动状态监控
      console.log('📊 状态监控已启动 (间隔: 30秒)');
      
      this.isRunning = true;
      console.log('✅ 重构版交易系统启动成功');

    } catch (error) {
      console.error('❌ 启动系统失败:', error);
      console.error('详细错误信息:', error.stack);
      throw error;
    }
  }

  // 🔥 启动Telegram服务 - 按照原版本流程
  private async startTelegramService(): Promise<void> {
    try {
      console.log('📱 =====================================');
      console.log('📱 测试Telegram连接...');
      
      // 启动Telegram通知服务
      await this.telegramNotifier.start();
      
      console.log('✅ Telegram连接成功！开始发送启动通知...');
      console.log('📱 =====================================');
      
    } catch (error) {
      console.error('❌ Telegram服务启动失败:', error);
      // 不抛出错误，允许系统继续运行
    }
  }

  // 🔥 更新Blockhash - 按照原版本
  private async updateBlockhash(): Promise<void> {
    try {
      const latestBlockhash = await connection.getLatestBlockhash();
      this.currentBlockhash = latestBlockhash.blockhash;
      this.lastBlockhashUpdate = new Date();
      
      const timeStr = this.lastBlockhashUpdate.toLocaleTimeString();
      console.log(`🔄 Blockhash已更新: ${this.currentBlockhash.slice(0, 8)}... (${timeStr})`);
      
    } catch (error) {
      console.error('❌ 更新Blockhash失败:', error);
    }
  }

  // 停止系统
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      console.log('⚠️ 系统未在运行');
      return;
    }

    try {
      console.log('🛑 停止重构版交易系统...');
      
      this.isRunning = false;

      // 停止定时器
      this.stopTimers();

      // 停止订阅管理器
      if (this.subscriptionManager) {
        await this.subscriptionManager.stop();
      }

      // 停止流处理器
      await this.streamHandler.stop();

      // 停止Telegram服务
      if (this.telegramNotifier) {
        await this.telegramNotifier.stop();
      }

      // 清理资源
      this.cleanup();

      console.log('✅ 重构版交易系统已停止');

    } catch (error) {
      console.error('❌ 停止系统时出错:', error);
    }
  }

  // 启动定时器
  private startTimers(): void {
    console.log('⏰ 启动定时器...');
    
    // 🔥 Blockhash更新定时器 - 按照原版本每2秒更新
    this.blockhashUpdateTimer = setInterval(async () => {
      await this.updateBlockhash();
    }, 5000);
    console.log('🔄 Blockhash更新定时器已启动 (间隔: 5秒)');
    
    // 状态监控定时器
    this.statusMonitorTimer = setInterval(() => {
      this.printStatus();
    }, STATUS_UPDATE_INTERVAL);
    console.log(`📊 状态监控定时器已启动 (间隔: ${STATUS_UPDATE_INTERVAL/1000}秒)`);

    // 🔥 移除周期性预测 - 改为事件驱动预测
    console.log(`🔮 预测系统已启动 (事件驱动模式)`);

    // Token统计定时器
    this.tokenStatsTimer = setInterval(() => {
      this.updateTokenStats();
    }, TOKEN_STATS_UPDATE_INTERVAL);
    console.log(`📈 Token统计定时器已启动 (间隔: ${TOKEN_STATS_UPDATE_INTERVAL/1000}秒)`);
    
    // 🔥 立即执行一次Blockhash更新
    this.updateBlockhash();
    
    // 立即执行一次状态打印
    setTimeout(() => {
      console.log('🚀 首次状态检查...');
      this.printStatus();
    }, 5000); // 5秒后执行首次状态检查
  }

  // 停止定时器
  private stopTimers(): void {
    if (this.blockhashUpdateTimer) {
      clearInterval(this.blockhashUpdateTimer);
      this.blockhashUpdateTimer = null;
    }

    if (this.statusMonitorTimer) {
      clearInterval(this.statusMonitorTimer);
      this.statusMonitorTimer = null;
    }

    if (this.predictionTimer) {
      clearInterval(this.predictionTimer);
      this.predictionTimer = null;
    }

    if (this.tokenStatsTimer) {
      clearInterval(this.tokenStatsTimer);
      this.tokenStatsTimer = null;
    }
  }

  // 运行预测周期
  private async runPredictionCycle(): Promise<void> {
    try {
      const activeTokens = Array.from(this.tokenInstances.keys()).filter(address => {
        const instance = this.tokenInstances.get(address);
        return instance?.isActive;
      });

      if (activeTokens.length === 0) {
        return;
      }

      console.log(`🔮 开始预测周期: ${activeTokens.length} 个活跃Token`);

      // 批量预测
      const predictions = await this.predictionEngine.batchPredict(activeTokens);

      // 处理预测结果
      for (const [tokenAddress, prediction] of predictions) {
        const instance = this.tokenInstances.get(tokenAddress);
        if (!instance) continue;

        // 制定交易决策
        const decision = this.predictionEngine.makeTradeDecision(
          tokenAddress,
          prediction.buyPrediction,
          prediction.sellPrediction,
          instance
        );

        // 执行交易决策
        await this.executeTradeDecision(tokenAddress, decision, instance);
      }

    } catch (error) {
      console.error('❌ 预测周期执行失败:', error);
    }
  }

  // 执行交易决策
  private async executeTradeDecision(
    tokenAddress: string,
    decision: any,
    instance: TokenTradingInstance
  ): Promise<void> {
    try {
      if (decision.action === 'buy') {
        console.log(`💰 执行买入: ${tokenAddress.slice(0, 8)}... 信心度=${(decision.confidence * 100).toFixed(1)}%`);
        
        const result = await this.tradeExecutor.executeRealBuy(tokenAddress, instance, decision.confidence);
        
        if (result.success) {
          this.stats.totalTrades++;
          this.stats.successfulTrades++;
          console.log(`✅ 买入成功: ${result.bundleId}`);
        } else {
          console.log(`❌ 买入失败: ${result.error}`);
        }

      } else if (decision.action === 'sell') {
        console.log(`💸 执行卖出: ${tokenAddress.slice(0, 8)}... 信心度=${(decision.confidence * 100).toFixed(1)}%`);
        
        const tokenAmount = instance.currentHolding?.amount || 0;
        const result = await this.tradeExecutor.executeRealSell(tokenAddress, tokenAmount, decision.confidence);
        
        if (result.success) {
          this.stats.totalTrades++;
          this.stats.successfulTrades++;
          console.log(`✅ 卖出成功: ${result.bundleId}`);
        } else {
          console.log(`❌ 卖出失败: ${result.error}`);
        }

      } else {
        console.log(`⏸️ 持有: ${tokenAddress.slice(0, 8)}... ${decision.reason}`);
      }

    } catch (error) {
      console.error(`❌ 执行交易决策失败 ${tokenAddress.slice(0, 8)}...:`, error);
    }
  }

  // 更新Token统计
  private updateTokenStats(): void {
    try {
      for (const [tokenAddress, instance] of this.tokenInstances) {
        // 更新统计信息
        instance.stats.totalTrades = instance.tradingHistory.length;
        
        // 计算成功交易数
        const successfulTrades = instance.tradingHistory.filter(trade => 
          trade.type === 'sell' && trade.solAmount > 0
        ).length;
        instance.stats.successfulTrades = successfulTrades;

        // 计算总PnL
        let totalPnL = 0;
        for (const trade of instance.tradingHistory) {
          if (trade.type === 'sell') {
            totalPnL += trade.solAmount;
          } else if (trade.type === 'buy') {
            totalPnL -= trade.solAmount;
          }
        }
        instance.stats.totalPnL = totalPnL;

        // 更新当前持仓
        instance.stats.currentPosition = instance.currentHolding?.amount || 0;
      }

    } catch (error) {
      console.error('❌ 更新Token统计失败:', error);
    }
  }

  // 打印状态
  private printStatus(): void {
    try {
      const activeTokens = Array.from(this.tokenInstances.values()).filter(instance => instance.isActive);
      const totalPositions = activeTokens.filter(instance => instance.currentHolding).length;
      
      console.log('\n' + '='.repeat(80));
      console.log('📊 重构版交易系统状态报告');
      console.log('='.repeat(80));
      console.log(`🕐 运行时间: ${this.getRunningTime()}`);
      console.log(`🎯 监控Token: ${activeTokens.length}/${MAX_MONITORED_TOKENS}`);
      console.log(`💼 当前持仓: ${totalPositions}`);
      console.log(`📈 总交易数: ${this.stats.totalTrades}`);
      console.log(`✅ 成功交易: ${this.stats.successfulTrades}`);
      console.log(`💰 总PnL: ${this.stats.totalPnL.toFixed(6)} SOL`);
      console.log(`📊 胜率: ${this.stats.totalTrades > 0 ? ((this.stats.successfulTrades / this.stats.totalTrades) * 100).toFixed(1) : 0}%`);
      
      // 流状态
      const streamStatus = this.streamHandler.getStatus();
      console.log(`🌊 流状态: ${streamStatus.isRunning ? '✅ 运行中' : '❌ 停止'}`);
      console.log(`🔗 GRPC客户端: ${streamStatus.hasClient ? '✅ 已连接' : '❌ 未连接'}`);
      console.log(`📡 订阅数: ${streamStatus.subscriptionCount}`);
      
      // 数据统计
      const dataStats = streamStatus.dataStats;
      console.log(`📊 数据接收: 总计${dataStats.totalReceived} | 交易:${dataStats.transactionCount} | 账户:${dataStats.accountCount}`);
      
      if (dataStats.firstReceivedTime) {
        const dataRunningTime = Math.floor((Date.now() - dataStats.firstReceivedTime.getTime()) / 1000);
        const dataRate = dataRunningTime > 0 ? (dataStats.totalReceived / dataRunningTime).toFixed(1) : '0';
        console.log(`📈 数据速率: ${dataRate} 包/秒 | 最后接收: ${Math.floor((Date.now() - dataStats.lastReceivedTime.getTime()) / 1000)}秒前`);
      } else {
        console.log(`⚠️ 尚未接收到任何GRPC数据 - 可能连接有问题`);
      }
      
      // 处理器状态
      const processingStats = this.transactionProcessor.getProcessingStats();
      console.log(`⚙️ 处理队列: ${processingStats.queueSize} | 处理中: ${processingStats.isProcessing ? '是' : '否'}`);
      
      console.log('='.repeat(80) + '\n');

    } catch (error) {
      console.error('❌ 打印状态失败:', error);
    }
  }

  // 获取运行时间
  private getRunningTime(): string {
    const now = new Date();
    const diff = now.getTime() - this.stats.startTime.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  }

  // 清理资源
  private cleanup(): void {
    try {
      // 清理预测锁
      this.predictionEngine.clearPredictionLocks();
      
      // 清理处理队列
      this.transactionProcessor.clearProcessingQueue();
      
      // 清理池子缓存
      this.poolManager.clearPoolCache();
      
      console.log('🧹 资源清理完成');

    } catch (error) {
      console.error('❌ 清理资源时出错:', error);
    }
  }

  // 公共API方法 - 与原始类保持兼容

  public getActiveTokens(): string[] {
    return Array.from(this.tokenInstances.keys()).filter(address => {
      const instance = this.tokenInstances.get(address);
      return instance?.isActive;
    });
  }

  public getTokenStats(): TokenStats[] {
    return Array.from(this.tokenInstances.entries()).map(([tokenAddress, instance]) => ({
      tokenAddress,
      totalTrades: instance.stats.totalTrades,
      totalPnL: instance.stats.totalPnL,
      transactionCount: instance.activity.transactionCount,
      winRate: instance.stats.totalTrades > 0 ? (instance.stats.successfulTrades / instance.stats.totalTrades) : 0
    }));
  }

  public getCurrentPositions(): CurrentPosition[] {
    return Array.from(this.tokenInstances.entries())
      .filter(([_, instance]) => instance.currentHolding)
      .map(([tokenAddress, instance]) => {
        const holding = instance.currentHolding!;
        const currentPrice = this.transactionProcessor.getLatestPrice(tokenAddress) || holding.buyPrice;
        const pnl = (currentPrice - holding.buyPrice) * holding.amount;
        const holdingTime = Date.now() - holding.buyTime.getTime();

        return {
          tokenAddress,
          position: holding.amount,
          pnl,
          buyPrice: holding.buyPrice,
          currentPrice,
          holdingTime: Math.floor(holdingTime / 1000) // 秒
        };
      });
  }

  public getSellOnlyBufferInfo(): SellOnlyBufferInfo[] {
    return this.tokenManager.getSellOnlyBufferInfo();
  }

  public isSystemRunning(): boolean {
    return this.isRunning;
  }

  // 诊断和维护方法
  public async diagnoseSellIssues(): Promise<void> {
    console.log('🔍 诊断卖出问题...');
    
    const positions = this.getCurrentPositions();
    for (const position of positions) {
      console.log(`📊 持仓分析: ${position.tokenAddress.slice(0, 8)}...`);
      console.log(`   数量: ${position.position}`);
      console.log(`   买入价: ${position.buyPrice.toFixed(8)} SOL`);
      console.log(`   当前价: ${position.currentPrice.toFixed(8)} SOL`);
      console.log(`   PnL: ${position.pnl.toFixed(6)} SOL`);
      console.log(`   持有时间: ${Math.floor(position.holdingTime / 60)}分钟`);
    }
  }

  public emergencyUnlockTradingStates(): void {
    console.log('🚨 紧急解锁交易状态...');
    
    for (const instance of this.tokenInstances.values()) {
      instance.isTrading = false;
    }
    
    this.predictionEngine.clearPredictionLocks();
    console.log('✅ 交易状态已解锁');
  }

  public checkAndCleanStuckTrades(): void {
    console.log('🧹 检查并清理卡住的交易...');
    
    const now = Date.now();
    for (const [tokenAddress, instance] of this.tokenInstances) {
      // 检查卡住的交易状态
      if (instance.isTrading) {
        const timeSinceLastTrade = now - instance.lastTradeTime.getTime();
        if (timeSinceLastTrade > 300000) { // 5分钟
          console.log(`🔧 清理卡住的交易状态: ${tokenAddress.slice(0, 8)}...`);
          instance.isTrading = false;
        }
      }

      // 清理过期的挂起交易
      for (const [signature, pendingTx] of instance.pendingTransactions) {
        const timeSincePending = now - pendingTx.timestamp.getTime();
        if (timeSincePending > 600000) { // 10分钟
          console.log(`🗑️ 清理过期挂起交易: ${signature.slice(0, 8)}...`);
          instance.pendingTransactions.delete(signature);
        }
      }
    }
  }

  public async validateAndFixPositionConsistency(): Promise<void> {
    console.log('🔍 验证和修复持仓一致性...');
    
    for (const [tokenAddress, instance] of this.tokenInstances) {
      if (instance.currentHolding) {
        try {
          // 获取实际Token余额
          const actualBalance = await this.tradeExecutor.getRealTokenBalance(tokenAddress);
          
          if (Math.abs(actualBalance - instance.currentHolding.amount) > 0.001) {
            console.log(`⚠️ 持仓不一致: ${tokenAddress.slice(0, 8)}...`);
            console.log(`   记录: ${instance.currentHolding.amount}`);
            console.log(`   实际: ${actualBalance}`);
            
            // 修复持仓
            if (actualBalance > 0.001) {
              instance.currentHolding.amount = actualBalance;
              console.log(`✅ 已修复持仓数量`);
            } else {
              instance.currentHolding = null;
              console.log(`✅ 已清空持仓记录`);
            }
          }
        } catch (error) {
          console.error(`❌ 验证持仓失败 ${tokenAddress.slice(0, 8)}...:`, error);
        }
      }
    }
  }

  // 🔥 新增：动态Token监控功能

  // 添加Token到监控并创建动态订阅
  public async addTokenToMonitoring(tokenAddress: string): Promise<void> {
    console.log(`📡 添加Token到监控: ${tokenAddress.slice(0, 8)}...`);
    
    try {
      // 检查是否已在监控
      if (this.tokenInstances.has(tokenAddress)) {
        console.log(`⚠️ Token已在监控列表: ${tokenAddress.slice(0, 8)}...`);
        return;
      }
      
      // 添加到Token管理器
      const instance = await this.tokenManager.createTokenInstance(tokenAddress);
      this.tokenInstances.set(tokenAddress, instance);
      console.log(`✅ Token已添加到管理器: ${tokenAddress.slice(0, 8)}...`);
      
      // 更新流处理器的Token订阅（这会触发动态订阅）
      const allTokens = Array.from(this.tokenInstances.keys());
      this.streamHandler.updateTokenSubscriptions(allTokens);
      console.log(`📡 已更新流处理器Token订阅列表`);
      
    } catch (error) {
      console.error(`❌ 添加Token监控失败: ${tokenAddress.slice(0, 8)}...`, error);
    }
  }

  // 批量添加Token到监控
  public async addTokensToMonitoring(tokenAddresses: string[]): Promise<void> {
    console.log(`📡 批量添加${tokenAddresses.length}个Token到监控`);
    
    const newTokens: string[] = [];
    
    for (const tokenAddress of tokenAddresses) {
      if (!this.tokenInstances.has(tokenAddress)) {
        try {
          const instance = await this.tokenManager.createTokenInstance(tokenAddress);
          this.tokenInstances.set(tokenAddress, instance);
          newTokens.push(tokenAddress);
          console.log(`✅ 新Token已添加: ${tokenAddress.slice(0, 8)}...`);
        } catch (error) {
          console.error(`❌ 添加Token失败: ${tokenAddress.slice(0, 8)}...`, error);
        }
      }
    }
    
    if (newTokens.length > 0) {
      // 更新流处理器的Token订阅
      const allTokens = Array.from(this.tokenInstances.keys());
      this.streamHandler.updateTokenSubscriptions(allTokens);
      console.log(`📡 已更新流处理器Token订阅列表 (+${newTokens.length}个新Token)`);
    }
  }

  // 从目标钱包交易中发现新Token并添加监控
  public async processTargetWalletDiscovery(tokenInfo: { address: string; action: string; solAmount?: number }): Promise<void> {
    console.log(`🎯 处理目标钱包发现的Token: ${tokenInfo.address.slice(0, 8)}... ${tokenInfo.action}`);
    
    try {
      // 过滤小额交易
      if (tokenInfo.solAmount && tokenInfo.solAmount < 0.0005) {
        console.log(`🚫 跳过小额交易Token: ${tokenInfo.address.slice(0, 8)}... (${tokenInfo.solAmount.toFixed(6)} SOL)`);
        return;
      }
      
      // 检查监控Token数量限制
      const currentMonitoredCount = this.tokenManager.getActiveTokens().length;
      if (currentMonitoredCount >= 30) {
        console.log(`⚠️ 已达到Token监控上限 (30)，暂时不添加新Token`);
        return;
      }
      
      // 🔥 重要：检查Token是否已在监控中
      if (this.tokenInstances.has(tokenInfo.address)) {
        console.log(`📊 Token已在监控中，更新活动数据: ${tokenInfo.address.slice(0, 8)}...`);
        return;
      }
      
      // 添加到监控
      await this.addTokenToMonitoring(tokenInfo.address);
      
      console.log(`🆕 目标钱包Token已添加到监控: ${tokenInfo.address.slice(0, 8)}... ${tokenInfo.action} ${tokenInfo.solAmount?.toFixed(4)}SOL`);
      
      // 🔥 新增：立即触发一次Token订阅更新，确保新Token能被订阅
      console.log(`📡 立即更新Token订阅以包含新发现的Token`);
      const allTokens = Array.from(this.tokenInstances.keys());
      this.streamHandler.updateTokenSubscriptions(allTokens);
      
    } catch (error) {
      console.error(`❌ 处理目标钱包Token发现失败:`, error);
    }
  }
}

// 🔥 主程序启动函数
async function main() {
  console.log('🚀 启动重构版交易系统...');
  
  try {
    const controller = new MainController();
    
    // 设置优雅关闭
    process.on('SIGINT', async () => {
      console.log('🛑 收到SIGINT信号，开始优雅关闭...');
      await controller.stop();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      console.log('🛑 收到SIGTERM信号，开始优雅关闭...');
      await controller.stop();
      process.exit(0);
    });

    // 启动系统
    await controller.start();
    
    console.log('✅ 重构版交易系统已启动并运行');
    
  } catch (error) {
    console.error('❌ 启动重构版交易系统失败:', error);
    process.exit(1);
  }
}

// 启动主程序
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 主程序异常:', error);
    process.exit(1);
  });
}