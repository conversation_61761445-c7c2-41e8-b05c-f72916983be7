import Client, { CommitmentLevel } from '@triton-one/yellowstone-grpc';
import { PublicKey } from '@solana/web3.js';
import { SubscribeRequest, StreamEvent } from './types-and-interfaces';
import { 
  REAL_TRADING_CONFIG, 
  TARGET_WALLET, 
  MY_WALLET, 
  TXN_FORMATTER,
  PUMP_FUN_IX_PARSER,
  PUMP_FUN_EVENT_PARSER,
  PUMP_FUN_AMM_PROGRAM_ID,
  MIN_TRANSACTION_SOL
} from './trading-constants';
import { bnLayoutFormatter } from '../utils/bn-layout-formatter';

// 删除重复的常量定义，使用从 trading-constants 导入的版本

// 删除重复的接口定义，使用从 types-and-interfaces 导入的版本

/**
 * 流处理器 - 负责GRPC流的处理和实时数据管理
 */
export class StreamHandler {
  private grpcClient: Client | null = null;
  private currentStream: any = null;
  private isRunning: boolean = false;
  private currentSubscription: Set<string> = new Set();
  private subscriptionUpdateTimer: NodeJS.Timeout | null = null;
  private streamTimeout: NodeJS.Timeout | null = null;
  private eventHandlers: Map<string, (event: StreamEvent) => void> = new Map();
  
  // 添加数据统计
  private dataStats = {
    totalReceived: 0,
    transactionCount: 0,
    accountCount: 0,
    slotCount: 0,
    blockCount: 0,
    lastReceivedTime: new Date(),
    firstReceivedTime: null as Date | null,
    lastReceivedTxTime: 0
  };

  // 添加Token订阅管理 - 与原文件一致
  private currentTokenSubscriptions: Set<string> = new Set();

  constructor() {
    this.setupEventHandlers();
    this.currentSubscription.add(TARGET_WALLET);
    this.currentSubscription.add(MY_WALLET);
  }

  // 设置事件处理器
  private setupEventHandlers(): void {
    this.eventHandlers.set('transaction', this.handleTransactionEvent.bind(this));
  }

  // 启动流处理
  async start(): Promise<void> {
    console.log('🌊 启动GRPC流处理器...');
    
    try {
      // 创建GRPC客户端
      this.grpcClient = new Client(
        REAL_TRADING_CONFIG.grpc?.endpoint!, 
        REAL_TRADING_CONFIG.grpc?.token, 
        undefined
      );
      console.log('🔗 GRPC客户端已创建');
      console.log(`📡 连接端点: ${REAL_TRADING_CONFIG.grpc?.endpoint}`);
      console.log(`🔑 使用Token: ${REAL_TRADING_CONFIG.grpc?.token ? '已配置' : '未配置'}`);
      
      // 启动流处理循环
      this.isRunning = true;
      
      // 创建初始订阅请求
      const subscribeRequest = this.createInitialSubscribeRequest();
      
      // 启动流处理 - 不要await，让它在后台运行
      this.handleStream(this.grpcClient, subscribeRequest).catch(error => {
        console.error('❌ 流处理过程中发生错误:', error);
      });
      
      console.log('✅ GRPC流处理器启动成功');
    } catch (error) {
      console.error('❌ GRPC流处理器启动失败:', error);
      throw error;
    }
  }

  // 停止流处理
  async stop(): Promise<void> {
    this.isRunning = false;
    
    console.log('🛑 停止GRPC流处理器...');
    
    // 清理定时器
    if (this.subscriptionUpdateTimer) {
      clearInterval(this.subscriptionUpdateTimer);
      this.subscriptionUpdateTimer = null;
    }
    
    if (this.streamTimeout) {
      clearTimeout(this.streamTimeout);
      this.streamTimeout = null;
    }
    
    // 关闭流
    if (this.currentStream) {
      try {
        this.currentStream.end();
      } catch (error) {
        console.log('🔧 流关闭时出现预期错误:', error.message);
      }
      this.currentStream = null;
    }
    
    // 关闭客户端
    if (this.grpcClient) {
      try {
        // 注意：原文件中可能没有close方法，这里需要根据实际情况调整
        console.log('🔧 GRPC客户端已清理');
      } catch (error) {
        console.log('🔧 客户端关闭时出现预期错误:', error.message);
      }
      this.grpcClient = null;
    }
    
    console.log('✅ GRPC流处理器已停止');
  }

  // 处理流连接 - 完全按照原文件的逻辑
  private async handleStream(client: Client, args: SubscribeRequest): Promise<void> {
    const maxRetries = 3;
    let currentRetry = 0;
    let consecutiveErrors = 0;
    const MAX_CONSECUTIVE_ERRORS = 5;
    
    while (this.isRunning && currentRetry < maxRetries) {
      let stream: any = null;
      let statusInterval: NodeJS.Timeout | null = null;
      let streamTimeout: NodeJS.Timeout | null = null;
      let connectTimeout: NodeJS.Timeout | null = null;
      
      try {
        console.log(`🔗 尝试建立GRPC流连接... (尝试 ${currentRetry + 1}/${maxRetries})`);
        
        // 🔥 添加连接超时保护
        connectTimeout = setTimeout(() => {
          console.log('⏰ GRPC连接超时 (30秒)');
          if (stream) {
            try {
              stream.destroy();
            } catch (destroyError) {
              console.log('强制关闭连接时发生错误:', destroyError);
            }
          }
        }, 30000);
        
        // 按照原文件的方式创建流
        stream = await client.subscribe();
        
        if (connectTimeout) {
          clearTimeout(connectTimeout);
          connectTimeout = null;
        }
        
        this.currentStream = stream;
        console.log(`✅ GRPC流连接已建立`);
        currentRetry = 0; // 重置重试计数
        consecutiveErrors = 0; // 重置连续错误计数

        // 添加状态监控变量
        let lastReceivedTxTime = 0;
        let connectionStartTime = Date.now();

        const streamClosed = new Promise<void>((resolve, reject) => {
          let hasResolved = false;
          
          const resolveOnce = (reason?: string) => {
            if (!hasResolved) {
              hasResolved = true;
              console.log(`🔚 GRPC流关闭: ${reason || '正常结束'}`);
              
              // 清理所有定时器
              if (statusInterval) clearInterval(statusInterval);
              if (streamTimeout) clearTimeout(streamTimeout);
              if (connectTimeout) clearTimeout(connectTimeout);
              
              resolve();
            }
          };
          
          const rejectOnce = (error: any) => {
            if (!hasResolved) {
              hasResolved = true;
              console.log(`❌ GRPC流错误:`, error?.message || error);
              
              // 清理所有定时器
              if (statusInterval) clearInterval(statusInterval);
              if (streamTimeout) clearTimeout(streamTimeout);
              if (connectTimeout) clearTimeout(connectTimeout);
              
              // 检查是否是网络相关错误
              if (this.isNetworkRelatedError(error)) {
                console.log('🔄 检测到网络错误，将尝试重连...');
                consecutiveErrors++;
                if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
                  console.log(`❌ 连续错误达到上限 (${MAX_CONSECUTIVE_ERRORS}次)，延长等待时间`);
                  setTimeout(() => resolve(), 30000); // 30秒后重试
                } else {
                  resolve(); // 作为正常结束处理，触发重连
                }
              } else {
                reject(error);
              }
            }
          };

          // 🔥 增强的错误处理
          stream.on("error", (error: any) => {
            console.log("❌ GRPC Stream ERROR:", error?.message || error);
            rejectOnce(error);
            
            // 🔥 安全关闭流
            try {
              if (stream && typeof stream.end === 'function') {
                stream.end();
              }
            } catch (endError) {
              console.log('流关闭时发生错误:', endError);
            }
          });
          
          stream.on("end", () => {
            resolveOnce('流结束');
          });
          
          stream.on("close", () => {
            resolveOnce('流关闭');
          });
          
          // 🔥 添加流超时保护（5分钟无数据触发重连）
          const resetStreamTimeout = () => {
            if (streamTimeout) clearTimeout(streamTimeout);
            streamTimeout = setTimeout(() => {
              if (!hasResolved) {
                console.log('⏰ GRPC流超时 (5分钟无数据)');
                resolveOnce('流超时');
                try {
                  if (stream && typeof stream.destroy === 'function') {
                    stream.destroy();
                  }
                } catch (destroyError) {
                  console.log('强制关闭流时发生错误:', destroyError);
                }
              }
            }, 300000); // 5分钟超时
          };
          
          resetStreamTimeout(); // 初始设置超时
        });

        // 🔥 Enhanced status display with comprehensive error handling - 完全按照原文件
        statusInterval = setInterval(() => {
          if (!this.isRunning) {
            if (statusInterval) clearInterval(statusInterval);
            return;
          }
          
          try {
            const now = Date.now();
            const secondsSinceLastTx = (now - lastReceivedTxTime) / 1000;
            const secondsSinceStart = (now - connectionStartTime) / 1000;
            
            console.log(`\n📊 GRPC连接状态 (${Math.floor(secondsSinceStart / 60)}分${Math.floor(secondsSinceStart % 60)}秒):`);
            console.log(`   📡 连接状态: ${this.currentStream ? '✅ 已连接' : '❌ 未连接'}`);
            console.log(`   📦 数据接收: 总计${this.dataStats.totalReceived} | 交易:${this.dataStats.transactionCount} | 账户:${this.dataStats.accountCount}`);
            console.log(`   🔄 重试次数: ${currentRetry}/${maxRetries}`);
            console.log(`   ⚠️ 连续错误: ${consecutiveErrors}/${MAX_CONSECUTIVE_ERRORS}`);
            
            if (this.dataStats.totalReceived === 0) {
              console.log('   ⚠️ 尚未接收到任何GRPC数据 - 可能连接有问题');
            }
            
            if (lastReceivedTxTime > 0) {
              console.log(`   📡 上次交易: ${secondsSinceLastTx.toFixed(0)} 秒前`);
              
              // 🔥 检查是否长时间无数据
              if (secondsSinceLastTx > 180) { // 3分钟无数据
                console.log('   ⚠️ 长时间无交易数据，连接可能有问题');
              }
            } else {
              console.log(`   📡 交易状态: ⚠️ 无交易数据`);
            }
            
          } catch (statusError) {
            console.error('状态显示错误:', statusError);
          }
          
        }, 30000); // 🔥 30秒间隔，替代STATUS_UPDATE_INTERVAL

                  // 🔥 Enhanced data processing with comprehensive error handling - 完全按照原文件
        stream.on("data", async (data: any) => {
          if (!this.isRunning) return;
          
          // 🔥 改进：根据数据类型选择性输出日志
          if (data?.ping) {
            console.log('📡 GRPC Ping数据');
            return; // ping消息直接返回，不处理
          } else {
            // console.log('🔥 GRPC流数据 (完整):', JSON.stringify(data, null, 2));
          }
          
          try {
            this.dataStats.totalReceived++;
            this.dataStats.lastReceivedTime = new Date();
            
            if (!this.dataStats.firstReceivedTime) {
              this.dataStats.firstReceivedTime = new Date();
            }
            
           
            
            if (data?.transaction) {
              lastReceivedTxTime = Date.now();
              this.dataStats.transactionCount++;
              
              // 静默计数
              
              const txn = data.transaction; // 🔥 简化，直接使用原始数据
              
              // 🔥 修复：添加安全检查，确保交易数据完整
              if (!txn || !txn.transaction) {
                console.log('⚠️ 交易数据不完整，跳过处理');
                return;
              }
              
              // 🔥 新增：检查交易数据结构
              console.log('🔍 交易数据结构检查:');
              console.log(`   txn存在: ${!!txn}`);
              console.log(`   txn.transaction存在: ${!!txn.transaction}`);
              console.log(`   txn.transaction.message存在: ${!!txn?.transaction?.message}`);
              if (txn?.transaction?.message) {
                console.log(`   可用字段: ${Object.keys(txn.transaction.message).join(', ')}`);
              }
              
              // 🔥 根据新的订阅结构处理不同类型的交易
              const filters = data.filters || [];
              
              // 🔥 确定filter类型用于解析
              let filterType = 'unknown';
              if (filters.includes('targetWalletTransaction')) {
                filterType = 'targetWalletTransaction';
              } else if (filters.includes('myWalletTransaction')) {
                filterType = 'myWalletTransaction';
              } else if (filters.includes('targetTokenTransaction')) {
                filterType = 'targetTokenTransaction';
              }
              
              const parsedTxn = this.decodePumpFunSwapTxn(txn, filterType, data);
              
              // 🔥 按照原文件：如果不是Pump.fun交易就返回，但数据统计已经更新了
              if (!parsedTxn) {
                return;
              }
              
              // 检查交易是否涉及目标钱包或监控的token
              const isTargetWalletInvolved = await this.isWalletInvolvedInTransaction(txn, TARGET_WALLET);
              const isMyWalletInvolved = await this.isWalletInvolvedInTransaction(txn, MY_WALLET);
              
              // 🔥 修复：安全提取账户列表
              let accountKeys: string[] = [];
              try {
                if (txn?.transaction?.message?.staticAccountKeys) {
                  // V0 消息格式
                  accountKeys = txn.transaction.message.staticAccountKeys.map((key: any) => {
                    if (typeof key === 'string') return key;
                    if (key?.toBase58) return key.toBase58();
                    return key.toString();
                  });
                } else if (txn?.transaction?.message?.accountKeys) {
                  // Legacy 消息格式
                  accountKeys = txn.transaction.message.accountKeys.map((key: any) => {
                    if (typeof key === 'string') return key;
                    if (key?.toBase58) return key.toBase58();
                    return key.toString();
                  });
                }
              } catch (keyError) {
                console.log('⚠️ 提取账户列表时出错:', keyError.message);
                accountKeys = [];
              }
              
              const monitoredTokensInvolved = Array.from(this.currentTokenSubscriptions).filter(token => 
                accountKeys.includes(token)
              );
              
              // 🔥 根据不同的订阅类型分别处理
              if (filters.includes('targetWalletTransaction')) {
                console.log('🎯 目标钱包交易数据');
                this.emitEvent('transaction', {
                  type: 'targetWalletTransaction',
                  data: {
                    txn,
                    parsedTxn,
                    originalData: data,
                    isTargetWalletInvolved,
                    isMyWalletInvolved,
                    monitoredTokensInvolved
                  },
                  timestamp: new Date()
                });
              } else if (filters.includes('myWalletTransaction')) {
                console.log('💼 我的钱包交易数据');
                this.emitEvent('transaction', {
                  type: 'myWalletTransaction', 
                  data: {
                    txn,
                    parsedTxn,
                    originalData: data,
                    isTargetWalletInvolved,
                    isMyWalletInvolved,
                    monitoredTokensInvolved
                  },
                  timestamp: new Date()
                });
              } else if (filters.includes('targetTokenTransaction')) {
                console.log('🪙 目标Token交易数据');
                this.emitEvent('targetTokenTransaction', {
                  type: 'targetTokenTransaction',
                  data: {
                    txn,
                    parsedTxn,
                    originalData: data,
                    isTargetWalletInvolved,
                    isMyWalletInvolved,
                    monitoredTokensInvolved,
                    tokenFilters: filters
                  },
                  timestamp: new Date()
                });
              } else {
                // 兼容旧的pumpFun命名或其他情况
                console.log('🔄 通用交易数据');
                this.emitEvent('transaction', {
                  type: 'transaction',
                  data: {
                    txn,
                    parsedTxn,
                    originalData: data,
                    isTargetWalletInvolved,
                    isMyWalletInvolved,
                    monitoredTokensInvolved
                  },
                  timestamp: new Date()
                });
              }
            }
            
          
            
          } catch (dataError) {
            console.error('数据处理错误:', dataError);
            
            // 如果是网络错误，不要影响流的稳定性
            if (!this.isNetworkRelatedError(dataError)) {
              consecutiveErrors++;
              if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
                console.log(`❌ 数据处理连续错误达到上限，重启流`);
                try {
                  if (stream && typeof stream.destroy === 'function') {
                    stream.destroy();
                  }
                } catch (destroyError) {
                  console.log('强制关闭流时发生错误:', destroyError);
                }
              }
            }
          }
        });

        // 发送订阅请求
        try {
          await new Promise<void>((resolve, reject) => {
            stream.write(args, (err: any) => {
              if (err === null || err === undefined) {
                console.log('✅ 订阅请求已发送');
                resolve();
              } else {
                console.error('❌ 发送订阅请求失败:', err);
                reject(err);
              }
            });
          });
        } catch (writeError) {
          console.error('❌ 写入订阅请求失败:', writeError);
          throw writeError;
        }

        // 等待流关闭或错误
        await streamClosed;
        
        // 正常结束，等待一下再重连
        if (this.isRunning) {
          console.log('🔄 流已关闭，2秒后重连...');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
      } catch (error) {
        console.error(`❌ GRPC流处理失败 (尝试 ${currentRetry + 1}/${maxRetries}):`, error);
        
        // 清理所有定时器和资源
        if (statusInterval) clearInterval(statusInterval);
        if (streamTimeout) clearTimeout(streamTimeout);
        if (connectTimeout) clearTimeout(connectTimeout);
        
        // 尝试安全关闭流
        if (stream) {
          try {
            if (typeof stream.end === 'function') {
              stream.end();
            }
            if (typeof stream.destroy === 'function') {
              stream.destroy();
            }
          } catch (closeError) {
            console.log('关闭流时发生错误:', closeError);
          }
        }
        
        currentRetry++;
        
        if (currentRetry >= maxRetries) {
          console.error('❌ 所有GRPC重试都失败了');
          
          // 如果是网络错误，不要完全失败，而是等待更长时间后重试
          if (this.isNetworkRelatedError(error)) {
            console.log('🔄 网络错误，等待30秒后重新开始...');
            await new Promise(resolve => setTimeout(resolve, 30000));
            currentRetry = 0; // 重置重试计数，继续尝试
            continue;
          } else {
            throw error; // 非网络错误，抛出异常
          }
        }
        
        // 计算退避延迟（指数退避，但有上限）
        const delay = Math.min(1000 * Math.pow(2, currentRetry - 1), 15000);
        console.log(`⏳ 等待 ${delay/1000} 秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } finally {
        // 确保清理所有资源
        if (statusInterval) clearInterval(statusInterval);
        if (streamTimeout) clearTimeout(streamTimeout);
        if (connectTimeout) clearTimeout(connectTimeout);
        this.currentStream = null;
      }
    }
    
    if (currentRetry >= maxRetries) {
      throw new Error('GRPC连接重试次数已用完');
    }
  }

  // 🔥 重新实现的Pump.fun Swap交易解析器 - 基于real-trading-with-tracker.ts中的已验证方法
  private decodePumpFunSwapTxn(tx: any, filterType?: string, originalData?: any) {
    console.log('🔥 开始解码Pump.fun Swap交易');
    console.log(`🎯 Filter类型: ${filterType || '未指定'}`);
    
    // 🔥 调试：显示输入数据结构
    console.log('🔍 输入数据结构分析:');
    console.log(`   tx存在: ${!!tx}`);
    console.log(`   tx.transaction存在: ${!!tx?.transaction}`);
    console.log(`   tx.transaction.message存在: ${!!tx?.transaction?.message}`);
    console.log(`   tx.meta存在: ${!!tx?.meta}`);
    
    if (tx?.transaction) {
      console.log(`   tx.transaction字段: ${Object.keys(tx.transaction).join(', ')}`);
    }
    
    if (tx.meta?.err) {
      console.log('❌ 交易失败，跳过解析');
      return null;
    }

    try {
      // 🔥 首先使用TXN_FORMATTER格式化交易数据
      console.log('🔧 开始格式化交易数据...');
      
      // 🔥 修复：重新构造TXN_FORMATTER期望的数据结构
      // TXN_FORMATTER期望的格式: { slot: number, transaction: { transaction: {...}, meta: {...} } }
      // 但我们从GRPC得到的tx已经是data.transaction，需要重新包装
      
      // 从原始data中获取slot信息
      const slot = originalData?.slot || tx.slot || 0; // 优先从originalData获取slot
      
      // 重新构造TXN_FORMATTER期望的数据结构
      const formatterInput = {
        slot: slot,
        transaction: tx // tx本身就是原始的transaction数据
      };
      
      console.log('🔧 重新构造数据结构用于TXN_FORMATTER');
      console.log(`   slot: ${slot}`);
      console.log(`   transaction存在: ${!!tx}`);
      
      const formattedTxn = TXN_FORMATTER.formTransactionFromJson(
        formatterInput,
        Date.now()
      );
      console.log('✅ 交易数据格式化完成');
      
      // 🔥 调试：检查格式化后的数据结构
      console.log('🔍 格式化后数据结构检查:');
      console.log(`   formattedTxn存在: ${!!formattedTxn}`);
      console.log(`   formattedTxn.transaction存在: ${!!formattedTxn?.transaction}`);
      console.log(`   formattedTxn.transaction.message存在: ${!!formattedTxn?.transaction?.message}`);
      console.log(`   formattedTxn.meta存在: ${!!formattedTxn?.meta}`);
      
      if (formattedTxn?.transaction) {
        console.log(`   formattedTxn.transaction字段: ${Object.keys(formattedTxn.transaction).join(', ')}`);
      }
      
      if (formattedTxn?.transaction?.message) {
        console.log(`   formattedTxn.transaction.message字段: ${Object.keys(formattedTxn.transaction.message).join(', ')}`);
      }

      // 🔥 使用与real-trading-with-tracker.ts相同的解析方法
      const originalConsole = console;
      const silentConsole = {
        log: () => {},
        error: () => {},
        warn: () => {},
        info: () => {},
        debug: () => {}
      };
      
      // 静默PUMP_FUN解析器的输出
      console = silentConsole as any;
      
      let parsedTxn;
      try {
        const paredIxs = PUMP_FUN_IX_PARSER.parseTransactionData(
          formattedTxn.transaction.message,
          formattedTxn.meta.loadedAddresses
        );
        
        // 过滤掉ComputeBudget程序的指令
        const filteredIxs = paredIxs.filter(ix => 
          !ix.programId.toString().includes('ComputeBudget')
        );

        const pumpFunIxs = filteredIxs.filter((ix) =>
          ix.programId.equals(PUMP_FUN_AMM_PROGRAM_ID)
        );

        if (pumpFunIxs.length === 0) {
          console = originalConsole;
          console.log('❌ 未找到Pump.fun指令');
          return null;
        }
        
                 const events = PUMP_FUN_EVENT_PARSER.parseEvent(formattedTxn);
         parsedTxn = { instructions: pumpFunIxs, events };
         bnLayoutFormatter(parsedTxn);
        
      } finally {
        console = originalConsole;
      }

      // 🔥 提取token信息 - 使用与real-trading-with-tracker.ts相同的逻辑
      const extractedInfo = this.extractTokenInfoFromParsedTxn(parsedTxn, filterType);
      
      if (!extractedInfo || extractedInfo.tokens.length === 0) {
        console.log('⚠️ 未解析到任何Pump.fun事件');
        
        // 🔥 调试：显示解析失败的详细信息
        console.log('🔍 解析失败调试信息:');
        console.log(`   指令数量: ${parsedTxn.instructions?.length || 0}`);
        console.log(`   事件数量: ${parsedTxn.events?.length || 0}`);
        
        if (parsedTxn.instructions && parsedTxn.instructions.length > 0) {
          console.log('   指令详情:');
          parsedTxn.instructions.forEach((ix: any, index: number) => {
            console.log(`     ${index + 1}. ${ix.name || '未知指令'}`);
          });
        }
        
        if (parsedTxn.events && parsedTxn.events.length > 0) {
          console.log('   事件详情:');
          parsedTxn.events.forEach((event: any, index: number) => {
            console.log(`     ${index + 1}. ${event.name || '未知事件'}`);
          });
        }
        
        return null;
      }

      // 🔥 根据filter类型打印不同详细程度的信息
      this.printEventsByFilterType(extractedInfo.tokens, filterType);
      
      // 返回结果
      const result = { 
        instructions: parsedTxn.instructions,
        events: extractedInfo.tokens,
        filterType: filterType
      };
      
      console.log('✅ Pump.fun交易解析完成');
      return result;
      
    } catch (error) {
      console.error('❌ 解析Pump.fun Swap交易失败:', error);
      return null;
    }
  }

  // 🔥 新增：基于real-trading-with-tracker.ts的extractTokenInfoFromTransaction方法
  private extractTokenInfoFromParsedTxn(parsedTxn: any, filterType?: string): { tokens: Array<{ address: string, action: 'buy' | 'sell' | 'unknown', solAmount?: number, tokenAmount?: number, usdAmount?: number }> } | null {
    const tokens: Array<{ address: string, action: 'buy' | 'sell' | 'unknown', solAmount?: number, tokenAmount?: number, usdAmount?: number }> = [];

    try {
      console.log('🔍 开始提取token信息...');
      
      // 从指令中提取所有token地址
      const allTokenAddresses = new Set<string>();
      const baseMints = new Set<string>();
      const quoteMints = new Set<string>();

      // 从指令中提取token地址
      if (parsedTxn.instructions && parsedTxn.instructions.length > 0) {
        for (const ix of parsedTxn.instructions) {
          console.log(`🔍 检查指令: ${ix.name || '未知'}`);
          
          if (ix.accounts) {
            // 查找base_mint
            const baseMintAccounts = ix.accounts.filter((a: any) => a.name === 'base_mint');
            for (const account of baseMintAccounts) {
              console.log(`   ✅ base_mint: ${account.pubkey}`);
              baseMints.add(account.pubkey);
              allTokenAddresses.add(account.pubkey);
            }
            
            // 查找quote_mint
            const quoteMintAccounts = ix.accounts.filter((a: any) => a.name === 'quote_mint');
            for (const account of quoteMintAccounts) {
              console.log(`   ✅ quote_mint: ${account.pubkey}`);
              quoteMints.add(account.pubkey);
              allTokenAddresses.add(account.pubkey);
            }

            // 查找其他可能的token相关账户
            const tokenAccounts = ix.accounts.filter((a: any) => 
              a.name && (
                a.name.includes('token') || 
                a.name.includes('mint') || 
                a.name.includes('_spl')
              ));
            
            for (const account of tokenAccounts) {
              if (account.name !== 'base_mint' && account.name !== 'quote_mint') {
                console.log(`   📋 其他token账户: ${account.name} = ${account.pubkey}`);
                allTokenAddresses.add(account.pubkey);
              }
            }
          }
        }
      }

      // 从事件中提取交易信息
      if (parsedTxn.events && Array.isArray(parsedTxn.events)) {
        console.log(`📋 解析事件 (${parsedTxn.events.length}个):`);
        
        for (const event of parsedTxn.events) {
          console.log(`   🎯 事件: ${event.name || '未知'}`);
          
          let action: 'buy' | 'sell' | 'unknown' = 'unknown';
          let solAmount = 0;
          let tokenAmount = 0;
          let tokenAddress = 'unknown';

          if (event.name === 'BuyEvent') {
            action = 'buy';
            solAmount = parseFloat(event.data.quote_amount_in || 0) / 1e9;
            tokenAmount = parseFloat(event.data.base_amount_out || 0) / 1e6;
          } else if (event.name === 'SellEvent') {
            action = 'sell';
            solAmount = parseFloat(event.data.quote_amount_out || 0) / 1e9;
            tokenAmount = parseFloat(event.data.base_amount_in || 0) / 1e6;
          }

          // 优先从事件数据获取token地址
          if (event.data.base_mint) {
            tokenAddress = event.data.base_mint;
            console.log(`   ✅ 事件base_mint: ${tokenAddress}`);
            baseMints.add(tokenAddress);
            allTokenAddresses.add(tokenAddress);
          }

          // 如果事件中没有token地址，从指令中的base_mint集合中取第一个
          if (tokenAddress === 'unknown' && baseMints.size > 0) {
            tokenAddress = Array.from(baseMints)[0];
            console.log(`   📋 使用指令base_mint: ${tokenAddress}`);
          }

          // 过滤小额交易
          if (solAmount < MIN_TRANSACTION_SOL) {
            console.log(`🚫 过滤小额交易: ${solAmount.toFixed(6)} SOL < ${MIN_TRANSACTION_SOL} SOL (${action})`);
            continue;
          }

          // 只有当我们有有效的token地址和交易数据时才添加
          if (tokenAddress !== 'unknown' && solAmount > 0) {
            // 简单的USD估算 (实际项目中应该使用实时价格)
            const usdAmount = solAmount * 240; // 假设SOL价格为240美元

            tokens.push({
              address: tokenAddress,
              action,
              solAmount,
              tokenAmount,
              usdAmount
            });
            
            console.log(`✅ 添加交易: ${tokenAddress.slice(0, 8)}... ${action} ${solAmount.toFixed(4)}SOL`);
          } else {
            console.log(`⚠️ 跳过无效交易: address=${tokenAddress}, solAmount=${solAmount}`);
          }
        }
      }

      // 如果没有从事件中得到交易，但有token地址，创建基础记录
      if (tokens.length === 0 && allTokenAddresses.size > 0) {
        console.log(`⚠️ 事件中无有效交易，但发现token地址，创建基础记录...`);
        for (const tokenAddress of allTokenAddresses) {
          // 跳过SOL地址和其他已知的系统地址
          if (tokenAddress === 'So11111111111111111111111111111111111111112' ||
              tokenAddress === '11111111111111111111111111111111' ||
              tokenAddress.length < 32) {
            console.log(`🚫 跳过系统地址: ${tokenAddress}`);
            continue;
          }
          
          // 🔥 对于targetTokenTransaction，即使没有具体交易数据也要记录
          // 这有助于Token发现和后续监控
          tokens.push({
            address: tokenAddress,
            action: 'unknown',
            solAmount: 0.001, // 设置一个最小值以通过过滤器
            tokenAmount: 0,
            usdAmount: 0.24 // 对应0.001 SOL
          });
          console.log(`📝 基础记录: ${tokenAddress.slice(0, 8)}... (用于Token发现)`);
        }
      }

      console.log(`📊 解析结果: ${tokens.length} 个token交易`);
      return tokens.length > 0 ? { tokens } : null;
      
    } catch (error) {
      console.error('❌ 提取token信息时出错:', error);
      return null;
    }
  }

  // 🔥 新增：根据filter类型打印不同详细程度的事件信息 - 适配新的事件格式
  private printEventsByFilterType(tokens: any[], filterType?: string): void {
    console.log('🎉 成功解析Pump.fun交易事件:');
    
    tokens.forEach((token, index) => {
      console.log(`📋 Token ${index + 1}: ${token.action || 'unknown'}`);
      
      if (filterType === 'targetWalletTransaction') {
        // 🎯 目标钱包：重点关注token发现和买卖识别
        console.log(`   🪙 Token地址: ${token.address}`);
        console.log(`   📊 交易类型: ${token.action}`);
        
        if (token.action === 'buy') {
          console.log(`   🛒 检测到买入行为 - 新Token发现机会`);
          console.log(`   💰 SOL投入: ${token.solAmount?.toFixed(6) || 'N/A'} SOL`);
        } else if (token.action === 'sell') {
          console.log(`   💰 检测到卖出行为 - 可能的退出信号`);
          console.log(`   💰 SOL获得: ${token.solAmount?.toFixed(6) || 'N/A'} SOL`);
        }
        
      } else if (filterType === 'myWalletTransaction') {
        // 💼 我的钱包：详细的交易数据和金额
        console.log(`   🪙 Token地址: ${token.address}`);
        
        if (token.action === 'buy') {
          console.log(`   💰 SOL投入: ${token.solAmount?.toFixed(6) || 'N/A'} SOL`);
          console.log(`   🪙 Token获得: ${token.tokenAmount?.toFixed(2) || 'N/A'} tokens`);
          console.log(`   💵 美元价值: $${token.usdAmount?.toFixed(2) || 'N/A'}`);
          console.log(`   ✅ 我的买入交易确认`);
          
        } else if (token.action === 'sell') {
          console.log(`   🪙 Token卖出: ${token.tokenAmount?.toFixed(2) || 'N/A'} tokens`);
          console.log(`   💰 SOL获得: ${token.solAmount?.toFixed(6) || 'N/A'} SOL`);
          console.log(`   💵 美元价值: $${token.usdAmount?.toFixed(2) || 'N/A'}`);
          console.log(`   ✅ 我的卖出交易确认`);
        }
        
      } else if (filterType === 'targetTokenTransaction') {
        // 🪙 目标Token：市场数据流用于预测
        console.log(`   🪙 Token地址: ${token.address}`);
        console.log(`   📊 交易类型: ${token.action}`);
        console.log(`   📈 市场活动数据点 - 用于AI预测`);
        
        if (token.action === 'buy') {
          console.log(`   📊 买入压力: ${token.solAmount?.toFixed(6) || 'N/A'} SOL`);
        } else if (token.action === 'sell') {
          console.log(`   📊 卖出压力: ${token.solAmount?.toFixed(6) || 'N/A'} SOL`);
        }
        
      } else {
        // 通用显示
        console.log(`   🪙 Token地址: ${token.address}`);
        console.log(`   📊 交易类型: ${token.action}`);
        console.log(`   💰 SOL数量: ${token.solAmount?.toFixed(6) || 'N/A'} SOL`);
        console.log(`   🪙 Token数量: ${token.tokenAmount?.toFixed(2) || 'N/A'} tokens`);
      }
    });
  }



  /**
   * 检查钱包是否参与交易 - 增强版错误处理
   */
  private async isWalletInvolvedInTransaction(tx: any, wallet: string): Promise<boolean> {
    try {
      console.log('🔍 检查钱包参与交易...');
      
      // 🔥 改进：更安全的调试输出
      if (tx) {
        console.log('📄 交易存在，检查结构...');
        if (tx.transaction) {
          console.log('📄 transaction字段存在');
          if (tx.transaction.message) {
            console.log('📄 message字段存在');
          } else {
            console.log('⚠️ message字段缺失');
          }
        } else {
          console.log('⚠️ transaction字段缺失');
        }
      } else {
        console.log('❌ 交易对象为null/undefined');
        return false;
      }
      
      // 🔥 修复：安全访问交易消息中的账户列表，兼容GRPC嵌套结构
      let accountKeys: string[] = [];
      
      // 🔥 尝试多种可能的数据结构路径
      if (tx?.transaction?.transaction?.message?.accountKeys) {
        console.log('📨 使用GRPC嵌套格式提取账户 (transaction.transaction.message.accountKeys)');
        try {
          accountKeys = tx.transaction.transaction.message.accountKeys.map((key: any) => {
            if (typeof key === 'string') return key;
            if (key?.type === 'Buffer' && key?.data) {
              // 🔥 Buffer格式需要转换为Base58地址
              try {
                const bs58 = require('bs58');
                return bs58.encode(Buffer.from(key.data));
              } catch (bufferError) {
                console.log('⚠️ Buffer转换失败:', bufferError.message);
                return key.toString();
              }
            }
            if (key?.toBase58) return key.toBase58();
            return key.toString();
          });
        } catch (mapError) {
          console.log('❌ 映射GRPC accountKeys失败:', mapError.message);
        }
      } else if (tx?.transaction?.message?.staticAccountKeys) {
        console.log('📨 使用V0消息格式提取账户 (transaction.message.staticAccountKeys)');
        try {
          accountKeys = tx.transaction.message.staticAccountKeys.map((key: any) => {
            if (typeof key === 'string') return key;
            if (key?.toBase58) return key.toBase58();
            return key.toString();
          });
        } catch (mapError) {
          console.log('❌ 映射staticAccountKeys失败:', mapError.message);
        }
      } else if (tx?.transaction?.message?.accountKeys) {
        console.log('📨 使用Legacy消息格式提取账户 (transaction.message.accountKeys)');
        try {
          accountKeys = tx.transaction.message.accountKeys.map((key: any) => {
            if (typeof key === 'string') return key;
            if (key?.toBase58) return key.toBase58();
            return key.toString();
          });
        } catch (mapError) {
          console.log('❌ 映射accountKeys失败:', mapError.message);
        }
      } else {
        console.log('⚠️ 无法找到任何账户列表字段');
        // 🔥 增强调试：显示可用的数据结构
        if (tx?.transaction?.transaction?.message) {
          console.log('🔍 GRPC嵌套message字段:', Object.keys(tx.transaction.transaction.message));
        } else if (tx?.transaction?.message) {
          console.log('🔍 直接message字段:', Object.keys(tx.transaction.message));
        } else if (tx?.transaction) {
          console.log('🔍 transaction字段:', Object.keys(tx.transaction));
        }
      }
      
      // console.log(`📋 账户列表 (${accountKeys.length}个):`, accountKeys.slice(0, 3)); // 只显示前3个避免日志过长
      const isInvolved = accountKeys.includes(wallet);
      console.log(`🎯 钱包 ${wallet.substring(0, 8)}... 是否参与: ${isInvolved ? '是' : '否'}`);
      
      return isInvolved;
    } catch (error) {
      console.error('❌ 检查钱包参与交易时出错:', error.message);
      return false;
    }
  }

  // 发射事件
  private emitEvent(type: string, event: StreamEvent): void {
    const handler = this.eventHandlers.get(type);
    if (handler) {
      try {
        handler(event);
      } catch (error) {
        console.error(`❌ 处理${type}事件失败:`, error);
      }
    }
  }

  // 处理交易事件
  private handleTransactionEvent(event: StreamEvent): void {
    // 默认实现，可以被外部覆盖
    // console.log('🔥 处理交易事件:', event);
  }


  // 创建初始订阅请求 - 完全按照原文件
  private createInitialSubscribeRequest(): SubscribeRequest {
    return {
      accounts: {},
      slots: {},
      transactions: {
        targetWalletTransaction: {
          vote: false,
          failed: false,
          signature: undefined,
          accountInclude: [TARGET_WALLET],
          accountExclude: [],
          accountRequired: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()],
        },
        myWalletTransaction: {
          vote: false,
          failed: false,
          signature: undefined,
          accountInclude: [MY_WALLET],
          accountExclude: [],
          accountRequired: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()],
        }
      },
      transactionsStatus: {},
      entry: {},
      blocks: {},
      blocksMeta: {},
      accountsDataSlice: [],
      ping: undefined,
      commitment: CommitmentLevel.PROCESSED,
    };
  }

  // 更新订阅
  updateSubscription(newAddresses: string[]): void {
    const oldSize = this.currentSubscription.size;
    
    // 清空当前订阅但保留基本钱包
    this.currentSubscription.clear();
    this.currentSubscription.add(TARGET_WALLET);
    this.currentSubscription.add(MY_WALLET);
    
    // 添加新地址
    newAddresses.forEach(address => {
      this.currentSubscription.add(address);
    });
    
    const newSize = this.currentSubscription.size;
    
    if (newSize !== oldSize) {
      console.log(`🔄 订阅更新: ${oldSize} -> ${newSize} 个地址`);
      
      // 这里可以实现动态更新订阅的逻辑
      // 原文件中可能没有实时更新，这里只是预留接口
    }
  }

  // 更新Token订阅列表
  updateTokenSubscriptions(tokenAddresses: string[]): void {
    const oldSize = this.currentTokenSubscriptions.size;
    this.currentTokenSubscriptions.clear();
    tokenAddresses.forEach(address => {
      this.currentTokenSubscriptions.add(address);
    });
    console.log(`📡 更新Token订阅: ${oldSize} -> ${this.currentTokenSubscriptions.size} 个Token`);
    
    // 🔥 如果有新的Token，动态添加订阅
    if (tokenAddresses.length > 0) {
      this.addTokenSubscriptions(tokenAddresses);
    }
  }

  // 🔥 新增：动态添加Token订阅
  async addTokenSubscriptions(tokenAddresses: string[]): Promise<void> {
    if (!this.isRunning || !this.currentStream) {
      console.log('⚠️ 无法添加Token订阅：流未运行');
      return;
    }
    
    try {
      // 🔥 修复：保留原有钱包订阅，并添加Token订阅
      const updateRequest: SubscribeRequest = {
        accounts: {},
        slots: {},
        transactions: {
          targetWalletTransaction: {
            vote: false,
            failed: false,
            signature: undefined,
            accountInclude: [TARGET_WALLET],
            accountExclude: [],
            accountRequired: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()],
          },
          myWalletTransaction: {
            vote: false,
            failed: false,
            signature: undefined,
            accountInclude: [MY_WALLET],
            accountExclude: [],
            accountRequired: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()],
          },
          targetTokenTransaction: {
            vote: false,
            failed: false,
            signature: undefined,
            accountInclude: tokenAddresses, // 🔥 包含所有Token地址
            accountExclude: [],
            accountRequired: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()],
          }
        },
        transactionsStatus: {},
        entry: {},
        blocks: {},
        blocksMeta: {},
        accountsDataSlice: [],
        commitment: CommitmentLevel.PROCESSED,
      };
      
      // 发送更新订阅请求
      console.log(`📡 添加Token订阅: ${tokenAddresses.length} 个Token`);
      tokenAddresses.forEach(addr => {
        console.log(`   🪙 ${addr.slice(0, 8)}...`);
      });
      
      await new Promise<void>((resolve, reject) => {
        this.currentStream.write(updateRequest, (err: any) => {
          if (err === null || err === undefined) {
            console.log('✅ Token订阅请求已发送');
            resolve();
          } else {
            console.error('❌ 发送Token订阅请求失败:', err);
            reject(err);
          }
        });
      });
      
    } catch (error) {
      console.error('❌ 动态添加Token订阅失败:', error);
    }
  }

  // 检查是否是网络相关错误
  private isNetworkRelatedError(error: any): boolean {
    if (!error) return false;
    
    const message = error.message || error.toString();
    const networkErrorPatterns = [
      'ECONNRESET',
      'ENOTFOUND', 
      'ECONNREFUSED',
      'ETIMEDOUT',
      'socket hang up',
      'network',
      'timeout',
      'connection',
      'CANCELLED'
    ];
    
    return networkErrorPatterns.some(pattern => 
      message.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  // 事件监听器注册方法
  onTransaction(handler: (event: StreamEvent) => void): void {
    this.eventHandlers.set('transaction', handler);
  }

  onAccount(handler: (event: StreamEvent) => void): void {
    this.eventHandlers.set('account', handler);
  }

  onSlot(handler: (event: StreamEvent) => void): void {
    this.eventHandlers.set('slot', handler);
  }

  onBlock(handler: (event: StreamEvent) => void): void {
    this.eventHandlers.set('block', handler);
  }

  onTargetTokenTransaction(handler: (event: StreamEvent) => void): void {
    this.eventHandlers.set('targetTokenTransaction', handler);
  }

  // 获取状态信息
  getStatus(): {
    isRunning: boolean;
    hasClient: boolean;
    hasStream: boolean;
    subscriptionCount: number;
    dataStats: {
      totalReceived: number;
      transactionCount: number;
      accountCount: number;
      slotCount: number;
      blockCount: number;
      lastReceivedTime: Date;
      firstReceivedTime: Date | null;
      lastReceivedTxTime: number;
    };
  } {
    return {
      isRunning: this.isRunning,
      hasClient: this.grpcClient !== null,
      hasStream: this.currentStream !== null,
      subscriptionCount: this.currentSubscription.size,
      dataStats: { ...this.dataStats }
    };
  }
} 