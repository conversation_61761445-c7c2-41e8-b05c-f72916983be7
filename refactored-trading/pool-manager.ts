import { <PERSON>Key, TransactionInstruction, SystemProgram, ComputeBudgetProgram, LAMPORTS_PER_SOL } from "@solana/web3.js";
import { 
  TOKEN_PROGRAM_ID, 
  getAssociatedTokenAddress,
  createAssociatedTokenAccountIdempotentInstruction,
  ASSOCIATED_TOKEN_PROGRAM_ID,
  createSyncNativeInstruction,
  createCloseAccountInstruction
} from "@solana/spl-token";
import { PoolData, TokenTradingInstance } from "./types-and-interfaces";
import { 
  queryConnection,
  PUMP_FUN_AMM_PROGRAM_ID,
  WSOL_TOKEN_ACCOUNT,
  GLOBAL_CONFIG,
  EVENT_AUTHORITY,
  PROTOCOL_FEE_RECIPIENT,
  PROTOCOL_FEE_RECIPIENT_TOKEN_ACCOUNT,
  BUY_DISCRIMINATOR,
  SELL_DISCRIMINATOR,
  USE_JITO,
  TIP_AMOUNT,
  PRIORITY_FEE,
  BUY_SLIPPAGE_PERCENT,
  SELL_SLIPPAGE_PERCENT,
  TRADE_AMOUNT_SOL,
  JITO_TIP_ACCOUNTS
} from "./trading-constants";

/**
 * Pool管理器 - 负责管理交易池数据和相关操作
 */
export class PoolManager {
  private globalPoolCache: Map<string, PoolData> = new Map();
  private tokenInstances: Map<string, TokenTradingInstance>;

  constructor(tokenInstances: Map<string, TokenTradingInstance>) {
    this.tokenInstances = tokenInstances;
  }

  // 从缓存创建Pool数据
  async createPoolDataFromCache(tokenMint: PublicKey): Promise<PoolData> {
    console.log(`📋 创建Pool数据 (缓存模式): ${tokenMint.toBase58().slice(0, 8)}...`);
    
    try {
      const tokenAddress = tokenMint.toBase58();
      
      // 🔥 第一优先级：全局Pool缓存
      if (this.globalPoolCache.has(tokenAddress)) {
        console.log(`✅ 使用全局Pool缓存 ${tokenAddress.slice(0, 8)}...`);
        const cachedData = this.globalPoolCache.get(tokenAddress)!;
         
         // 如果需要计算vault ATA，现在计算
        if (cachedData.needsVaultAta) {
           console.log(`🔗 计算缺失的Vault ATA...`);
          cachedData.vaultAta = await getAssociatedTokenAddress(WSOL_TOKEN_ACCOUNT, cachedData.vaultAuthority, true);
          cachedData.needsVaultAta = false;
          console.log(`✅ Vault ATA计算完成: ${cachedData.vaultAta.toBase58()}`);
         }
         
        console.log(`📊 使用全局缓存Pool信息`);
        return cachedData;
      }
      
      // 🔥 第二优先级：token实例中的pool数据
      const instance = this.tokenInstances.get(tokenAddress);
      if (instance?.realPoolData) {
        console.log(`✅ 使用实例Pool数据 ${tokenAddress.slice(0, 8)}...`);
        const realData = instance.realPoolData;
        
        // 同时保存到全局缓存
        this.globalPoolCache.set(tokenAddress, realData);
        return realData;
      }
      
      // 如果有存储的池子信息，使用缓存
      if (instance?.poolData) {
        console.log(`✅ 使用备用Pool数据 ${tokenAddress.slice(0, 8)}...`);
        return instance.poolData;
      }
      
      // 🔥 如果没有从GRPC提取到pool数据，暂时跳过交易
      console.log(`⚠️ 无法创建Pool数据: 缺少从GRPC提取的真实pool信息`);
      throw new Error(`Token ${tokenAddress.slice(0, 8)}... 缺少pool信息，等待目标钱包交易`);
      
    } catch (error) {
      console.error(`❌ 创建Pool数据失败 ${tokenMint.toBase58().slice(0, 8)}...:`, error);
      throw error;
    }
  }

  // 🔥 从池子账户数据中动态获取coinCreator
  private async fetchCoinCreatorFromPool(poolAddress: PublicKey): Promise<PublicKey> {
    console.log(`🔍 从池子账户获取Coin Creator: ${poolAddress.toBase58().slice(0, 8)}...`);
    
    const poolAccountInfo = await queryConnection.getAccountInfo(poolAddress);
    if (!poolAccountInfo) {
      throw new Error(`无法获取池子账户信息: ${poolAddress.toBase58()}`);
    }
    
    if (poolAccountInfo.data.length < 67) {
      throw new Error(`池子账户数据长度不足: ${poolAccountInfo.data.length} < 67`);
    }
    
    // 从池子数据中提取coinCreator (offset 35-67)
    const coinCreatorBytes = poolAccountInfo.data.slice(35, 67);
    const coinCreator = new PublicKey(coinCreatorBytes);
    
    console.log(`✅ 动态获取Coin Creator成功: ${coinCreator.toBase58()}`);
    return coinCreator;
  }

  // 获取真实池子余额
  async fetchRealPoolBalances(poolData: PoolData): Promise<{ tokenBalance: number, solBalance: number }> {
    try {
      const [tokenAccount, solAccount] = await Promise.all([
        queryConnection.getTokenAccountBalance(poolData.poolBaseTokenAccount),
        queryConnection.getTokenAccountBalance(poolData.poolQuoteTokenAccount)
      ]);

      const tokenBalance = tokenAccount.value.uiAmount || 0;
      const solBalance = solAccount.value.uiAmount || 0;

      console.log(`📊 RPC池子余额: Token=${tokenBalance.toFixed(2)}, SOL=${solBalance.toFixed(6)}`);
      return { tokenBalance, solBalance };
    } catch (error) {
      console.error(`❌ 获取池子余额失败:`, error);
      // 返回默认值
      return { tokenBalance: 1000000, solBalance: 30 };
    }
  }

  // 🔥 从事件中提取池子余额
  private extractRealPoolBalancesFromEvents(parsedTxn: any): { tokenBalance?: number, solBalance?: number } | null {
    try {
      if (!parsedTxn.events || !Array.isArray(parsedTxn.events)) {
        return null;
      }

      for (const event of parsedTxn.events) {
        if (event.data) {
          // 检查各种可能的字段名
          const tokenBalance = event.data.tokenBalance || event.data.token_balance || 
                              event.data.poolTokens || event.data.pool_tokens ||
                              event.data.baseAmount || event.data.base_amount;
          
          const solBalance = event.data.solBalance || event.data.sol_balance ||
                            event.data.poolSol || event.data.pool_sol ||
                            event.data.quoteAmount || event.data.quote_amount;

          if (tokenBalance && solBalance) {
            return {
              tokenBalance: Number(tokenBalance),
              solBalance: Number(solBalance)
            };
          }
        }
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  // 提取Pool数据从交易中 - 完整实现
  async extractPoolDataFromTransaction(tokenMint: PublicKey, parsedTxn: any): Promise<void> {
    const tokenAddress = tokenMint.toBase58();
    
    // 🔥 检查全局缓存是否已经有pool信息
    if (this.globalPoolCache.has(tokenAddress)) {
      return;
    }
    
    // 检查token实例是否已经提取过
    const instance = this.tokenInstances.get(tokenAddress);
    if (instance && (instance.poolExtracted || instance.realPoolData)) {
      return;
    }
    
    try {
      // 🔥 简化：直接从指令中提取pool信息，不限制指令类型
      if (!parsedTxn || !parsedTxn.instructions || !Array.isArray(parsedTxn.instructions)) {
        return;
      }
      
      for (let i = 0; i < parsedTxn.instructions.length; i++) {
        const ix = parsedTxn.instructions[i];
        
        try {
          // 检查是否是pump.fun AMM指令
          if (!ix || !ix.programId || !ix.accounts || !Array.isArray(ix.accounts)) {
            continue;
          }
          
          const isPumpFunInstruction = ix.programId === PUMP_FUN_AMM_PROGRAM_ID.toBase58() || 
                                     ix.programId.toString() === PUMP_FUN_AMM_PROGRAM_ID.toBase58();
          
          if (!isPumpFunInstruction) {
            continue;
          }
          
          // 🔥 简化：直接提取所有相关账户，不管指令类型
          const accountMap = new Map<string, string>();
          
          for (const account of ix.accounts) {
            if (account && account.name && account.pubkey) {
              // 🔥 确保pubkey是完整的字符串
              const fullPubkey = account.pubkey.toString();
              accountMap.set(account.name, fullPubkey);
            }
          }
          
          // 提取关键账户
          const poolAddress = accountMap.get('pool');
          const mintAddress = accountMap.get('base_mint');
          const poolBaseTokenAccount = accountMap.get('pool_base_token_account');
          const poolQuoteTokenAccount = accountMap.get('pool_quote_token_account');
          
          // 🔥 验证：mint地址匹配且有基本pool信息
          if (mintAddress === tokenAddress && poolAddress && poolBaseTokenAccount && poolQuoteTokenAccount) {
            // 创建Pool数据并保存到全局缓存
            const poolData: PoolData = {
              poolAddress: new PublicKey(poolAddress),
              mintAddress: new PublicKey(mintAddress),
              coinCreator: await this.fetchCoinCreatorFromPool(new PublicKey(poolAddress)),
              poolBaseTokenAccount: new PublicKey(poolBaseTokenAccount),
              poolQuoteTokenAccount: new PublicKey(poolQuoteTokenAccount),
              vaultAuthority: new PublicKey('11111111111111111111111111111111'), // 临时值
              vaultAta: new PublicKey('11111111111111111111111111111111'), // 临时值
              tokenBalance: 1000000,
              solBalance: 30
            };
            
            // 获取真实余额
            const realBalances = await this.fetchRealPoolBalances(poolData);
            poolData.tokenBalance = realBalances.tokenBalance;
            poolData.solBalance = realBalances.solBalance;
            
            // 🔥 保存到全局缓存
            this.globalPoolCache.set(tokenAddress, poolData);
            
            // 🔥 如果token实例存在，也保存到实例中
            if (instance) {
              instance.realPoolData = poolData;
              instance.poolExtracted = true;
            }
            
            console.log(`✅ Pool数据提取完成: ${tokenAddress.slice(0, 8)}...`);
            return;
          }
          
        } catch (ixError) {
          console.error(`❌ 处理指令 [${i}] 时出错:`, ixError);
          continue;
        }
      }
      
      console.log(`⚠️ 未能从任何指令中提取到有效的Pool信息`);
      
    } catch (error) {
      console.error(`❌ 提取Pool信息时出错:`, error);
    }
  }

  // 新增：计算滑点后的代币数量
  calculateTokensWithSlippage(solAmount: number, poolData: PoolData): { expectedTokens: bigint, minTokens: bigint } {
    const tokensPerSol = poolData.tokenBalance / poolData.solBalance;
    const expectedTokens = BigInt(Math.floor(solAmount * tokensPerSol * 1_000_000)); // 6 decimals
    const minTokens = BigInt(Math.floor(Number(expectedTokens) * (100 - BUY_SLIPPAGE_PERCENT) / 100));
    
    return { expectedTokens, minTokens };
  }

  // 新增：计算卖出滑点后的最少SOL数量
  calculateMinSolWithSlippage(tokenAmount: number, poolData: PoolData): bigint {
    const solPerToken = poolData.solBalance / poolData.tokenBalance;
    const expectedSol = tokenAmount * solPerToken;
    const minSol = expectedSol * (100 - SELL_SLIPPAGE_PERCENT) / 100;
    const minSolLamports = BigInt(Math.floor(minSol * LAMPORTS_PER_SOL));
    
    return minSolLamports;
  }

  // 获取全局池子缓存
  getGlobalPoolCache(): Map<string, PoolData> {
    return this.globalPoolCache;
  }

  // 清理池子缓存
  clearPoolCache(tokenAddress?: string): void {
    if (tokenAddress) {
      this.globalPoolCache.delete(tokenAddress);
    } else {
      this.globalPoolCache.clear();
    }
  }
} 