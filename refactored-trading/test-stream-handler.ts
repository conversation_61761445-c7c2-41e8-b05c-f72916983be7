#!/usr/bin/env ts-node

import { StreamHandler } from './stream-handler';

/**
 * 简单测试脚本 - 验证stream-handler修复
 */
async function testStreamHandler() {
  console.log('🧪 开始测试StreamHandler...');
  
  const streamHandler = new StreamHandler();
  
  // 添加事件监听器
  streamHandler.onTransaction((event) => {
    console.log('📬 收到交易事件:', event.type);
    console.log('📋 事件数据:', JSON.stringify(event.data, null, 2).substring(0, 200));
  });
  
  try {
    // 启动流处理器
    await streamHandler.start();
    console.log('✅ StreamHandler启动成功');
    
    // 运行30秒测试
    console.log('⏰ 运行30秒测试...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
    // 显示状态
    const status = streamHandler.getStatus();
    console.log('📊 最终状态:', status);
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    // 停止流处理器
    await streamHandler.stop();
    console.log('🛑 StreamHandler已停止');
  }
}

// 错误处理
process.on('unhandledRejection', (error) => {
  console.error('❌ 未处理的Promise拒绝:', error);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n🛑 收到中断信号，正在退出...');
  process.exit(0);
});

// 运行测试
if (require.main === module) {
  testStreamHandler().catch(console.error);
} 