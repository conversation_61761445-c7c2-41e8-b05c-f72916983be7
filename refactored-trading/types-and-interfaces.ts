import { PublicKey } from "@solana/web3.js";
import { 
  CommitmentLevel,
  SubscribeRequestAccountsDataSlice,
  SubscribeRequestFilterAccounts,
  SubscribeRequestFilterBlocks,
  SubscribeRequestFilterBlocksMeta,
  SubscribeRequestFilterEntry,
  SubscribeRequestFilterSlots,
  SubscribeRequestFilterTransactions,
} from "@triton-one/yellowstone-grpc";
import { SubscribeRequestPing } from "@triton-one/yellowstone-grpc/dist/types/grpc/geyser";
import { BuyPredictor } from '../predictors/buy-predictor';
import { SellPredictor } from "../predictors/sell-predictor";

// GRPC 订阅请求接口
export interface SubscribeRequest {
  accounts: { [key: string]: SubscribeRequestFilterAccounts };
  slots: { [key: string]: SubscribeRequestFilterSlots };
  transactions: { [key: string]: SubscribeRequestFilterTransactions };
  transactionsStatus: { [key: string]: SubscribeRequestFilterTransactions };
  blocks: { [key: string]: SubscribeRequestFilterBlocks };
  blocksMeta: { [key: string]: SubscribeRequestFilterBlocksMeta };
  entry: { [key: string]: SubscribeRequestFilterEntry };
  commitment?: CommitmentLevel | undefined;
  accountsDataSlice: SubscribeRequestAccountsDataSlice[];
  ping?: SubscribeRequestPing | undefined;
}

// Token 活动接口
export interface TokenActivity {
  address: string;
  firstSeen: Date;
  lastSeen: Date;
  transactionCount: number;
  actions: Array<{
    type: 'buy' | 'sell';
    amount: number;
    timestamp: Date;
    price?: number;
  }>;
}

// 真实交易结果接口
export interface RealTradeResult {
  success: boolean;
  bundleId?: string;
  actualPrice?: number;
  actualAmount?: number;
  gasFee?: number;
  error?: string;
}

// Pool 数据接口
export interface PoolData {
  poolAddress: PublicKey;
  mintAddress: PublicKey;
  coinCreator: PublicKey;
  poolBaseTokenAccount: PublicKey;
  poolQuoteTokenAccount: PublicKey;
  vaultAuthority: PublicKey;
  vaultAta: PublicKey;
  tokenBalance: number;
  solBalance: number;
  // 🔥 新增：vault ATA异步计算标记
  needsVaultAta?: boolean;
}

// Token 交易实例接口
export interface TokenTradingInstance {
  address: string;
  activity: TokenActivity;
  buyPredictor: BuyPredictor;
  sellPredictor: SellPredictor;
  featureWindow: Array<any>;
  lastPrediction: Date;
  isActive: boolean;
  isTrading: boolean;
  lastTradeTime: Date;
  tradingHistory: Array<{
    type: 'buy' | 'sell';
    timestamp: Date;
    tokenAmount: number;
    solAmount: number;
    price: number;
    prediction: number;
    bundleId?: string;
  }>;
  currentHolding: {
    amount: number;
    buyPrice: number;
    buyTime: Date;
    buySolAmount: number;
    buyGasFee: number;
    buyPlatformFee: number;
    totalBuyCost: number;
    bundleId: string;
  } | null;
  stats: {
    totalTrades: number;
    successfulTrades: number;
    totalPnL: number;
    currentPosition: number;
  };
  lastProcessedTransactionTime?: Date;
  // 🔥 新增：Pool信息提取状态
  poolExtracted?: boolean;
  realPoolData?: PoolData;
  poolData?: PoolData; // 备用池子数据
  // 🔥 新增：交易状态跟踪
  pendingTransactions: Map<string, {
    type: 'buy' | 'sell';
    timestamp: Date;
    signature: string;
    confirmed: boolean;
  }>;
  lastConfirmedSellTime?: Date; // 最后一次确认卖出的时间
}

// 价格历史记录接口
export interface PriceHistoryEntry {
  price: number;
  timestamp: Date;
  solAmount: number;
  tokenAmount: number;
  action: 'buy' | 'sell';
  source: 'stream' | 'own_trade';
}

// Token 统计接口
export interface TokenStats {
  tokenAddress: string;
  totalTrades: number;
  totalPnL: number;
  transactionCount: number;
  winRate: number;
}

// 当前持仓接口
export interface CurrentPosition {
  tokenAddress: string;
  position: number;
  pnl: number;
  buyPrice: number;
  currentPrice: number;
  holdingTime: number;
}

// 卖出缓冲区信息接口
export interface SellOnlyBufferInfo {
  tokenAddress: string;
  reason: string;
  addedTime: Date;
  bufferDurationMinutes: number;
  hasPosition: boolean;
}

// 交易决策接口
export interface TradeDecision {
  action: 'buy' | 'sell' | 'hold';
  confidence: number;
  reason: string;
  amount?: number;
}

// 系统统计接口
export interface SystemStats {
  totalTrades: number;
  successfulTrades: number;
  totalPnL: number;
  dailyPnL: number;
  activeTokens: number;
  monitoredTokens: number;
  totalCapital: number;
  availableCapital: number;
  winRate: number;
  avgTradeTime: number;
}

// 监控队列项接口
export interface MonitoringQueueItem {
  address: string;
  addedTime: Date;
}

// 卖出缓冲队列项接口
export interface SellOnlyBufferItem {
  address: string;
  addedTime: Date;
  reason: string;
  lastCheckTime: Date;
}

// 挂起交易接口
export interface PendingTransaction {
  type: 'buy' | 'sell';
  timestamp: Date;
  signature: string;
  confirmed: boolean;
}

// Token 价格历史接口
export interface TokenPriceHistory {
  lastPrice: number;
  lastUpdateTime: Date;
  priceStartBase: number;
}

// 交易历史记录接口
export interface TradeHistoryEntry {
  type: 'buy' | 'sell';
  timestamp: Date;
  tokenAmount: number;
  solAmount: number;
  price: number;
  prediction: number;
  bundleId?: string;
}

// 流处理事件接口
export interface StreamEvent {
  type: 'transaction' | 'account' | 'slot' | 'block' | 'targetWalletTransaction' | 'myWalletTransaction' | 'targetTokenTransaction';
  data: any;
  timestamp: Date;
}

// 交易数据接口 - 用于AI预测器
export interface TransactionData {
  timestamp: Date;
  action: number; // 0: sell, 1: buy, 2: hold
  sol_amount: number;
  usd_amount: number;
  is_target_wallet: boolean;
  wallet: string;
  block_number: number;
} 