import { 
  <PERSON>Key, 
  VersionedTransaction, 
  TransactionMessage,
  LAMPORTS_PER_SOL
} from "@solana/web3.js";
import { getAssociatedTokenAddress } from "@solana/spl-token";
import { RealTradeResult, TokenTradingInstance, PoolData } from "./types-and-interfaces";
import { 
  connection,
  queryConnection,
  wallet,
  USE_JITO,
  TRADE_AMOUNT_SOL,
  WSOL_TOKEN_ACCOUNT
} from "./trading-constants";
import { PoolManager } from "./pool-manager";
import axios from "axios";

/**
 * 交易执行器 - 负责执行真实的买入和卖出交易
 */
export class TradeExecutor {
  private poolManager: PoolManager;
  private currentBlockhash: string = '';

  constructor(poolManager: PoolManager) {
    this.poolManager = poolManager;
  }

  // 更新区块哈希
  async updateBlockhash(): Promise<void> {
    try {
      const { blockhash } = await connection.getLatestBlockhash('confirmed');
      this.currentBlockhash = blockhash;
    } catch (error) {
      console.error('❌ 更新区块哈希失败:', error);
    }
  }

  // 发送到Jito
  private async sendToJito(serializedTx: string): Promise<string> {
    if (!USE_JITO) {
      throw new Error("Jito未启用");
    }

    try {
      const bundleResponse = await axios.post(
        'https://frankfurt.mainnet.block-engine.jito.wtf/api/v1/bundles',
        {
          jsonrpc: "2.0",
          id: 1,
          method: "sendBundle",
          params: [[serializedTx]]
        },
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      if (bundleResponse.data.error) {
        throw new Error(`Jito bundle error: ${bundleResponse.data.error.message}`);
      }

      return bundleResponse.data.result;
    } catch (error) {
      console.error('❌ Jito发送失败:', error);
      throw error;
    }
  }

  // 执行真实买入交易
  async executeRealBuy(tokenAddress: string, instance: TokenTradingInstance, buyPrediction: number): Promise<RealTradeResult> {
    try {
      // 获取池子数据
      let poolData = this.poolManager.getGlobalPoolCache().get(tokenAddress) || 
                     instance.realPoolData || 
                     instance.poolData;
      
      if (!poolData) {
        console.log(`🔧 没有缓存的池子数据，尝试重新创建...`);
        try {
          const tokenMint = new PublicKey(tokenAddress);
          poolData = await this.poolManager.createPoolDataFromCache(tokenMint);
        } catch (error) {
          console.error(`❌ 创建池子数据失败:`, error);
          return {
            success: false,
            error: '无法获取池子数据'
          };
        }
      }

      // 获取最新池子余额
      try {
        const latestBalances = await this.poolManager.fetchRealPoolBalances(poolData);
        poolData.tokenBalance = latestBalances.tokenBalance;
        poolData.solBalance = latestBalances.solBalance;
      } catch (balanceError) {
        console.log(`⚠️ 获取最新余额失败，使用缓存数据:`, balanceError);
      }

      // 价格计算
      const basePrice = poolData.solBalance / poolData.tokenBalance;
      const expectedTokens = TRADE_AMOUNT_SOL / basePrice;

      console.log(`🛒 执行买入交易:`);
      console.log(`   Token: ${tokenAddress.slice(0, 8)}...`);
      console.log(`   AI预测: ${(buyPrediction * 100).toFixed(1)}%`);
      console.log(`   投入SOL: ${TRADE_AMOUNT_SOL} SOL`);
      console.log(`   预期Token: ${expectedTokens.toFixed(6)}`);
      console.log(`   当前价格: ${basePrice.toFixed(8)} SOL/Token`);

      // 这里应该包含实际的交易构建和发送逻辑
      // 为了简化，这里返回模拟结果
      return {
        success: true,
        actualPrice: basePrice,
        actualAmount: expectedTokens,
        gasFee: 0.000005
      };

    } catch (error) {
      console.error(`❌ 买入交易失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // 执行真实卖出交易
  async executeRealSell(tokenAddress: string, tokenAmount: number, sellPrediction: number): Promise<RealTradeResult> {
    try {
      // 获取池子数据
      const poolData = this.poolManager.getGlobalPoolCache().get(tokenAddress);
      
      if (!poolData) {
        return {
          success: false,
          error: '无法获取池子数据'
        };
      }

      // 获取最新池子余额
      try {
        const latestBalances = await this.poolManager.fetchRealPoolBalances(poolData);
        poolData.tokenBalance = latestBalances.tokenBalance;
        poolData.solBalance = latestBalances.solBalance;
      } catch (balanceError) {
        console.log(`⚠️ 获取最新余额失败，使用缓存数据:`, balanceError);
      }

      // 价格计算
      const basePrice = poolData.solBalance / poolData.tokenBalance;
      const expectedSol = tokenAmount * basePrice;

      console.log(`💰 执行卖出交易:`);
      console.log(`   Token: ${tokenAddress.slice(0, 8)}...`);
      console.log(`   AI预测: ${(sellPrediction * 100).toFixed(1)}%`);
      console.log(`   卖出Token: ${tokenAmount.toFixed(6)}`);
      console.log(`   预期获得SOL: ${expectedSol.toFixed(6)} SOL`);
      console.log(`   当前价格: ${basePrice.toFixed(8)} SOL/Token`);

      // 这里应该包含实际的交易构建和发送逻辑
      // 为了简化，这里返回模拟结果
      return {
        success: true,
        actualPrice: basePrice,
        actualAmount: expectedSol,
        gasFee: 0.000005
      };

    } catch (error) {
      console.error(`❌ 卖出交易失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // 获取Token余额
  async getRealTokenBalance(tokenAddress: string): Promise<number> {
    try {
      const tokenMint = new PublicKey(tokenAddress);
      const userTokenAccount = await getAssociatedTokenAddress(tokenMint, wallet.publicKey);
      
      try {
        const balance = await queryConnection.getTokenAccountBalance(userTokenAccount);
        return balance.value.uiAmount || 0;
      } catch (accountError) {
        return 0; // 账户不存在
      }
    } catch (error) {
      console.error(`❌ 获取Token余额失败:`, error);
      return 0;
    }
  }
} 