import { MainController } from './main-controller';

/**
 * 完整重构版交易系统入口文件
 * 提供与原 real-trading-with-tracker.ts 相同的功能
 */

console.log('🚀 启动完整重构版交易系统...');
console.log('🎯 目标: 与原文件功能完全一致的交易处理');

// 创建主控制器实例
const mainController = new MainController();

// 启动函数
async function main(): Promise<void> {
  try {
    console.log('🔧 初始化交易系统...');
    await mainController.start();
    console.log('✅ 完整交易系统启动成功');
    
    // 保持进程运行
    console.log('🔄 系统运行中... 按 Ctrl+C 停止');
    
  } catch (error) {
    console.error('❌ 启动失败:', error);
    console.error('详细错误:', error.stack);
    process.exit(1);
  }
}

// 优雅关闭处理
async function gracefulShutdown(signal: string): Promise<void> {
  console.log(`\n🛑 收到${signal}信号，开始优雅关闭...`);
  
  try {
    await mainController.stop();
    console.log('✅ 系统已安全关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭过程中出错:', error);
    process.exit(1);
  }
}

// 注册信号处理器
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error);
  gracefulShutdown('UNCAUGHT_EXCEPTION');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 未处理的Promise拒绝:', reason);
  gracefulShutdown('UNHANDLED_REJECTION');
});

// 启动系统
if (require.main === module) {
  main().catch(error => {
    console.error('💥 启动失败:', error);
    process.exit(1);
  });
} 