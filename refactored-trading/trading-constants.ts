import "dotenv/config";
import { PublicKey, Keypair, Connection } from "@solana/web3.js";
import { Idl } from "@coral-xyz/anchor";
import { SolanaParser } from "@shyft-to/solana-transaction-parser";
import { TransactionFormatter } from "../utils/transaction-formatter";
import { SolanaEventParser } from "../utils/event-parser";
import pumpFunAmmIdl from "../idls/pump_amm_0.1.0.json";
import { PartialTradingSystemConfig } from '../trading-config';
import bs58 from "bs58";

// 🔥 交易解析器和格式化器
export const TXN_FORMATTER = new TransactionFormatter();
export const PUMP_FUN_AMM_PROGRAM_ID = new PublicKey("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");
export const PUMP_FUN_IX_PARSER = new SolanaParser([]);
PUMP_FUN_IX_PARSER.addParserFromIdl(PUMP_FUN_AMM_PROGRAM_ID.toBase58(), pumpFunAmmIdl as Idl);
export const PUMP_FUN_EVENT_PARSER = new SolanaEventParser([], console);
PUMP_FUN_EVENT_PARSER.addParserFromIdl(PUMP_FUN_AMM_PROGRAM_ID.toBase58(), pumpFunAmmIdl as Idl);

// Pump.fun 常量
export const WSOL_TOKEN_ACCOUNT = new PublicKey('So11111111111111111111111111111111111111112');
export const GLOBAL_CONFIG = new PublicKey('ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw');
export const EVENT_AUTHORITY = new PublicKey('GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR');
export const PROTOCOL_FEE_RECIPIENT = new PublicKey('62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV');
export const PROTOCOL_FEE_RECIPIENT_TOKEN_ACCOUNT = new PublicKey('94qWNrtmfn42h3ZjUZwWvK1MEo9uVmmrBPd2hpNjYDjb');

// 交易判别器
export const BUY_DISCRIMINATOR = new Uint8Array([102, 6, 61, 18, 1, 218, 235, 234]);
export const SELL_DISCRIMINATOR = new Uint8Array([51, 230, 133, 164, 1, 127, 131, 173]);

// Jito 配置
export const JITO_BLOCK_ENGINE_URL = "https://frankfurt.mainnet.block-engine.jito.wtf";
export const JITO_TIP_ACCOUNTS = [
    "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
    "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe", 
    "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY",
    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
    "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
    "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
    "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
    "********************************************"
];

// 🔥 USE_JITO配置 - 必须在其他常量之前声明
export const USE_JITO = process.env.USE_JITO === 'true';

// 真实交易配置
export const TARGET_WALLET = process.env.TARGET_WALLET || '8J5GUAf7hr3LTPHJSkwrKFDNJPtAXtLHhnNHq6XxTLrW';
export const WINDOW_HOURS = parseFloat(process.env.WINDOW_HOURS || '1');
export const UPDATE_INTERVAL_MS = parseInt(process.env.UPDATE_INTERVAL_MS || '60000');
export const MIN_TRANSACTION_SOL = parseFloat(process.env.MIN_TRANSACTION_SOL || '0.0005');
export const MAX_MONITORED_TOKENS = parseInt(process.env.MAX_MONITORED_TOKENS || '10');

// 真实交易参数
export const TRADE_AMOUNT_SOL = parseFloat(process.env.TRADE_AMOUNT_SOL || '0.0001'); // 单次买入金额，默认0.0001 SOL
export const TIP_AMOUNT = 0.00001; // Jito tip 0.00001 SOL
export const PRIORITY_FEE = 0.000001; // Priority fee 0.000001 SOL
// 滑点配置 - 从环境变量读取
export const BUY_SLIPPAGE_PERCENT = parseFloat(process.env.BUY_SLIPPAGE_PERCENT || '5'); // 默认5%买入滑点
export const SELL_SLIPPAGE_PERCENT = parseFloat(process.env.SELL_SLIPPAGE_PERCENT || '5'); // 默认5%卖出滑点
export const BLOCKHASH_UPDATE_INTERVAL = USE_JITO ? 30000 : 2000; // Jito: 30秒, RPC: 2秒

// 风险管理配置
export const STOP_LOSS_PERCENTAGE = parseFloat(process.env.STOP_LOSS_PERCENTAGE || '0.25'); // 默认25%止损
export const MAX_DAILY_LOSS_SOL = parseFloat(process.env.MAX_DAILY_LOSS_SOL || '0.5'); // 默认0.5 SOL日亏损限制
export const CONSECUTIVE_LOSS_LIMIT = parseInt(process.env.CONSECUTIVE_LOSS_LIMIT || '5'); // 🔥 默认连续亏损5笔后暂停交易
export const INITIAL_CAPITAL_SOL = parseFloat(process.env.INITIAL_CAPITAL_SOL || '5.0'); // 默认5 SOL初始资金

// AI配置常量
export const AI_CONFIG = {
  predictionInterval: parseInt(process.env.PREDICTION_INTERVAL || '30000'),
  featureWindowSize: parseInt(process.env.FEATURE_WINDOW_SIZE || '20'),
};

// 状态更新间隔
export const STATUS_UPDATE_INTERVAL = parseInt(process.env.STATUS_UPDATE_INTERVAL_MS || '30000'); // 改为30秒以便更频繁看到状态

// 增加一些调试常量
export const DEBUG_MODE = process.env.DEBUG_MODE === 'true';
export const PREDICTION_INTERVAL_MS = 30000; // 预测间隔30秒
export const TOKEN_STATS_UPDATE_INTERVAL = 60000; // Token统计更新间隔1分钟

// 钱包配置
const PRIVATE_KEY = process.env.PRIVATE_KEY;
if (!PRIVATE_KEY) {
    throw new Error("PRIVATE_KEY not found in environment variables");
}

// 🔥 修复私钥格式问题 - 专门处理base58格式
let wallet: Keypair;
try {
  console.log(`🔑 尝试加载Base58私钥...`);
  console.log(`   私钥长度: ${PRIVATE_KEY.length} 字符`);
  console.log(`   私钥前缀: ${PRIVATE_KEY.substring(0, 10)}...`);
  
  // 解码base58私钥
  const secretKeyBytes = bs58.decode(PRIVATE_KEY);
  console.log(`   解码后字节长度: ${secretKeyBytes.length} bytes`);
  
  // Solana私钥应该是64字节
  if (secretKeyBytes.length !== 64) {
    throw new Error(`私钥字节长度错误: 期望64字节，实际${secretKeyBytes.length}字节`);
  }
  
  wallet = Keypair.fromSecretKey(secretKeyBytes);
  console.log(`✅ 私钥加载成功: ${wallet.publicKey.toBase58()}`);
  
} catch (error) {
  console.error('❌ Base58私钥加载失败');
  console.error('错误详情:', error);
  console.error('');
  console.error('请检查您的PRIVATE_KEY环境变量:');
  console.error('1. 确保是完整的64字节私钥的base58编码');
  console.error('2. 私钥应该类似: "5Kj...ABC" (88个字符左右)');
  console.error('3. 可以使用 solana-keygen 生成新的密钥对');
  console.error('');
  throw new Error(`私钥加载失败: ${error}`);
}

export { wallet };

// 连接配置
export const RPC_ENDPOINT = process.env.SOLANA_RPC_ENDPOINT;
export const connection = new Connection(RPC_ENDPOINT);

// 🔥 新增：独立的查询RPC端点 - 专门用于余额查询、交易确认等查询操作
export const QUERY_RPC_ENDPOINT = "https://mainnet.helius-rpc.com/?api-key=4c08b3c1-801f-4656-aa62-79deca4d1a18";
export const queryConnection = new Connection(QUERY_RPC_ENDPOINT, {
  commitment: "confirmed",
  confirmTransactionInitialTimeout: 120000, // 2分钟超时
  wsEndpoint: undefined // 不使用websocket
});

// 🔥 定义我自己的钱包地址，用于GRPC监控
export const MY_WALLET = process.env.MY_WALLET || wallet.publicKey.toBase58();

// Real Trading 配置
export const REAL_TRADING_CONFIG: PartialTradingSystemConfig = {
  trading: {
    tradeAmountSol: TRADE_AMOUNT_SOL,
    initialCapitalSol: parseFloat(process.env.INITIAL_CAPITAL_SOL || '5.0'),
    maxPositions: parseInt(process.env.MAX_POSITIONS || '3'),
    paperTrading: false, // 🔥 真实交易模式
    slippageTolerance: parseFloat(process.env.SLIPPAGE_TOLERANCE || '0.05') // 5%滑点
  },
  
  risk: {
    stopLossPercentage: parseFloat(process.env.STOP_LOSS_PERCENTAGE || '0.08'),
    takeProfitPercentage: parseFloat(process.env.TAKE_PROFIT_PERCENTAGE || '0.15'),
    maxDailyLossSol: parseFloat(process.env.MAX_DAILY_LOSS_SOL || '1.0'),
    maxTotalLossSol: parseFloat(process.env.MAX_TOTAL_LOSS_SOL || '2.0'),
  },

  models: {
    buyThreshold: parseFloat(process.env.BUY_THRESHOLD || '0.7'), // 70%买入阈值，更严格
    sellThreshold: parseFloat(process.env.SELL_THRESHOLD || '0.6'), // 60%卖出阈值，更严格
  },

  monitoring: {
    targetTokenAddresses: ['So11111111111111111111111111111111111111112'],
    logLevel: 'info' as const,
    enableDetailedLogs: true,
    statsUpdateIntervalSeconds: 30
  },

  grpc: {
    endpoint: process.env.ENDPOINT,
    token: process.env.X_TOKEN
  }
};

// 启动时打印配置信息
console.log('🚀 Real Trading 动态订阅系统 (增强版GRPC解析)');
console.log('=' + '='.repeat(80));
console.log(`🎯 目标钱包: ${TARGET_WALLET}`);
console.log(`💰 真实交易模式: ✅ 启用`);
console.log(`💵 每笔交易: ${TRADE_AMOUNT_SOL} SOL`);
console.log(`🚀 交易方式: ${USE_JITO ? 'Jito Bundle' : 'RPC直发'}`);
if (USE_JITO) {
  console.log(`💸 Jito Tip: ${TIP_AMOUNT} SOL`);
}
console.log(`⚡ Priority Fee: ${PRIORITY_FEE} SOL`);
console.log(`📊 滑点容忍: 买入${BUY_SLIPPAGE_PERCENT}% | 卖出${SELL_SLIPPAGE_PERCENT}%`);
console.log(`🔄 Blockhash更新: 每${BLOCKHASH_UPDATE_INTERVAL/1000}秒 (${USE_JITO ? 'Jito优化' : 'RPC频率'})`);
console.log(`🎯 AI买入阈值: ${(REAL_TRADING_CONFIG.models?.buyThreshold! * 100).toFixed(1)}%`);
console.log(`🎯 AI卖出阈值: ${(REAL_TRADING_CONFIG.models?.sellThreshold! * 100).toFixed(1)}%`);
console.log(`🚫 最小交易过滤: ≥${MIN_TRANSACTION_SOL} SOL`);
console.log(`🔢 Token监控上限: ${MAX_MONITORED_TOKENS} 个`);
console.log(`💼 钱包地址: ${wallet.publicKey.toBase58()}`);
console.log(`🔗 交易发送RPC: ${process.env.SOLANA_RPC_ENDPOINT || "https://solana-rpc.publicnode.com"}`);
console.log(`🔍 查询专用RPC: ${QUERY_RPC_ENDPOINT}`);
console.log('');

console.log('🛡️ 风险管理配置:');
console.log(`   止损比例: ${(STOP_LOSS_PERCENTAGE * 100).toFixed(1)}%`);
console.log(`   日亏损限制: ${MAX_DAILY_LOSS_SOL} SOL`);
console.log(`   连续亏损限制: ${CONSECUTIVE_LOSS_LIMIT === 999999 ? '已禁用' : CONSECUTIVE_LOSS_LIMIT + ' 笔'}`);
console.log(`   初始资金: ${INITIAL_CAPITAL_SOL} SOL`);
console.log('');

// 🔥 调试模式说明
if (process.env.DEBUG_PARSING === 'true') {
  console.log('🔍 调试模式已启用 - 将显示详细的解析信息');
} else {
  console.log('💡 提示: 设置 DEBUG_PARSING=true 可启用详细解析调试');
}
console.log(''); 