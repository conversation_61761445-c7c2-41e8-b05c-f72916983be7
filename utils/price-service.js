"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PriceService = void 0;
const web3_js_1 = require("@solana/web3.js");
const axios_1 = require("axios");
class PriceService {
    constructor() {
        this.lastSolPrice = 0;
        this.lastPriceUpdateTime = 0;
        this.PRICE_CACHE_DURATION = 30000; // 30 seconds cache
        // SOL price feed ID from Pyth Network
        this.SOL_PRICE_FEED_ID = 'ef0d8b6fda2ceba41da15d4095d1da392a0d2f8ed0c6c7bc0f4cfac8c280b56d';
        // Initialize connection to Solana mainnet
        this.connection = new web3_js_1.Connection(process.env.RPC_URL || 'https://api.mainnet-beta.solana.com', 'confirmed');
    }
    static getInstance() {
        if (!PriceService.instance) {
            PriceService.instance = new PriceService();
        }
        return PriceService.instance;
    }
    /**
     * Get current SOL/USD price with multiple fallback methods
     */
    async getSolUsdPrice() {
        const now = Date.now();
        // Return cached price if still valid
        if (this.lastSolPrice > 0 && (now - this.lastPriceUpdateTime) < this.PRICE_CACHE_DURATION) {
            return this.lastSolPrice;
        }
        try {
            // Try Pyth Hermes API first (faster and more reliable)
            const price = await this.fetchFromPythHermes();
            if (price > 0) {
                this.lastSolPrice = price;
                this.lastPriceUpdateTime = now;
                console.log(`🔮 SOL/USD价格更新 (Pyth): $${price.toFixed(2)}`);
                return price;
            }
        }
        catch (error) {
            console.warn('🔮 Pyth Hermes API失败，尝试备用方案:', error);
        }
        try {
            // Fallback to CoinGecko API
            const price = await this.fetchFromCoinGecko();
            if (price > 0) {
                this.lastSolPrice = price;
                this.lastPriceUpdateTime = now;
                console.log(`🔮 SOL/USD价格更新 (CoinGecko): $${price.toFixed(2)}`);
                return price;
            }
        }
        catch (error) {
            console.warn('🔮 CoinGecko API失败:', error);
        }
        // Final fallback to default price
        const defaultPrice = this.getDefaultSolPrice();
        console.warn(`🔮 使用默认SOL价格: $${defaultPrice.toFixed(2)}`);
        return defaultPrice;
    }
    /**
     * Fetch SOL price from Pyth Hermes API
     */
    async fetchFromPythHermes() {
        const response = await axios_1.default.get(`https://hermes.pyth.network/v2/updates/price/latest?ids[]=${this.SOL_PRICE_FEED_ID}`, { timeout: 5000 });
        if (response.data && response.data.parsed && response.data.parsed.length > 0) {
            const priceData = response.data.parsed[0];
            if (priceData.price && priceData.price.price) {
                // Pyth prices include an exponent, so we need to adjust
                const price = parseFloat(priceData.price.price);
                const expo = priceData.price.expo;
                return price * Math.pow(10, expo);
            }
        }
        throw new Error('Invalid Pyth response format');
    }
    /**
     * Fetch SOL price from CoinGecko API as fallback
     */
    async fetchFromCoinGecko() {
        const response = await axios_1.default.get('https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd', { timeout: 5000 });
        if (response.data && response.data.solana && response.data.solana.usd) {
            return parseFloat(response.data.solana.usd);
        }
        throw new Error('Invalid CoinGecko response format');
    }
    /**
     * Get default SOL price as final fallback
     */
    getDefaultSolPrice() {
        // Return a reasonable default SOL price (update this periodically)
        return 240.0; // $240 USD per SOL
    }
    /**
     * Convert SOL price to USD price
     */
    async convertSolToUsd(solAmount) {
        const solUsdPrice = await this.getSolUsdPrice();
        return solAmount * solUsdPrice;
    }
    /**
     * Convert SOL/token price to USD/token price
     */
    async convertTokenPriceToUsd(solPerToken) {
        const solUsdPrice = await this.getSolUsdPrice();
        return solPerToken * solUsdPrice;
    }
    /**
     * Format USD price with appropriate decimal places
     */
    formatUsdPrice(usdPrice) {
        if (usdPrice >= 1) {
            return `$${usdPrice.toFixed(4)}`;
        }
        else if (usdPrice >= 0.01) {
            return `$${usdPrice.toFixed(6)}`;
        }
        else {
            return `$${usdPrice.toFixed(8)}`;
        }
    }
    /**
     * Get cached SOL price without making new requests
     */
    getCachedSolPrice() {
        return this.lastSolPrice > 0 ? this.lastSolPrice : this.getDefaultSolPrice();
    }
    /**
     * Manual price update method for testing
     */
    async updatePrice() {
        this.lastPriceUpdateTime = 0; // Force refresh
        return await this.getSolUsdPrice();
    }
}
exports.PriceService = PriceService;
exports.default = PriceService;
