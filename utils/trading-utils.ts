import { PublicKey, Connection } from '@solana/web3.js';
import { PoolData, TokenTradingInstance, PriceData } from '../types/trading-interfaces';

export class TradingUtils {
  
  /**
   * 验证钱包地址格式
   */
  public static validateWalletAddress(address: string): boolean {
    try {
      new PublicKey(address);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 安全创建PublicKey对象
   */
  public static safeCreatePublicKey(address: string, name: string = 'address'): PublicKey {
    try {
      return new PublicKey(address);
    } catch (error) {
      throw new Error(`无效的${name}: ${address}`);
    }
  }

  /**
   * 格式化Token显示名称
   */
  public static formatTokenDisplay(tokenAddress: string, symbol?: string): string {
    const shortAddress = `${tokenAddress.slice(0, 4)}...${tokenAddress.slice(-4)}`;
    return symbol ? `${symbol}(${shortAddress})` : shortAddress;
  }

  /**
   * 计算带滑点的Token数量
   */
  public static calculateTokensWithSlippage(
    solAmount: number, 
    poolData: PoolData, 
    slippageTolerance: number
  ): { expectedTokens: bigint, minTokens: bigint } {
    // 计算预期Token数量
    const expectedTokens = BigInt(Math.floor(
      (solAmount * poolData.tokenBalance) / poolData.solBalance
    ));
    
    // 计算最小Token数量（考虑滑点）
    const minTokens = BigInt(Math.floor(
      Number(expectedTokens) * (1 - slippageTolerance)
    ));
    
    return { expectedTokens, minTokens };
  }

  /**
   * 计算带滑点的最小SOL数量
   */
  public static calculateMinSolWithSlippage(
    tokenAmount: number, 
    poolData: PoolData, 
    slippageTolerance: number
  ): bigint {
    // 计算预期SOL数量
    const expectedSol = (tokenAmount * poolData.solBalance) / poolData.tokenBalance;
    
    // 计算最小SOL数量（考虑滑点）
    const minSol = expectedSol * (1 - slippageTolerance);
    
    return BigInt(Math.floor(minSol * 1e9)); // 转换为lamports
  }

  /**
   * 计算价格影响
   */
  public static calculatePriceImpact(
    tradeAmount: number, 
    poolLiquidity: number
  ): number {
    return (tradeAmount / poolLiquidity) * 100;
  }

  /**
   * 格式化价格显示
   */
  public static formatPrice(price: number, decimals: number = 8): string {
    if (price === 0) return '0';
    if (price < 0.000001) return price.toExponential(3);
    return price.toFixed(decimals).replace(/\.?0+$/, '');
  }

  /**
   * 格式化SOL数量显示
   */
  public static formatSolAmount(amount: number, decimals: number = 4): string {
    return `${amount.toFixed(decimals)} SOL`;
  }

  /**
   * 格式化百分比显示
   */
  public static formatPercentage(value: number, decimals: number = 2): string {
    return `${value.toFixed(decimals)}%`;
  }

  /**
   * 格式化持仓时间
   */
  public static formatHoldingTime(startTime: Date, endTime: Date = new Date()): string {
    const diffMs = endTime.getTime() - startTime.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays}天${diffHours % 24}小时`;
    } else if (diffHours > 0) {
      return `${diffHours}小时${diffMinutes % 60}分钟`;
    } else {
      return `${diffMinutes}分钟`;
    }
  }

  /**
   * 计算移动平均价格
   */
  public static calculateMovingAverage(prices: number[], window: number): number {
    if (prices.length === 0) return 0;
    const relevantPrices = prices.slice(-window);
    return relevantPrices.reduce((sum, price) => sum + price, 0) / relevantPrices.length;
  }

  /**
   * 计算价格波动率
   */
  public static calculateVolatility(prices: number[]): number {
    if (prices.length < 2) return 0;
    
    const mean = prices.reduce((sum, price) => sum + price, 0) / prices.length;
    const variance = prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / prices.length;
    
    return Math.sqrt(variance);
  }

  /**
   * 检查是否为网络相关错误
   */
  public static isNetworkRelatedError(error: any): boolean {
    const networkErrors = [
      'ECONNRESET', 'ENOTFOUND', 'ECONNREFUSED', 'ETIMEDOUT',
      'socket hang up', 'network timeout', 'connection refused'
    ];
    
    const errorString = error?.message?.toLowerCase() || error?.toString()?.toLowerCase() || '';
    return networkErrors.some(netError => errorString.includes(netError.toLowerCase()));
  }

  /**
   * 安全解析JSON
   */
  public static safeParseJSON<T>(jsonString: string, defaultValue: T): T {
    try {
      return JSON.parse(jsonString);
    } catch {
      return defaultValue;
    }
  }

  /**
   * 生成唯一交易ID
   */
  public static generateTradeId(tokenAddress: string, type: 'buy' | 'sell'): string {
    return `${type}_${tokenAddress}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 计算ROI
   */
  public static calculateROI(initialValue: number, currentValue: number): number {
    if (initialValue === 0) return 0;
    return ((currentValue - initialValue) / initialValue) * 100;
  }

  /**
   * 计算年化收益率
   */
  public static calculateAnnualizedReturn(
    totalReturn: number, 
    holdingPeriodDays: number
  ): number {
    if (holdingPeriodDays === 0) return 0;
    return Math.pow(1 + totalReturn / 100, 365 / holdingPeriodDays) - 1;
  }

  /**
   * 计算夏普比率
   */
  public static calculateSharpeRatio(
    returns: number[], 
    riskFreeRate: number = 0
  ): number {
    if (returns.length === 0) return 0;
    
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const volatility = this.calculateVolatility(returns);
    
    if (volatility === 0) return 0;
    return (avgReturn - riskFreeRate) / volatility;
  }

  /**
   * 计算最大回撤
   */
  public static calculateMaxDrawdown(values: number[]): number {
    if (values.length === 0) return 0;
    
    let maxDrawdown = 0;
    let peak = values[0];
    
    for (const value of values) {
      if (value > peak) {
        peak = value;
      }
      
      const drawdown = (peak - value) / peak;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }
    
    return maxDrawdown * 100;
  }

  /**
   * 获取价格趋势
   */
  public static getPriceTrend(prices: number[], window: number = 5): 'up' | 'down' | 'sideways' {
    if (prices.length < window) return 'sideways';
    
    const recentPrices = prices.slice(-window);
    const firstPrice = recentPrices[0];
    const lastPrice = recentPrices[recentPrices.length - 1];
    
    const changePercent = ((lastPrice - firstPrice) / firstPrice) * 100;
    
    if (changePercent > 2) return 'up';
    if (changePercent < -2) return 'down';
    return 'sideways';
  }

  /**
   * 延迟执行
   */
  public static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 重试机制
   */
  public static async retry<T>(
    fn: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        if (i === maxRetries) {
          throw lastError;
        }
        
        console.warn(`重试 ${i + 1}/${maxRetries}: ${lastError.message}`);
        await this.sleep(delay * Math.pow(2, i)); // 指数退避
      }
    }
    
    throw lastError!;
  }

  /**
   * 格式化错误信息
   */
  public static formatError(error: any): string {
    if (error?.message) return error.message;
    if (typeof error === 'string') return error;
    return JSON.stringify(error);
  }

  /**
   * 检查对象是否为空
   */
  public static isEmpty(obj: any): boolean {
    if (obj == null) return true;
    if (Array.isArray(obj)) return obj.length === 0;
    if (typeof obj === 'object') return Object.keys(obj).length === 0;
    return false;
  }

  /**
   * 深度克隆对象
   */
  public static deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime()) as any;
    if (obj instanceof Array) return obj.map(item => this.deepClone(item)) as any;
    if (typeof obj === 'object') {
      const clonedObj = {} as T;
      for (const key in obj) {
        clonedObj[key] = this.deepClone(obj[key]);
      }
      return clonedObj;
    }
    return obj;
  }

  /**
   * 限制数值范围
   */
  public static clamp(value: number, min: number, max: number): number {
    return Math.min(Math.max(value, min), max);
  }

  /**
   * 生成随机字符串
   */
  public static generateRandomString(length: number = 8): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 获取当前时间戳
   */
  public static getCurrentTimestamp(): number {
    return Math.floor(Date.now() / 1000);
  }

  /**
   * 格式化时间戳
   */
  public static formatTimestamp(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  }
} 