import axios from 'axios';
import express from 'express';
import { Server } from 'http';
import PriceService from './price-service';

export interface TradingNotification {
  type: 'subscription' | 'buy' | 'sell' | 'liquidation' | 'system';
  tokenAddress?: string;
  tokenSymbol?: string;
  message: string;
  data?: {
    // 买入信息
    buyPrice?: number;
    buyAmount?: number;
    buySolAmount?: number;
    
    // 卖出信息
    sellPrice?: number;
    sellAmount?: number;
    sellSolAmount?: number;
    
    // 收益信息
    profit?: number;
    profitPercentage?: number;
    holdingTime?: number; // 持仓时间（分钟）
    
    // 其他信息
    prediction?: number;
    timestamp?: Date;
    walletAddress?: string;
  };
}

// 🔥 新增：消息队列项接口
interface QueuedMessage {
  id: string;
  notification: TradingNotification;
  chatId: string;
  timestamp: Date;
  retryCount: number;
  priority: number; // 优先级：1=高(系统消息), 2=中(交易消息), 3=低(统计消息)
}

// 数据提供接口 - 主系统需要实现这个接口
export interface TradingDataProvider {
  getActiveTokens(): string[];
  getCurrentPositions(): Array<{
    tokenAddress: string;
    position: number;
    pnl: number;
    buyPrice: number;
    currentPrice: number;
    holdingTime: number;
  }>;
  getTokenStats(): Array<{
    tokenAddress: string;
    totalTrades: number;
    totalPnL: number;
    transactionCount: number;
    winRate: number;
  }>;
  getSellOnlyBufferInfo(): Array<{
    tokenAddress: string;
    reason: string;
    addedTime: Date;
    bufferDurationMinutes: number;
    hasPosition: boolean;
  }>;
}

export class TelegramNotifier {
  private botToken: string;
  private chatId: string | null = null;
  private isEnabled: boolean;
  
  // Webhook服务器
  private webhookServer: Server | null = null;
  private webhookPort: number = 3001;
  private webhookPath: string = '/webhook';
  
  // 数据提供者
  private dataProvider: TradingDataProvider | null = null;

  // 🔥 新增：消息队列系统
  private messageQueue: QueuedMessage[] = [];
  private isProcessingQueue: boolean = false;
  private queueProcessor: NodeJS.Timeout | null = null;
  private lastSentTime: number = 0;
  private rateLimitUntil: number = 0; // 速率限制解除时间
  private readonly MESSAGE_INTERVAL_MS = 1100; // 每条消息间隔1.1秒（略大于1秒确保安全）
  private readonly MAX_RETRY_COUNT = 3;
  private readonly QUEUE_MAX_SIZE = 100; // 队列最大长度

  // 🔥 新增：队列统计
  private queueStats = {
    totalSent: 0,
    totalFailed: 0,
    rateLimitHits: 0,
    queueOverflows: 0
  };

  constructor(botToken: string = '**********:AAHSQTLWA_FC1naa7L95oaohm79f9NEum5M') {
    this.botToken = botToken;
    this.isEnabled = !!botToken;
    
    // 尝试从环境变量获取Chat ID
    this.chatId = process.env.TELEGRAM_CHAT_ID || null;
    
    if (this.isEnabled) {
      console.log('📱 Telegram通知服务已启用');
      console.log('🔄 消息队列系统已初始化 (间隔: 1.1秒/消息)');
      if (this.chatId) {
        console.log(`📱 使用预设Chat ID: ${this.chatId}`);
      } else {
        console.log('📱 未设置Chat ID，将尝试自动获取');
        console.log('💡 提示: 可以设置环境变量 TELEGRAM_CHAT_ID 来手动指定');
        console.log('💡 获取Chat ID方法: 发送 /start 给bot，然后访问:');
        console.log(`💡 https://api.telegram.org/bot${this.botToken}/getUpdates`);
      }
      
      // 🔥 启动队列处理器
      this.startQueueProcessor();
    } else {
      console.log('📱 Telegram通知服务未配置');
    }
  }

  // 🔥 新增：启动队列处理器
  private startQueueProcessor(): void {
    if (this.queueProcessor) {
      clearInterval(this.queueProcessor);
    }

    this.queueProcessor = setInterval(() => {
      this.processMessageQueue();
    }, 500); // 每500ms检查一次队列

    console.log('🔄 Telegram消息队列处理器已启动');
  }

  // 🔥 新增：停止队列处理器
  private stopQueueProcessor(): void {
    if (this.queueProcessor) {
      clearInterval(this.queueProcessor);
      this.queueProcessor = null;
      console.log('🔄 Telegram消息队列处理器已停止');
    }
  }

  // 🔥 新增：处理消息队列
  private async processMessageQueue(): Promise<void> {
    if (!this.isEnabled || this.isProcessingQueue || this.messageQueue.length === 0) {
      return;
    }

    // 检查速率限制
    const now = Date.now();
    if (now < this.rateLimitUntil) {
      const waitSeconds = Math.ceil((this.rateLimitUntil - now) / 1000);
      if (waitSeconds % 10 === 0) { // 每10秒打印一次等待信息
        console.log(`⏳ Telegram速率限制中，还需等待 ${waitSeconds} 秒...`);
      }
      return;
    }

    // 检查消息发送间隔
    if (now - this.lastSentTime < this.MESSAGE_INTERVAL_MS) {
      return; // 还没到发送时间
    }

    this.isProcessingQueue = true;

    try {
      // 按优先级排序队列（优先级数字越小越优先）
      this.messageQueue.sort((a, b) => {
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }
        // 同优先级按时间排序
        return a.timestamp.getTime() - b.timestamp.getTime();
      });

      const queuedMessage = this.messageQueue.shift();
      if (!queuedMessage) {
        return;
      }

      console.log(`📤 处理队列消息 (${queuedMessage.id.slice(0, 8)}...) 优先级:${queuedMessage.priority} 队列剩余:${this.messageQueue.length}`);

      const success = await this.sendMessageDirectly(queuedMessage.chatId, queuedMessage.notification);
      
      if (success) {
        this.queueStats.totalSent++;
        this.lastSentTime = Date.now();
        console.log(`✅ 消息发送成功 (队列剩余: ${this.messageQueue.length})`);
      } else {
        // 发送失败，根据错误类型处理
        queuedMessage.retryCount++;
        
        if (queuedMessage.retryCount <= this.MAX_RETRY_COUNT) {
          // 重新加入队列，降低优先级
          queuedMessage.priority = Math.min(queuedMessage.priority + 1, 5);
          this.messageQueue.unshift(queuedMessage); // 放回队列前面
          console.log(`🔄 消息重试 ${queuedMessage.retryCount}/${this.MAX_RETRY_COUNT} (${queuedMessage.id.slice(0, 8)}...)`);
        } else {
          this.queueStats.totalFailed++;
          console.log(`❌ 消息发送失败，已达最大重试次数 (${queuedMessage.id.slice(0, 8)}...)`);
        }
      }

    } catch (error) {
      console.error('❌ 队列处理错误:', error);
    } finally {
      this.isProcessingQueue = false;
    }
  }

  // 🔥 新增：直接发送消息（不经过队列）
  private async sendMessageDirectly(chatId: string, notification: TradingNotification): Promise<boolean> {
    try {
      const url = `https://api.telegram.org/bot${this.botToken}/sendMessage`;
      
      const payload = {
        chat_id: chatId,
        text: notification.message,
        parse_mode: 'Markdown',
        disable_web_page_preview: true
      };

      const response = await axios.post(url, payload, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data.ok) {
        return true;
      } else {
        console.error('📱 Telegram发送失败:', response.data);
        return false;
      }

    } catch (error) {
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        
        if (status === 429) {
          // 处理速率限制
          this.queueStats.rateLimitHits++;
          const retryAfter = parseInt(error.response?.headers?.['retry-after'] || '60');
          
          // 🔥 修复：限制最大等待时间，避免过长等待
          const maxWaitSeconds = 300; // 最多等待5分钟
          const actualWaitSeconds = Math.min(retryAfter, maxWaitSeconds);
          this.rateLimitUntil = Date.now() + (actualWaitSeconds + 5) * 1000; // 多等5秒确保安全
          
          console.log(`⚠️ Telegram 429速率限制，原始等待时间: ${retryAfter}秒，实际等待: ${actualWaitSeconds + 5}秒`);
          console.log(`📊 速率限制统计: 第 ${this.queueStats.rateLimitHits} 次遇到429错误`);
          
          return false; // 返回false让队列系统重试
        } else {
          console.error('📱 Telegram API错误:', {
            status: status,
            statusText: error.response?.statusText,
            data: error.response?.data
          });
        }
      } else {
        console.error('📱 Telegram发送错误:', error);
      }
      return false;
    }
  }

  // 🔥 新增：添加消息到队列
  private addToQueue(notification: TradingNotification, chatId: string, priority: number = 2): string {
    // 检查队列是否已满
    if (this.messageQueue.length >= this.QUEUE_MAX_SIZE) {
      // 移除最旧的低优先级消息
      const lowPriorityIndex = this.messageQueue.findIndex(msg => msg.priority >= 3);
      if (lowPriorityIndex !== -1) {
        const removed = this.messageQueue.splice(lowPriorityIndex, 1)[0];
        console.log(`⚠️ 队列已满，移除低优先级消息 (${removed.id.slice(0, 8)}...)`);
        this.queueStats.queueOverflows++;
      } else {
        // 如果没有低优先级消息，移除最旧的消息
        const removed = this.messageQueue.shift();
        if (removed) {
          console.log(`⚠️ 队列已满，移除最旧消息 (${removed.id.slice(0, 8)}...)`);
          this.queueStats.queueOverflows++;
        }
      }
    }

    const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const queuedMessage: QueuedMessage = {
      id: messageId,
      notification,
      chatId,
      timestamp: new Date(),
      retryCount: 0,
      priority
    };

    this.messageQueue.push(queuedMessage);
    
    console.log(`📥 消息已加入队列 (${messageId.slice(0, 12)}...) 优先级:${priority} 队列长度:${this.messageQueue.length}/${this.QUEUE_MAX_SIZE}`);
    
    return messageId;
  }

  // 🔥 新增：获取队列状态
  public getQueueStatus(): {
    queueLength: number;
    isProcessing: boolean;
    rateLimitUntil: Date | null;
    stats: typeof this.queueStats;
  } {
    return {
      queueLength: this.messageQueue.length,
      isProcessing: this.isProcessingQueue,
      rateLimitUntil: this.rateLimitUntil > 0 ? new Date(this.rateLimitUntil) : null,
      stats: { ...this.queueStats }
    };
  }

  // 🔥 新增：清空队列
  public clearQueue(): number {
    const clearedCount = this.messageQueue.length;
    this.messageQueue = [];
    console.log(`🗑️ 已清空消息队列，移除 ${clearedCount} 条消息`);
    return clearedCount;
  }

  /**
   * 设置数据提供者
   */
  public setDataProvider(provider: TradingDataProvider): void {
    this.dataProvider = provider;
    console.log('📱 Telegram数据提供者已设置');
  }

  /**
   * 启动webhook服务器
   */
  public async startWebhook(): Promise<void> {
    if (!this.isEnabled) {
      console.log('📱 Telegram未启用，跳过webhook启动');
      return;
    }

    try {
      // 设置Bot菜单命令
      await this.setBotCommands();
      
      const app = express();
      app.use(express.json());

      // Webhook端点
      app.post(this.webhookPath, async (req, res) => {
        try {
          await this.handleWebhookUpdate(req.body);
          res.status(200).send('OK');
        } catch (error) {
          console.error('📱 Webhook处理错误:', error);
          res.status(500).send('Error');
        }
      });

      // 健康检查端点
      app.get('/health', (req, res) => {
        res.status(200).json({ status: 'ok', service: 'telegram-webhook' });
      });

      this.webhookServer = app.listen(this.webhookPort, () => {
        console.log(`📱 Telegram Webhook服务器启动: http://0.0.0.0:${this.webhookPort}${this.webhookPath}`);
      });

      // 设置webhook URL（假设部署到本地或有公网IP）
      // 在实际部署时，需要使用ngrok或公网服务器
      const webhookUrl = process.env.WEBHOOK_URL || `http://0.0.0.0:${this.webhookPort}${this.webhookPath}`;
      console.log(`📱 Webhook URL: ${webhookUrl}`);
      
    } catch (error) {
      console.error('📱 启动Webhook服务器失败:', error);
    }
  }

  /**
   * 停止webhook服务器
   */
  public async stopWebhook(): Promise<void> {
    // 🔥 停止队列处理器
    this.stopQueueProcessor();
    
    if (this.webhookServer) {
      this.webhookServer.close();
      this.webhookServer = null;
      console.log('📱 Telegram Webhook服务器已停止');
    }
    
    // 🔥 显示最终队列统计
    const queueStatus = this.getQueueStatus();
    console.log('📊 Telegram队列最终统计:');
    console.log(`   📤 总发送: ${queueStatus.stats.totalSent} 条`);
    console.log(`   ❌ 总失败: ${queueStatus.stats.totalFailed} 条`);
    console.log(`   ⚠️ 429错误: ${queueStatus.stats.rateLimitHits} 次`);
    console.log(`   🗑️ 队列溢出: ${queueStatus.stats.queueOverflows} 次`);
    console.log(`   📋 剩余队列: ${queueStatus.queueLength} 条`);
  }

  /**
   * 处理webhook更新
   */
  private async handleWebhookUpdate(update: any): Promise<void> {
    console.log('📱 收到Telegram更新:', JSON.stringify(update, null, 2));

    // 处理消息
    if (update.message) {
      const message = update.message;
      const chatId = message.chat.id.toString();
      
      // 自动设置chatId
      if (!this.chatId) {
        this.setChatId(chatId);
      }

      // 处理命令
      if (message.text) {
        await this.handleCommand(chatId, message.text);
      }
    }

    // 处理回调查询（按钮点击）
    if (update.callback_query) {
      const callbackQuery = update.callback_query;
      const chatId = callbackQuery.message.chat.id.toString();
      const data = callbackQuery.data;

      await this.handleCallbackQuery(chatId, data, callbackQuery.id);
    }
  }

  /**
   * 处理命令
   */
  private async handleCommand(chatId: string, text: string): Promise<void> {
    const command = text.trim().toLowerCase();

    switch (command) {
      case '/start':
        await this.sendWelcomeMessage(chatId);
        break;
      case '/status':
        await this.sendStatusWithButtons(chatId);
        break;
      case '/help':
        await this.sendHelpMessage(chatId);
        break;
      default:
        // 对于非命令消息，发送状态面板
        if (!command.startsWith('/')) {
          await this.sendStatusWithButtons(chatId);
        }
        break;
    }
  }

  /**
   * 处理回调查询（按钮点击）
   */
  private async handleCallbackQuery(chatId: string, data: string, queryId: string): Promise<void> {
    console.log(`📱 处理按钮点击: ${data}`);

    // 先回答回调查询
    await this.answerCallbackQuery(queryId);

    if (!this.dataProvider) {
      await this.sendTextMessage(chatId, '❌ 数据提供者未设置，请联系管理员');
      return;
    }

    switch (data) {
      case 'tokens':
        await this.sendTokenList(chatId);
        break;
      case 'positions':
        await this.sendPositions(chatId);
        break;
      case 'stats':
        await this.sendTokenStats(chatId);
        break;
      case 'refresh':
        await this.sendStatusWithButtons(chatId);
        break;
      default:
        await this.sendTextMessage(chatId, `❓ 未知操作: ${data}`);
        break;
    }
  }

  /**
   * 发送欢迎消息
   */
  private async sendWelcomeMessage(chatId: string): Promise<void> {
    const message = `🤖 欢迎使用AI交易机器人！
    
🎯 功能说明：
• 自动监控目标钱包交易
• AI驱动的买卖决策
• 实时通知交易信号
• Paper Trading模式

📱 可用命令：
/start - 显示欢迎消息
/status - 查看状态面板
/help - 查看帮助信息

💡 发送任意消息可快速查看状态`;

    await this.sendTextMessage(chatId, message);
  }

  /**
   * 发送帮助消息
   */
  private async sendHelpMessage(chatId: string): Promise<void> {
    const message = `📖 AI交易机器人帮助

🔧 主要功能：
1️⃣ **Token监控** - 实时跟踪活跃token
2️⃣ **持仓管理** - 查看当前持仓情况  
3️⃣ **交易统计** - 每个token的详细数据

📊 按钮说明：
🪙 **Token列表** - 查看当前监控的所有token
💼 **持仓情况** - 查看当前持有的仓位和盈亏
📈 **交易统计** - 查看每个token的交易数量和收益
🔄 **刷新** - 更新最新状态

⚡ 快速访问：发送任意消息即可显示状态面板`;

    await this.sendTextMessage(chatId, message);
  }

  /**
   * 发送带按钮的状态消息
   */
  public async sendStatusWithButtons(chatId: string): Promise<void> {
    const message = `🤖 AI交易机器人状态面板
    
⏰ 时间: ${new Date().toLocaleString('zh-CN')}
🎯 目标钱包: DDGniHdk...
💰 Paper Trading: ✅ 启用

请选择要查看的信息：`;

    const keyboard = {
      inline_keyboard: [
        [
          { text: '🪙 Token列表', callback_data: 'tokens' },
          { text: '💼 持仓情况', callback_data: 'positions' }
        ],
        [
          { text: '📈 交易统计', callback_data: 'stats' },
          { text: '🔄 刷新', callback_data: 'refresh' }
        ]
      ]
    };

    await this.sendMessageWithKeyboard(chatId, message, keyboard);
  }

  /**
   * 发送Token列表
   */
  private async sendTokenList(chatId: string): Promise<void> {
    if (!this.dataProvider) {
      await this.sendTextMessage(chatId, '❌ 数据提供者未设置');
      return;
    }

    const activeTokens = this.dataProvider.getActiveTokens();
    
    if (activeTokens.length === 0) {
      await this.sendTextMessage(chatId, '📋 当前没有监控的Token');
      return;
    }

    let message = `🪙 当前监控Token列表 (${activeTokens.length}个)\n\n`;
    
    activeTokens.forEach((token, index) => {
      const gmgnLink = `https://gmgn.ai/sol/token/${token}`;
      message += `${index + 1}. \`${token.slice(0, 8)}...${token.slice(-8)}\`\n`;
      message += `   🔗 [GMGN](${gmgnLink})\n\n`;
    });

    message += `⏰ 更新时间: ${new Date().toLocaleString('zh-CN')}`;

    await this.sendTextMessage(chatId, message);
  }

  /**
   * 发送持仓情况
   */
  private async sendPositions(chatId: string): Promise<void> {
    if (!this.dataProvider) {
      await this.sendTextMessage(chatId, '❌ 数据提供者未设置');
      return;
    }

    const positions = this.dataProvider.getCurrentPositions();
    
    if (positions.length === 0) {
      await this.sendTextMessage(chatId, '💼 当前没有持仓');
      return;
    }

    let message = `💼 当前持仓情况 (${positions.length}个)\n\n`;
    let totalPnL = 0;

    positions.forEach((pos, index) => {
      const profitEmoji = pos.pnl > 0 ? '📈' : '📉';
      const profitSign = pos.pnl > 0 ? '+' : '';
      const holdingHours = Math.floor(pos.holdingTime / 60);
      const holdingMins = pos.holdingTime % 60;
      const gmgnLink = `https://gmgn.ai/sol/token/${pos.tokenAddress}`;
      
      totalPnL += pos.pnl;

      message += `${index + 1}. 🪙 \`${pos.tokenAddress.slice(0, 8)}...\`\n`;
      message += `   📦 数量: ${pos.position.toFixed(2)} tokens\n`;
      message += `   💲 买入价: ${pos.buyPrice.toFixed(8)} SOL\n`;
      message += `   💲 当前价: ${pos.currentPrice.toFixed(8)} SOL\n`;
      message += `   ${profitEmoji} 盈亏: ${profitSign}${pos.pnl.toFixed(4)} SOL\n`;
      message += `   ⏱️ 持仓: ${holdingHours}h ${holdingMins}m\n`;
      message += `   🔗 [GMGN](${gmgnLink})\n\n`;
    });

    const totalProfitEmoji = totalPnL > 0 ? '📈' : '📉';
    const totalProfitSign = totalPnL > 0 ? '+' : '';
    message += `${totalProfitEmoji} 总盈亏: ${totalProfitSign}${totalPnL.toFixed(4)} SOL\n`;
    message += `⏰ 更新时间: ${new Date().toLocaleString('zh-CN')}`;

    await this.sendTextMessage(chatId, message);
  }

  /**
   * 发送Token交易统计
   */
  private async sendTokenStats(chatId: string): Promise<void> {
    if (!this.dataProvider) {
      await this.sendTextMessage(chatId, '❌ 数据提供者未设置');
      return;
    }

    const tokenStats = this.dataProvider.getTokenStats();
    const activeTokens = this.dataProvider.getActiveTokens();
    const sellOnlyBuffer = this.dataProvider.getSellOnlyBufferInfo();
    
    let message = `📈 AI交易机器人详细统计\n\n`;
    
    // 📊 监控队列统计
    message += `📋 主监控队列 (${activeTokens.length}/10):\n`;
    if (activeTokens.length === 0) {
      message += `   暂无监控Token\n`;
    } else {
      activeTokens.slice(0, 5).forEach((token, i) => {
        const gmgnLink = `https://gmgn.ai/sol/token/${token}`;
        message += `   ${i + 1}. \`${token.slice(0, 8)}...\` [GMGN](${gmgnLink})\n`;
      });
      if (activeTokens.length > 5) {
        message += `   ... 还有 ${activeTokens.length - 5} 个token\n`;
      }
    }
    message += '\n';
    
    // 🔄 缓冲队列统计
    if (sellOnlyBuffer.length > 0) {
      message += `🔄 缓冲队列 (只卖出不买入) - ${sellOnlyBuffer.length} 个token:\n`;
      sellOnlyBuffer.forEach((buffer, i) => {
        const hasPositionEmoji = buffer.hasPosition ? '💼' : '📊';
        const bufferHours = Math.floor(buffer.bufferDurationMinutes / 60);
        const bufferMins = buffer.bufferDurationMinutes % 60;
        const gmgnLink = `https://gmgn.ai/sol/token/${buffer.tokenAddress}`;
        message += `   ${i + 1}. ${hasPositionEmoji} \`${buffer.tokenAddress.slice(0, 8)}...\`\n`;
        message += `      原因: ${buffer.reason}\n`;
        message += `      时长: ${bufferHours}h ${bufferMins}m\n`;
        message += `      持仓: ${buffer.hasPosition ? '有' : '无'}\n`;
        message += `      🔗 [GMGN](${gmgnLink})\n`;
      });
      message += '\n';
    }
    
    // 📈 交易统计
    if (tokenStats.length === 0) {
      message += `📈 暂无Token交易统计\n`;
    } else {
      message += `📈 Token交易统计 (${tokenStats.length}个):\n`;
      let totalTrades = 0;
      let totalPnL = 0;

      tokenStats.slice(0, 8).forEach((stat, index) => {
        const profitEmoji = stat.totalPnL > 0 ? '📈' : '📉';
        const profitSign = stat.totalPnL > 0 ? '+' : '';
        const gmgnLink = `https://gmgn.ai/sol/token/${stat.tokenAddress}`;
        
        totalTrades += stat.totalTrades;
        totalPnL += stat.totalPnL;

        message += `   ${index + 1}. 🪙 \`${stat.tokenAddress.slice(0, 8)}...\`\n`;
        message += `      🔄 交易: ${stat.totalTrades}笔 | 胜率: ${(stat.winRate * 100).toFixed(1)}%\n`;
        message += `      ${profitEmoji} 盈亏: ${profitSign}${stat.totalPnL.toFixed(4)} SOL\n`;
        message += `      🔗 [GMGN](${gmgnLink})\n`;
      });
      
      if (tokenStats.length > 8) {
        message += `   ... 还有 ${tokenStats.length - 8} 个token\n`;
      }

      const totalProfitEmoji = totalPnL > 0 ? '📈' : '📉';
      const totalProfitSign = totalPnL > 0 ? '+' : '';
      message += `\n📊 总计: ${totalTrades} 笔交易\n`;
      message += `${totalProfitEmoji} 总盈亏: ${totalProfitSign}${totalPnL.toFixed(4)} SOL\n`;
    }
    
    message += `\n⏰ 更新时间: ${new Date().toLocaleString('zh-CN')}`;

    await this.sendTextMessage(chatId, message);
  }

  /**
   * 发送带键盘的消息
   */
  private async sendMessageWithKeyboard(chatId: string, text: string, keyboard: any): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const url = `https://api.telegram.org/bot${this.botToken}/sendMessage`;
      
      const payload = {
        chat_id: chatId,
        text: text,
        parse_mode: 'Markdown',
        disable_web_page_preview: true,
        reply_markup: keyboard
      };

      const response = await axios.post(url, payload, {
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.data.ok) {
        console.log(`📱 带按钮的消息已发送`);
      } else {
        console.error('📱 发送带按钮消息失败:', response.data);
      }

    } catch (error) {
      console.error('📱 发送带按钮消息错误:', error);
    }
  }

  /**
   * 发送纯文本消息
   */
  private async sendTextMessage(chatId: string, text: string): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const url = `https://api.telegram.org/bot${this.botToken}/sendMessage`;
      
      const payload = {
        chat_id: chatId,
        text: text,
        parse_mode: 'Markdown',
        disable_web_page_preview: true
      };

      const response = await axios.post(url, payload, {
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.data.ok) {
        console.log(`📱 文本消息已发送`);
      } else {
        console.error('📱 发送文本消息失败:', response.data);
      }

    } catch (error) {
      console.error('📱 发送文本消息错误:', error);
    }
  }

  /**
   * 回答回调查询
   */
  private async answerCallbackQuery(queryId: string, text?: string): Promise<void> {
    try {
      const url = `https://api.telegram.org/bot${this.botToken}/answerCallbackQuery`;
      
      const payload = {
        callback_query_id: queryId,
        text: text || '✅'
      };

      await axios.post(url, payload, {
        timeout: 5000,
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('📱 回答回调查询错误:', error);
    }
  }

  /**
   * 设置聊天ID（可以通过环境变量或者第一次发送消息时获取）
   */
  public setChatId(chatId: string): void {
    this.chatId = chatId;
    console.log(`📱 设置Chat ID: ${chatId}`);
  }

  /**
   * 发送订阅通知
   */
  public async notifySubscription(tokenAddress: string, action: 'subscribe' | 'unsubscribe'): Promise<void> {
    const message = `🔔 ${action === 'subscribe' ? '新增订阅' : '取消订阅'}\n` +
                   `🪙 Token: \`${tokenAddress.slice(0, 8)}...${tokenAddress.slice(-8)}\`\n` +
                   `⏰ 时间: ${new Date().toLocaleString('zh-CN')}`;

    await this.sendNotification({
      type: 'subscription',
      tokenAddress,
      message,
      data: {
        timestamp: new Date()
      }
    }, 3); // 🔥 低优先级：订阅通知
  }

  /**
   * 发送买入通知 - 增强版，包含模拟交易详情
   */
  public async notifyBuy(
    tokenAddress: string,
    solAmount: number,
    tokenAmount: number,
    price: number,
    prediction: number,
    transactionId?: string,
    slippage?: number,
    gasFee?: number,
    tokenSymbol?: string
  ): Promise<boolean> {
    const gmgnLink = `https://gmgn.ai/sol/token/${tokenAddress}`;
    
    // 🔥 添加AI预测调试信息
    console.log(`🔍 Telegram买入通知 - AI预测调试:`);
    console.log(`   AI预测值: ${(prediction * 100).toFixed(1)}% (应该在50-100%范围内)`);
    
    // 🔥 修复价格计算 - price应该是SOL/token，需要正确转换
    console.log(`🔍 Telegram买入通知 - 价格计算调试:`);
    console.log(`   输入价格: ${price.toFixed(12)} SOL/token`);
    console.log(`   SOL投入: ${solAmount.toFixed(6)} SOL`);
    console.log(`   Token获得: ${tokenAmount.toFixed(2)} 个`);
    
    // 验证价格计算是否合理
    const calculatedPrice = tokenAmount > 0 ? solAmount / tokenAmount : 0;
    console.log(`   计算验证价格: ${calculatedPrice.toFixed(12)} SOL/token`);
    console.log(`   价格差异: ${Math.abs(price - calculatedPrice).toFixed(12)} SOL/token`);
    
    // 使用更准确的价格（优先使用计算出的价格）
    const actualPrice = calculatedPrice > 0 ? calculatedPrice : price;
    
    // Convert SOL price to USD price
    const priceService = PriceService.getInstance();
    const usdPrice = await priceService.convertTokenPriceToUsd(actualPrice);
    const formattedUsdPrice = priceService.formatUsdPrice(usdPrice);
    
    console.log(`   最终使用价格: ${actualPrice.toFixed(12)} SOL/token`);
    console.log(`   USD转换价格: ${usdPrice.toFixed(12)} USD/token`);
    console.log(`   格式化价格: ${formattedUsdPrice}`);
    
    const tokenDisplay = tokenSymbol ? `${tokenSymbol} (${tokenAddress.slice(0, 8)}...${tokenAddress.slice(-8)})` : `${tokenAddress.slice(0, 8)}...${tokenAddress.slice(-8)}`;
    
    const message = `🚀 买入交易执行\n\n` +
      `🪙 Token: ${tokenDisplay}\n` +
      `💵 投入SOL: ${solAmount.toFixed(4)} SOL\n` +
      `📦 获得Token: ${tokenAmount.toFixed(0)} 个\n` +
      `💲 买入价格: ${formattedUsdPrice}/token\n` +
      `🤖 AI预测: ${(prediction * 100).toFixed(1)}%\n` +
      `⏰ 时间: ${new Date().toLocaleString()}\n` +
      (transactionId ? `📋 交易签名: ${transactionId}\n✅ 状态: 已确认\n` : '') +
      (slippage ? `📈 实际滑点: ${(slippage * 100).toFixed(2)}%\n` : '') +
      (gasFee ? `⛽ Gas费用: ${gasFee.toFixed(8)} SOL\n` : '') +
      `🔗 GMGN链接: ${gmgnLink}`;

    return this.sendNotification({
      type: 'buy',
      tokenAddress,
      message,
      data: {
        buyAmount: tokenAmount,
        buySolAmount: solAmount,
        buyPrice: actualPrice, // 使用修正后的价格
        prediction,
        timestamp: new Date()
      }
    }, 2); // 🔥 中优先级：买入交易通知
  }

  /**
   * 发送卖出通知 - 增强版，包含模拟交易详情和盈亏计算
   */
  public async notifySell(
    tokenAddress: string,
    tokenAmount: number,
    solReceived: number,
    sellPrice: number,
    buyPrice: number,
    originalSolAmount: number,
    holdingTimeMinutes: number,
    prediction: number,
    transactionId?: string,
    slippage?: number,
    gasFee?: number,
    tokenSymbol?: string
  ): Promise<boolean> {
    const profit = solReceived - originalSolAmount;
    const profitPercentage = originalSolAmount > 0 ? ((solReceived / originalSolAmount - 1) * 100) : 0;
    
    // 🔥 修复价格计算 - 重新计算实际价格
    console.log(`🔍 Telegram卖出通知 - 原始数据调试:`);
    console.log(`   输入卖出价格: ${sellPrice.toFixed(12)} SOL/token`);
    console.log(`   输入买入价格: ${buyPrice.toFixed(12)} SOL/token`);
    console.log(`   Token数量: ${tokenAmount.toFixed(2)} 个`);
    console.log(`   收到SOL: ${solReceived.toFixed(9)} SOL`);
    console.log(`   原始投入: ${originalSolAmount.toFixed(9)} SOL`);
    
    // 重新计算实际价格
    const actualSellPrice = tokenAmount > 0 ? solReceived / tokenAmount : sellPrice;
    const actualBuyPrice = tokenAmount > 0 ? originalSolAmount / tokenAmount : buyPrice;
    
    console.log(`   计算卖出价格: ${actualSellPrice.toFixed(12)} SOL/token`);
    console.log(`   计算买入价格: ${actualBuyPrice.toFixed(12)} SOL/token`);
    
    // 使用更准确的价格
    const finalSellPrice = actualSellPrice > 0 ? actualSellPrice : sellPrice;
    const finalBuyPrice = actualBuyPrice > 0 ? actualBuyPrice : buyPrice;
    
    // 🔥 修复价格变化计算，处理极小价格和异常情况
    let priceChangePercentage = 0;
    if (finalBuyPrice > 0 && finalSellPrice >= 0) {
      priceChangePercentage = ((finalSellPrice / finalBuyPrice - 1) * 100);
      // 限制显示范围，避免极端数值
      if (Math.abs(priceChangePercentage) > 99999) {
        priceChangePercentage = priceChangePercentage > 0 ? 99999 : -99999;
      }
    } else if (finalBuyPrice > 0 && finalSellPrice === 0) {
      priceChangePercentage = -100; // 价格归零
    } else if (finalBuyPrice === 0 && finalSellPrice > 0) {
      priceChangePercentage = 99999; // 从零开始涨价
    }
    
    const holdingHours = Math.floor(holdingTimeMinutes / 60);
    const holdingMins = holdingTimeMinutes % 60;
    
    const profitEmoji = profit >= 0 ? '📈' : '📉';
    const profitSign = profit >= 0 ? '+' : '';
    const gmgnLink = `https://gmgn.ai/sol/token/${tokenAddress}`;

    // Convert SOL prices to USD prices
    const priceService = PriceService.getInstance();
    
    // 获取当前SOL价格和转换USD价格
    const currentSolPrice = await priceService.getSolUsdPrice();
    const sellUsdPrice = await priceService.convertTokenPriceToUsd(finalSellPrice);
    const buyUsdPrice = await priceService.convertTokenPriceToUsd(finalBuyPrice);
    const formattedSellUsdPrice = priceService.formatUsdPrice(sellUsdPrice);
    const formattedBuyUsdPrice = priceService.formatUsdPrice(buyUsdPrice);
    
    // 🔥 强制显示价格计算调试信息（不受环境变量控制）
    console.log(`🔍 Telegram卖出通知 - 价格计算调试:`);
    console.log(`   📊 修正后的价格:`);
    console.log(`     - 最终卖出价格: ${finalSellPrice.toFixed(12)} SOL/token`);
    console.log(`     - 最终买入价格: ${finalBuyPrice.toFixed(12)} SOL/token`);
    console.log(`   💱 价格转换:`);
    console.log(`     - 当前SOL价格: $${currentSolPrice.toFixed(2)}`);
    console.log(`     - 卖出USD价格: $${sellUsdPrice.toFixed(12)} -> ${formattedSellUsdPrice}`);
    console.log(`     - 买入USD价格: $${buyUsdPrice.toFixed(12)} -> ${formattedBuyUsdPrice}`);
    console.log(`   📈 计算验证:`);
    console.log(`     - 价格变化计算: (${finalSellPrice.toFixed(12)} / ${finalBuyPrice.toFixed(12)} - 1) × 100 = ${priceChangePercentage.toFixed(2)}%`);
    console.log(`     - 盈亏百分比计算: (${solReceived.toFixed(9)} / ${originalSolAmount.toFixed(9)} - 1) × 100 = ${profitPercentage.toFixed(2)}%`);
    
    // 🔥 特殊情况警告
    if (solReceived < 0.001) {
      console.warn(`⚠️ 收到的SOL数量极少 (${solReceived.toFixed(9)} SOL)，可能是：`);
      console.warn(`   1. Token价值极低或已归零`);
      console.warn(`   2. 卖出数量计算错误`);
      console.warn(`   3. 滑点过大导致收益微乎其微`);
    }
    
    if (finalSellPrice < 0.000000001) {
      console.warn(`⚠️ 卖出价格极低 (${finalSellPrice.toExponential(4)} SOL/token)，可能是：`);
      console.warn(`   1. Token已基本归零`);
      console.warn(`   2. 卖出了大量低价值token`);
      console.warn(`   3. 市场流动性不足`);
    }

    const tokenDisplay = tokenSymbol ? `${tokenSymbol} (${tokenAddress.slice(0, 8)}...${tokenAddress.slice(-8)})` : `${tokenAddress.slice(0, 8)}...${tokenAddress.slice(-8)}`;
    
    // 🔥 修复持仓时间显示
    const holdingTimeDisplay = holdingHours > 0 ? 
      `${holdingHours}h ${holdingMins.toFixed(1)}m` : 
      `${holdingTimeMinutes.toFixed(1)}m`;
    
    const message = `${profitEmoji} 卖出交易执行\n\n` +
      `🪙 Token: ${tokenDisplay}\n` +
      `📦 卖出Token: ${tokenAmount.toFixed(0)} 个\n` +
      `💵 收到SOL: ${solReceived < 0.001 ? solReceived.toExponential(4) : solReceived.toFixed(4)} SOL\n` +
      `💲 卖出价格: ${formattedSellUsdPrice}/token\n` +
      `💲 买入价格: ${formattedBuyUsdPrice}/token\n` +
      `📊 价格变化: ${priceChangePercentage >= 99999 ? '+99999' : priceChangePercentage <= -99999 ? '-99999' : priceChangePercentage.toFixed(2)}%\n` +
      `💰 盈亏: ${profitSign}${profit < 0.001 && profit > -0.001 ? profit.toExponential(4) : profit.toFixed(4)} SOL (${profitSign}${profitPercentage.toFixed(2)}%)\n` +
      `⏱️ 持仓时间: ${holdingTimeDisplay}\n` +
      `🤖 AI预测: ${(prediction * 100).toFixed(1)}%\n` +
      `⏰ 时间: ${new Date().toLocaleString()}\n` +
      (transactionId ? `📋 交易签名: ${transactionId}\n✅ 状态: 已确认\n` : '') +
      (slippage ? `📈 实际滑点: ${(slippage * 100).toFixed(2)}%\n` : '') +
      (gasFee ? `⛽ Gas费用: ${gasFee.toFixed(8)} SOL\n` : '') +
      `🔗 GMGN链接: ${gmgnLink}`;

    return this.sendNotification({
      type: 'sell',
      tokenAddress,
      message,
      data: {
        sellAmount: tokenAmount,
        sellSolAmount: solReceived,
        sellPrice: finalSellPrice, // 使用修正后的价格
        buyPrice: finalBuyPrice,   // 使用修正后的价格
        profit,
        profitPercentage,
        holdingTime: holdingTimeMinutes,
        prediction,
        timestamp: new Date()
      }
    }, 2); // 🔥 中优先级：卖出交易通知
  }

  /**
   * 发送清仓通知
   */
  public async notifyLiquidation(
    tokenAddress: string,
    reason: string,
    tokenAmount: number,
    solReceived: number,
    profit: number
  ): Promise<void> {
    const profitEmoji = profit > 0 ? '📈' : '📉';
    const profitSign = profit > 0 ? '+' : '';
    const gmgnLink = `https://gmgn.ai/sol/token/${tokenAddress}`;

    const message = `🔴 强制清仓\n` +
                   `🪙 Token: \`${tokenAddress.slice(0, 8)}...${tokenAddress.slice(-8)}\`\n` +
                   `📝 原因: ${reason}\n` +
                   `🎯 清仓: ${tokenAmount.toFixed(2)} tokens\n` +
                   `💰 收到: ${solReceived.toFixed(6)} SOL\n` +
                   `${profitEmoji} 盈亏: ${profitSign}${profit.toFixed(8)} SOL\n` +
                   `🔗 GMGN链接: ${gmgnLink}\n` +
                   `⏰ 时间: ${new Date().toLocaleString('zh-CN')}`;

    await this.sendNotification({
      type: 'liquidation',
      tokenAddress,
      message,
      data: {
        sellSolAmount: solReceived,
        profit,
        timestamp: new Date()
      }
    }, 1); // 🔥 高优先级：清仓通知
  }

  /**
   * 发送系统通知
   */
  public async notifySystem(message: string, data?: any): Promise<void> {
    const formattedMessage = `🤖 系统通知\n${message}\n⏰ ${new Date().toLocaleString('zh-CN')}`;

    await this.sendNotification({
      type: 'system',
      message: formattedMessage,
      data: {
        ...data,
        timestamp: new Date()
      }
    }, 1); // 🔥 高优先级：系统通知
  }

  /**
   * 发送每日统计
   */
  public async notifyDailyStats(stats: {
    totalTrades: number;
    totalOperations?: number;
    winningTrades: number;
    losingTrades: number;
    totalProfit: number;
    winRate: number;
    activeTokens: number;
  }): Promise<void> {
    const profitEmoji = stats.totalProfit > 0 ? '📈' : '📉';
    const profitSign = stats.totalProfit > 0 ? '+' : '';

    let message = `📊 每日交易统计\n`;
    
    if (stats.totalOperations !== undefined) {
      message += `🔄 完成交易对: ${stats.totalTrades} 对 (买入→卖出)\n`;
      message += `📋 总操作数: ${stats.totalOperations} 笔 (所有买入+卖出)\n`;
    } else {
      message += `🎯 总交易: ${stats.totalTrades} 笔\n`;
    }
    
    message += `✅ 盈利: ${stats.winningTrades} 笔\n` +
               `❌ 亏损: ${stats.losingTrades} 笔\n` +
               `📈 胜率: ${stats.winRate.toFixed(1)}%\n` +
               `${profitEmoji} 总盈亏: ${profitSign}${stats.totalProfit.toFixed(6)} SOL\n` +
               `🪙 活跃Token: ${stats.activeTokens} 个\n` +
               `📅 日期: ${new Date().toLocaleDateString('zh-CN')}`;

    await this.sendNotification({
      type: 'system',
      message,
      data: {
        ...stats,
        timestamp: new Date()
      }
    }, 3); // 🔥 低优先级：统计报告
  }

  /**
   * 通用发送方法 - 🔥 修改为使用队列系统
   */
  private async sendNotification(notification: TradingNotification, priority: number = 2): Promise<boolean> {
    if (!this.isEnabled) {
      console.log('📱 Telegram未启用，跳过通知');
      return false;
    }

    // 如果没有设置chatId，尝试获取
    let targetChatId = this.chatId;
    
    if (!targetChatId) {
      targetChatId = await this.getDefaultChatId();
    }

    if (!targetChatId) {
      console.log('📱 未设置Chat ID，无法发送Telegram通知');
      return false;
    }

    // 🔥 使用队列系统发送消息
    const messageId = this.addToQueue(notification, targetChatId, priority);
    console.log(`📱 Telegram通知已加入队列: ${notification.type} (${messageId.slice(0, 12)}...)`);
    
    return true; // 返回true表示已成功加入队列
  }

  /**
   * 尝试获取默认的Chat ID
   */
  private async getDefaultChatId(): Promise<string | null> {
    try {
      console.log('📱 正在获取Telegram更新...');
      const url = `https://api.telegram.org/bot${this.botToken}/getUpdates`;
      const response = await axios.get(url, { timeout: 5000 });
      
      console.log(`📱 获取到 ${response.data.result?.length || 0} 条更新`);
      
      if (response.data.ok && response.data.result.length > 0) {
        // 获取最近的消息中的chat ID
        const latestUpdate = response.data.result[response.data.result.length - 1];
        const chatId = latestUpdate.message?.chat?.id || latestUpdate.channel_post?.chat?.id;
        
        if (chatId) {
          this.chatId = chatId.toString();
          console.log(`📱 自动获取到Chat ID: ${this.chatId}`);
          console.log(`📱 用户信息: ${latestUpdate.message?.chat?.first_name || 'Unknown'} (${latestUpdate.message?.chat?.username || 'No username'})`);
          return this.chatId;
        } else {
          console.log('📱 更新中未找到有效的Chat ID');
          console.log('💡 请先向bot发送任意消息，例如 /start');
        }
      } else {
        console.log('📱 暂无Telegram更新');
        console.log('💡 请先向bot发送任意消息，例如 /start');
      }
      
      return null;
    } catch (error) {
      console.error('📱 获取Chat ID失败:', error);
      return null;
    }
  }

  /**
   * 测试连接
   */
  public async testConnection(): Promise<boolean> {
    if (!this.isEnabled) {
      console.log('📱 Telegram服务未启用');
      return false;
    }

    try {
      console.log('📱 测试bot连接...');
      const url = `https://api.telegram.org/bot${this.botToken}/getMe`;
      const response = await axios.get(url, { timeout: 5000 });
      
      if (response.data.ok) {
        const botInfo = response.data.result;
        console.log(`📱 Telegram连接成功: @${botInfo.username} (${botInfo.first_name})`);
        
        // 如果没有Chat ID，尝试获取
        if (!this.chatId) {
          console.log('📱 尝试自动获取Chat ID...');
          await this.getDefaultChatId();
        }
        
        return true;
      } else {
        console.error('📱 Telegram连接失败:', response.data);
        return false;
      }
    } catch (error) {
      console.error('📱 Telegram测试连接错误:', error);
      if (axios.isAxiosError(error)) {
        console.error('📱 错误详情:', {
          status: error.response?.status,
          message: error.message,
          code: error.code
        });
      }
      return false;
    }
  }

  /**
   * 设置Bot菜单命令
   */
  private async setBotCommands(): Promise<void> {
    if (!this.isEnabled) return;

    try {
      const url = `https://api.telegram.org/bot${this.botToken}/setMyCommands`;
      
      const commands = [
        {
          command: 'start',
          description: '🤖 启动机器人，显示欢迎信息'
        },
        {
          command: 'status',
          description: '📊 查看交易统计和状态面板'
        },
        {
          command: 'help',
          description: '❓ 查看帮助信息和使用说明'
        }
      ];

      const payload = {
        commands: commands
      };

      const response = await axios.post(url, payload, {
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.data.ok) {
        console.log('📱 Bot菜单命令设置成功');
      } else {
        console.error('📱 设置Bot菜单命令失败:', response.data);
      }

    } catch (error) {
      console.error('📱 设置Bot菜单命令错误:', error);
    }
  }
} 