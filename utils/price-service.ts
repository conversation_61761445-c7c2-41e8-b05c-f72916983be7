import { Connection, PublicKey } from '@solana/web3.js';
import axios from 'axios';

export class PriceService {
  private static instance: PriceService;
  private connection: Connection;
  private lastSolPrice: number = 0;
  private lastPriceUpdateTime: number = 0;
  private readonly PRICE_CACHE_DURATION = 30000; // 30 seconds cache
  
  // SOL price feed ID from Pyth Network
  private readonly SOL_PRICE_FEED_ID = 'ef0d8b6fda2ceba41da15d4095d1da392a0d2f8ed0c6c7bc0f4cfac8c280b56d';

  private constructor() {
    // Initialize connection to Solana mainnet
    this.connection = new Connection(
      process.env.RPC_URL || 'https://api.mainnet-beta.solana.com',
      'confirmed'
    );
  }

  public static getInstance(): PriceService {
    if (!PriceService.instance) {
      PriceService.instance = new PriceService();
    }
    return PriceService.instance;
  }

  /**
   * Get current SOL/USD price with multiple fallback methods
   */
  public async getSolUsdPrice(): Promise<number> {
    const now = Date.now();
    
    // Return cached price if still valid
    if (this.lastSolPrice > 0 && (now - this.lastPriceUpdateTime) < this.PRICE_CACHE_DURATION) {
      return this.lastSolPrice;
    }

    try {
      // Try Pyth Hermes API first (faster and more reliable)
      const price = await this.fetchFromPythHermes();
      if (price > 0) {
        this.lastSolPrice = price;
        this.lastPriceUpdateTime = now;
        console.log(`🔮 SOL/USD价格更新 (Pyth): $${price.toFixed(2)}`);
        return price;
      }
    } catch (error) {
      console.warn('🔮 Pyth Hermes API失败，尝试备用方案:', error);
    }

    try {
      // Fallback to CoinGecko API
      const price = await this.fetchFromCoinGecko();
      if (price > 0) {
        this.lastSolPrice = price;
        this.lastPriceUpdateTime = now;
        console.log(`🔮 SOL/USD价格更新 (CoinGecko): $${price.toFixed(2)}`);
        return price;
      }
    } catch (error) {
      console.warn('🔮 CoinGecko API失败:', error);
    }

    // Final fallback to default price
    const defaultPrice = this.getDefaultSolPrice();
    console.warn(`🔮 使用默认SOL价格: $${defaultPrice.toFixed(2)}`);
    return defaultPrice;
  }

  /**
   * Fetch SOL price from Pyth Hermes API
   */
  private async fetchFromPythHermes(): Promise<number> {
    const response = await axios.get(
      `https://hermes.pyth.network/v2/updates/price/latest?ids[]=${this.SOL_PRICE_FEED_ID}`,
      { timeout: 5000 }
    );

    if (response.data && response.data.parsed && response.data.parsed.length > 0) {
      const priceData = response.data.parsed[0];
      if (priceData.price && priceData.price.price) {
        // Pyth prices include an exponent, so we need to adjust
        const price = parseFloat(priceData.price.price);
        const expo = priceData.price.expo;
        return price * Math.pow(10, expo);
      }
    }

    throw new Error('Invalid Pyth response format');
  }

  /**
   * Fetch SOL price from CoinGecko API as fallback
   */
  private async fetchFromCoinGecko(): Promise<number> {
    const response = await axios.get(
      'https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd',
      { timeout: 5000 }
    );

    if (response.data && response.data.solana && response.data.solana.usd) {
      return parseFloat(response.data.solana.usd);
    }

    throw new Error('Invalid CoinGecko response format');
  }

  /**
   * Get default SOL price as final fallback
   */
  private getDefaultSolPrice(): number {
    // Return a reasonable default SOL price (update this periodically)
    return 240.0; // $240 USD per SOL
  }

  /**
   * Convert SOL price to USD price
   */
  public async convertSolToUsd(solAmount: number): Promise<number> {
    const solUsdPrice = await this.getSolUsdPrice();
    return solAmount * solUsdPrice;
  }

  /**
   * Convert SOL/token price to USD/token price
   */
  public async convertTokenPriceToUsd(solPerToken: number): Promise<number> {
    const solUsdPrice = await this.getSolUsdPrice();
    return solPerToken * solUsdPrice;
  }

  /**
   * Format USD price with appropriate decimal places
   */
  public formatUsdPrice(usdPrice: number): string {
    if (usdPrice >= 1) {
      return `$${usdPrice.toFixed(4)}`;
    } else if (usdPrice >= 0.01) {
      return `$${usdPrice.toFixed(6)}`;
    } else if (usdPrice >= 0.000001) {
      return `$${usdPrice.toFixed(8)}`;
    } else if (usdPrice >= 0.000000001) {
      return `$${usdPrice.toFixed(12)}`;
    } else if (usdPrice > 0) {
      // 🔥 对于极小的价格，使用科学计数法
      return `$${usdPrice.toExponential(4)}`;
    } else {
      return `$0.00000000`;
    }
  }

  /**
   * Get cached SOL price without making new requests
   */
  public getCachedSolPrice(): number {
    return this.lastSolPrice > 0 ? this.lastSolPrice : this.getDefaultSolPrice();
  }

  /**
   * Manual price update method for testing
   */
  public async updatePrice(): Promise<number> {
    this.lastPriceUpdateTime = 0; // Force refresh
    return await this.getSolUsdPrice();
  }
}

export default PriceService; 