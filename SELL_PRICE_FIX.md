# 卖出交易价格和SOL计算修复

## 问题描述

用户报告卖出交易中的价格和收到SOL都是错误的，导致盈亏计算不准确。具体表现为：

1. 卖出价格显示错误
2. 收到的SOL数量不准确
3. 盈亏计算基于错误的数据

## 问题分析

通过代码分析，发现以下几个关键问题：

### 1. `extractSolReceivedFromSellTransaction` 方法问题
- **原问题**: 只是简单计算余额变化，没有考虑交易费用
- **影响**: 导致收到SOL数量不准确

### 2. `extractTokenAmountFromTransaction` 方法问题
- **原问题**: 对于卖出交易，错误地查找用户接收的Token，而不是发出的Token
- **影响**: 无法正确提取实际卖出的Token数量

### 3. 价格计算逻辑问题
- **原问题**: 使用预估值而不是从链上提取的真实数据
- **影响**: 价格计算不准确

## 修复方案

### 1. 修复SOL提取逻辑

```typescript
// 修复前：简单余额变化
const solReceived = (postBalance - preBalance) / 1e9;

// 修复后：考虑交易费用和WSOL账户
const transactionFee = transaction.meta.fee || 0;
const netSolChange = (postBalance - preBalance + transactionFee) / 1e9;

// 优先检查WSOL账户变化
// 检查innerInstructions中的转账记录
// 使用多种方法验证SOL收入
```

### 2. 修复Token数量提取逻辑

```typescript
// 修复前：查找用户接收的Token
if (transfer.destination === userPublicKey.toString()) {
  return amount;
}

// 修复后：查找用户发出的Token（卖出）
const sourceOwner = info.authority || info.source;
if (sourceOwner === userPublicKey.toString() && amount > 0) {
  return amount;
}

// 余额变化计算也修复为减少量
const difference = preAmount - postAmount; // 卖出时是余额减少
```

### 3. 改进价格计算流程

```typescript
// 新增：从交易中提取实际卖出的Token数量
const extractedTokens = this.extractTokenAmountFromTransaction(transaction, tokenAddress, wallet.publicKey);

// 优先使用提取的真实数据
if (extractedSol > 0) {
  actualSolReceived = extractedSol;
}

if (extractedTokens > 0) {
  actualTokensSold = extractedTokens;
}

// 重新计算真实执行价格
if (actualSolReceived > 0 && actualTokensSold > 0) {
  realExecutionPrice = actualSolReceived / actualTokensSold;
}
```

### 4. 增强调试功能

添加了 `debugSellPriceCalculation` 方法来帮助诊断价格计算问题：

```typescript
public async debugSellPriceCalculation(tokenAddress: string): Promise<void>
```

该方法提供：
- 持仓信息分析
- 池子数据检查
- 价格计算模拟
- 盈亏预估
- 真实余额验证
- 调试建议

## 修复效果

### 1. 更准确的SOL收入计算
- 考虑交易费用
- 检查WSOL账户变化
- 验证innerInstructions中的转账记录

### 2. 正确的Token数量提取
- 识别用户发出的Token转账
- 正确计算余额减少量
- 支持多种Token转账格式

### 3. 真实的价格计算
- 使用链上实际数据
- 避免预估值的误差
- 提供详细的计算日志

### 4. 完善的调试工具
- 全面的价格计算分析
- 实时数据验证
- 问题诊断建议

## 使用方法

### 调试卖出价格问题
```typescript
// 在交易系统中调用
await tradingInstance.debugSellPriceCalculation(tokenAddress);
```

### 查看详细日志
修复后的代码会输出详细的调试信息：
- SOL余额变化分析
- Token转账记录
- 价格计算过程
- 真实数据提取结果

## 注意事项

1. **交易费用处理**: 新的逻辑正确处理了Solana交易费用的扣除
2. **WSOL账户**: 特别处理了WSOL账户的变化，这是DEX交易的常见模式
3. **多重验证**: 使用多种方法验证数据的准确性
4. **向后兼容**: 保持了原有接口的兼容性

## 测试建议

1. 监控下一次卖出交易的日志输出
2. 验证Telegram通知中的数据准确性
3. 使用调试方法分析价格计算过程
4. 对比修复前后的数据差异

通过这些修复，卖出交易的价格和SOL计算应该会更加准确，从而提供正确的盈亏信息。 