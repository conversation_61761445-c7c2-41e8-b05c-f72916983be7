import "dotenv/config";
import Client, {
  CommitmentLevel,
  SubscribeRequestAccountsDataSlice,
  SubscribeRequestFilterAccounts,
  SubscribeRequestFilterBlocks,
  SubscribeRequestFilterBlocksMeta,
  SubscribeRequestFilterEntry,
  SubscribeRequestFilterSlots,
  SubscribeRequestFilterTransactions,
} from "@triton-one/yellowstone-grpc";
import { PublicKey, VersionedTransactionResponse } from "@solana/web3.js";
import { Idl } from "@coral-xyz/anchor";
import { SolanaParser } from "@shyft-to/solana-transaction-parser";
import { SubscribeRequestPing } from "@triton-one/yellowstone-grpc/dist/types/grpc/geyser";
import { TransactionFormatter } from "./utils/transaction-formatter";
import { SolanaEventParser } from "./utils/event-parser";
import { bnLayoutFormatter } from "./utils/bn-layout-formatter";
import pumpFunAmmIdl from "./idls/pump_amm_0.1.0.json";
import { DynamicSubscriptionManager } from './dynamic-subscription-manager';
import { TradingBot } from './trading-bot';
import { TradingSystemConfig, PartialTradingSystemConfig, loadConfig, validateConfig, showConfigSummary } from './trading-config';
import { TransactionData } from './predictors/buy-predictor';
import { BuyPredictor } from "./predictors/buy-predictor";
import { SellPredictor } from "./predictors/sell-predictor";
import { PumpSwapManager } from "./trading/pump-swap-manager";
import { TelegramNotifier, TradingDataProvider } from "./utils/telegram-notifier";
import PriceService from "./utils/price-service";

interface SubscribeRequest {
  accounts: { [key: string]: SubscribeRequestFilterAccounts };
  slots: { [key: string]: SubscribeRequestFilterSlots };
  transactions: { [key: string]: SubscribeRequestFilterTransactions };
  transactionsStatus: { [key: string]: SubscribeRequestFilterTransactions };
  blocks: { [key: string]: SubscribeRequestFilterBlocks };
  blocksMeta: { [key: string]: SubscribeRequestFilterBlocksMeta };
  entry: { [key: string]: SubscribeRequestFilterEntry };
  commitment?: CommitmentLevel | undefined;
  accountsDataSlice: SubscribeRequestAccountsDataSlice[];
  ping?: SubscribeRequestPing | undefined;
}

// 常量初始化
const TXN_FORMATTER = new TransactionFormatter();
const PUMP_FUN_AMM_PROGRAM_ID = new PublicKey("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");
const PUMP_FUN_IX_PARSER = new SolanaParser([]);
PUMP_FUN_IX_PARSER.addParserFromIdl(PUMP_FUN_AMM_PROGRAM_ID.toBase58(), pumpFunAmmIdl as Idl);
const PUMP_FUN_EVENT_PARSER = new SolanaEventParser([], console);
PUMP_FUN_EVENT_PARSER.addParserFromIdl(PUMP_FUN_AMM_PROGRAM_ID.toBase58(), pumpFunAmmIdl as Idl);

// 配置
const TARGET_WALLET = process.env.TARGET_WALLET || '8J5GUAf7hr3LTPHJSkwrKFDNJPtAXtLHhnNHq6XxTLrW';
const WINDOW_HOURS = parseFloat(process.env.WINDOW_HOURS || '1');
const UPDATE_INTERVAL_MS = parseInt(process.env.UPDATE_INTERVAL_MS || '60000');

// 最小交易金额阈值 - 过滤小额交易
const MIN_TRANSACTION_SOL = parseFloat(process.env.MIN_TRANSACTION_SOL || '0.0005');

// Token监控上限 - FIFO队列，新token进来踢掉旧的
const MAX_MONITORED_TOKENS = parseInt(process.env.MAX_MONITORED_TOKENS || '10');

// Paper Trading 配置 - 从环境变量读取
const PAPER_TRADING_CONFIG: PartialTradingSystemConfig = {
  trading: {
    tradeAmountSol: parseFloat(process.env.TRADE_AMOUNT_SOL || '0.05'),
    initialCapitalSol: parseFloat(process.env.INITIAL_CAPITAL_SOL || '2.0'),
    maxPositions: parseInt(process.env.MAX_POSITIONS || '3'),
    paperTrading: true, // 强制启用paper trading
    slippageTolerance: parseFloat(process.env.SLIPPAGE_TOLERANCE || '0.03')
  },
  
  risk: {
    stopLossPercentage: parseFloat(process.env.STOP_LOSS_PERCENTAGE || '0.08'),
    takeProfitPercentage: parseFloat(process.env.TAKE_PROFIT_PERCENTAGE || '0.15'),
    maxDailyLossSol: parseFloat(process.env.MAX_DAILY_LOSS_SOL || '0.5'),
    maxTotalLossSol: parseFloat(process.env.MAX_TOTAL_LOSS_SOL || '1.0'),
  },

  models: {
    buyThreshold: parseFloat(process.env.BUY_THRESHOLD || '0.3'), // 30%买入阈值，更容易触发
    sellThreshold: parseFloat(process.env.SELL_THRESHOLD || '0.3'), // 30%卖出阈值，更容易触发
  },

  monitoring: {
    targetTokenAddresses: ['So11111111111111111111111111111111111111112'], // 占位符地址，用于通过验证
    logLevel: 'info' as const,
    enableDetailedLogs: true,
    statsUpdateIntervalSeconds: 30
  },

  grpc: {
    endpoint: process.env.ENDPOINT || 'https://solana-yellowstone-grpc.publicnode.com:443',
    token: process.env.X_TOKEN
  }
};

// AI配置常量
const AI_CONFIG = {
  predictionInterval: parseInt(process.env.PREDICTION_INTERVAL || '30000'), // AI预测间隔：每30秒对所有token运行一次AI分析
  featureWindowSize: parseInt(process.env.FEATURE_WINDOW_SIZE || '20'), // 特征窗口大小：用于AI预测的历史交易数量
};

// 状态更新间隔
const STATUS_UPDATE_INTERVAL = parseInt(process.env.STATUS_UPDATE_INTERVAL_MS || '120000'); // 2分钟默认

// Token活动记录接口
interface TokenActivity {
  address: string;
  firstSeen: Date;
  lastSeen: Date;
  transactionCount: number;
  actions: Array<{
    type: 'buy' | 'sell';
    amount: number;
    timestamp: Date;
    price?: number;
  }>;
}

// 每个Token的交易实例 - 增强版，记录更多交易信息
interface TokenTradingInstance {
  address: string;
  activity: TokenActivity;
  swapManager: PumpSwapManager;
  // 恢复多例模式：每个token有独立的预测器实例
  buyPredictor: BuyPredictor;
  sellPredictor: SellPredictor;
  featureWindow: Array<any>; // 存储特征数据的时间窗口
  lastPrediction: Date;
  isActive: boolean;
  
  // 🔥 添加交易锁机制，防止并发交易
  isTrading: boolean; // 是否正在交易中
  lastTradeTime: Date; // 最后交易时间
  
  // 交易记录 - 增强版
  tradingHistory: Array<{
    type: 'buy' | 'sell';
    timestamp: Date;
    tokenAmount: number;
    solAmount: number;
    price: number;
    prediction: number;
  }>;
  
  // 当前持仓信息
  currentHolding: {
    amount: number;
    buyPrice: number;
    buyTime: Date;
    buySolAmount: number;
    buyGasFee: number; // 🔥 记录买入时的Gas费
    buyPlatformFee: number; // 🔥 记录买入时的平台费
    totalBuyCost: number; // 🔥 记录买入的总成本（含所有费用）
  } | null;
  
  stats: {
    totalTrades: number;
    successfulTrades: number;
    totalPnL: number;
    currentPosition: number; // 当前持仓数量
  };

  // 🔥 新增：最后处理的交易时间戳
  lastProcessedTransactionTime?: Date;
}

console.log('🚀 Paper Trading 动态订阅系统');
console.log('=' + '='.repeat(60));
console.log(`🎯 目标钱包: ${TARGET_WALLET}`);
console.log(`📡 GRPC端点: ${PAPER_TRADING_CONFIG.grpc?.endpoint}`);
console.log(`📊 数据确认级别: PROCESSED (快速响应)`);
console.log(`⏰ 时间窗口: ${WINDOW_HOURS} 小时`);
console.log(`🔄 更新间隔: ${UPDATE_INTERVAL_MS/1000} 秒`);
console.log(`🤖 AI预测间隔: ${AI_CONFIG.predictionInterval/1000} 秒 (每${AI_CONFIG.predictionInterval/1000}秒分析一次所有token)`);
console.log(`💰 Paper Trading: ${PAPER_TRADING_CONFIG.trading?.paperTrading ? '✅ 启用' : '❌ 禁用'}`);
console.log(`💵 每笔交易: ${PAPER_TRADING_CONFIG.trading?.tradeAmountSol} SOL`);
console.log(`🎯 AI买入阈值: ${(PAPER_TRADING_CONFIG.models?.buyThreshold! * 100).toFixed(1)}% (预测概率需≥此值才买入)`);
console.log(`🎯 AI卖出阈值: ${(PAPER_TRADING_CONFIG.models?.sellThreshold! * 100).toFixed(1)}% (预测概率需≥此值才卖出)`);
console.log(`🚫 最小交易过滤: ≥${MIN_TRANSACTION_SOL} SOL (小于此金额的交易将被忽略)`);
console.log(`🔢 Token监控上限: ${MAX_MONITORED_TOKENS} 个 (FIFO队列，新token进来踢掉旧的)`);
console.log('📱 准备启用Telegram通知服务...');
console.log('');

export class PaperTradingWithTracker implements TradingDataProvider {
  private subscriptionManager: DynamicSubscriptionManager;
  private tradingBot: TradingBot;
  private config: TradingSystemConfig;
  private currentTokenSubscriptions: Set<string> = new Set();
  private grpcClient: Client | null = null;
  private currentStream: any = null;
  private isRunning: boolean = false;
  private tokenActivities: Map<string, TokenActivity> = new Map();
  private currentSubscription: Set<string> = new Set([TARGET_WALLET]);
  private subscriptionUpdateTimer: NodeJS.Timeout | null = null;
  
  // Token交易实例管理
  private tokenInstances: Map<string, TokenTradingInstance> = new Map();
  private predictionTimer: NodeJS.Timeout | null = null;
  private tokenStatsTimer: NodeJS.Timeout | null = null;
  
  // Token监控队列 - FIFO，按添加时间排序
  private tokenMonitoringQueue: Array<{ address: string, addedTime: Date }> = [];
  
  // 缓冲队列 - 只卖出不买入的token，等待持仓清空后移除
  private sellOnlyBufferQueue: Map<string, { 
    address: string, 
    addedTime: Date, 
    reason: string,
    lastCheckTime: Date 
  }> = new Map();

  // Telegram通知服务
  private telegramNotifier: TelegramNotifier;
  
  // 统计数据
  private stats = {
    totalTransactions: 0,
    pumpFunTransactions: 0,
    uniqueTokens: 0,
    subscriptionUpdates: 0,
    totalTrades: 0,
    activeTradingTokens: 0
  };

  // Token价格记录 - 为每个token记录最近价格
  private tokenPriceHistory: Map<string, {
    lastPrice: number;
    lastUpdateTime: Date;
    priceStartBase: number; // 该token的基础价格
  }> = new Map();

  // 🔥 新增：真实价格记录系统
  private realPriceData: Map<string, Array<{
    price: number; // 真实计算的价格：solAmount / tokenAmount
    timestamp: Date;
    solAmount: number;
    tokenAmount: number;
    action: 'buy' | 'sell';
    source: 'stream' | 'own_trade';
  }>> = new Map();

  /**
   * Helper method to format price with USD conversion
   */
  private async formatPriceDisplay(solPrice: number): Promise<string> {
    try {
      const priceService = PriceService.getInstance();
      const usdPrice = await priceService.convertTokenPriceToUsd(solPrice);
      return priceService.formatUsdPrice(usdPrice);
    } catch (error) {
      console.warn('🔮 价格转换失败，显示SOL价格:', error);
      return `${solPrice.toFixed(8)} SOL`;
    }
  }

  constructor() {
    console.log('🤖 初始化AI驱动的Paper Trading系统...');
    console.log(`💰 Paper Trading模式: ${PAPER_TRADING_CONFIG.trading?.paperTrading ? '启用' : '禁用'}`);
    console.log(`🎯 目标钱包: ${TARGET_WALLET}`);
    console.log(`   活动窗口: ${WINDOW_HOURS} 小时`);
    console.log(`   更新间隔: ${UPDATE_INTERVAL_MS / 1000} 秒`);

    // 验证钱包地址
    if (!this.validateWalletAddress(TARGET_WALLET)) {
      throw new Error(`❌ 无效的钱包地址: ${TARGET_WALLET}`);
    }

    // 初始化Telegram通知服务
    this.telegramNotifier = new TelegramNotifier();
    
    // 设置数据提供者
    this.telegramNotifier.setDataProvider(this);
    
    // 初始化系统配置
    this.config = loadConfig(PAPER_TRADING_CONFIG);
    
    // 初始化交易机器人
    this.tradingBot = new TradingBot(PAPER_TRADING_CONFIG);
    
    // 初始化动态订阅管理器
    this.subscriptionManager = new DynamicSubscriptionManager(
      TARGET_WALLET, 
      WINDOW_HOURS, 
      UPDATE_INTERVAL_MS
    );

    console.log('✅ Paper Trading配置加载完成');
  }

  /**
   * 验证钱包地址
   */
  private validateWalletAddress(address: string): boolean {
    try {
      new PublicKey(address);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 启动系统
   */
  public async start(): Promise<void> {
    console.log('🔄 启动动态交易管理器...');
    this.isRunning = true;

    // 移除单例预测器管理器初始化，因为使用多例模式
    // console.log('🤖 初始化预测器管理器...');
    // await this.predictorManager.initialize(
    //   this.config.models.buyThreshold,
    //   this.config.models.sellThreshold,
    //   AI_CONFIG.featureWindowSize
    // );

    // 测试Telegram连接 - 增强版调试
    console.log('\n📱 ===== Telegram通知系统初始化 =====');
    console.log('📱 测试Telegram连接...');
    
    try {
      const telegramConnected = await this.telegramNotifier.testConnection();
      
      if (telegramConnected) {
        console.log('✅ Telegram连接成功！开始发送启动通知...');
        await this.telegramNotifier.notifySystem(
          `🚀 Paper Trading系统启动\n🎯 目标钱包: ${TARGET_WALLET.slice(0, 8)}...\n💰 每笔交易: ${this.config.trading.tradeAmountSol} SOL\n🚫 最小交易过滤: ≥${MIN_TRANSACTION_SOL} SOL\n🤖 AI阈值: 买入≥${(this.config.models.buyThreshold*100).toFixed(1)}% | 卖出≥${(this.config.models.sellThreshold*100).toFixed(1)}%`
        );
        console.log('✅ Telegram启动通知已发送');
      } else {
        console.log('❌ Telegram连接失败，将跳过所有通知');
      }
    } catch (error) {
      console.error('❌ Telegram连接测试出错:', error);
    }
    
    console.log('📱 =====================================\n');

    // 启动Telegram Webhook服务器
    console.log('📱 启动Telegram Webhook服务器...');
    await this.telegramNotifier.startWebhook();

    // 🔥 禁用TradingBot - 避免与主系统冲突和自动止损
    // 原因：TradingBot有独立的止损逻辑，会与主系统的AI决策冲突
    // 主系统已经有完整的交易逻辑，不需要额外的TradingBot
    console.log('🤖 TradingBot已禁用 - 使用主系统的AI交易逻辑');
    console.log('🔥 这解决了-5%固定损失问题 - TradingBot的止损逻辑已关闭');
    
    // await this.tradingBot.start(); // 🔥 注释掉，避免冲突

    // 启动动态订阅管理器
    console.log('🔗 启动动态订阅管理器...');
    this.subscriptionManager.start(this.tradingBot);

    // 初始化GRPC客户端
    console.log('🔗 创建GRPC客户端...');
    this.grpcClient = new Client(PAPER_TRADING_CONFIG.grpc?.endpoint, PAPER_TRADING_CONFIG.grpc?.token, undefined);
    console.log('✅ GRPC客户端创建成功');

    // 启动定期更新订阅
    this.subscriptionUpdateTimer = setInterval(() => {
      this.updateTokenList();
      this.printStatus();
    }, UPDATE_INTERVAL_MS);

    // 启动AI预测定时器 - 已禁用，只使用事件驱动预测
    // this.predictionTimer = setInterval(() => {
    //   this.runAIPredictions();
    // }, AI_CONFIG.predictionInterval);
    
    // console.log(`🤖 AI预测定时器启动 (间隔: ${AI_CONFIG.predictionInterval/1000}秒) - 用于兜底预测`);
    console.log(`🚀 只使用事件驱动预测: 每次新交易数据到达立即预测 - 取消定时器兜底预测`);

    // 移除定期推送统计报告的定时器 - 改为Telegram菜单按钮方式
    // this.tokenStatsTimer = setInterval(() => {
    //   this.printTokenStats();
    // }, 300000); // 5分钟 = 300000ms
    
    console.log(`📊 统计报告改为Telegram菜单按钮方式，不再定期推送 - 减少消息打扰`);

    // 启动缓冲队列管理定时器 - 每2分钟检查一次缓冲队列
    setInterval(() => {
      this.manageSellOnlyBufferQueue();
    }, 120000); // 2分钟 = 120000ms
    
    console.log(`🔄 缓冲队列管理定时器启动 (间隔: 2分钟) - 自动检查待清仓token`);

    // 开始监听
    await this.handleStream(this.grpcClient, this.createInitialSubscribeRequest());
  }

  /**
   * 创建初始订阅请求 - 修正订阅逻辑
   */
  private createInitialSubscribeRequest(): SubscribeRequest {
    return {
      accounts: {},
      slots: {},
      transactions: {
        pumpFun: {
          vote: false,
          failed: false,
          signature: undefined,
          accountInclude: [TARGET_WALLET], // OR逻辑：包含目标钱包的交易
          accountExclude: [],
          accountRequired: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()], // AND逻辑：必须是PumpFun AMM的交易
        },
      },
      transactionsStatus: {},
      entry: {},
      blocks: {},
      blocksMeta: {},
      accountsDataSlice: [],
      ping: undefined,
      commitment: CommitmentLevel.PROCESSED,
    };
  }

  /**
   * 更新token列表（移除过期token）
   */
  private updateTokenList(): void {
    const now = new Date();
    const windowMs = WINDOW_HOURS * 60 * 60 * 1000;
    let removedCount = 0;

    for (const [tokenAddress, activity] of this.tokenActivities) {
      if (now.getTime() - activity.lastSeen.getTime() > windowMs) {
        this.tokenActivities.delete(tokenAddress);
        this.currentTokenSubscriptions.delete(tokenAddress);
        
        // 停用token实例
        const instance = this.tokenInstances.get(tokenAddress);
        if (instance) {
          instance.isActive = false;
          this.tokenInstances.delete(tokenAddress);
        }
        
        removedCount++;
      }
    }

    if (removedCount > 0) {
      console.log(`\n🗑️ 移除了 ${removedCount} 个过期token`);
      this.stats.subscriptionUpdates++;
    }
  }

  /**
   * 打印状态信息
   */
  private printStatus(): void {
    const activeTokens = this.subscriptionManager.getActiveTokens();
    const stats = this.tradingBot.getStats();
    
    console.log(`\n📊 系统状态 [${new Date().toLocaleTimeString()}]:`);
    console.log(`   🎯 目标钱包: ${TARGET_WALLET.slice(0, 12)}...`);
    console.log(`   🪙 活跃Token: ${activeTokens.length} 个`);
    console.log(`   🤖 AI交易统计: ${stats.totalTrades} 笔 | 胜率: ${(stats.winRate * 100).toFixed(1)}%`);
    console.log(`   💰 当前余额: ${stats.currentBalance.toFixed(4)} SOL | 活跃仓位: ${stats.activePositions}`);
    console.log(`   📈 总盈亏: ${stats.totalPnL.toFixed(4)} SOL | 最大回撤: ${(stats.maxDrawdown * 100).toFixed(2)}%`);
    
    if (activeTokens.length > 0) {
      console.log(`   🏆 监控Token:`);
      activeTokens.slice(0, 3).forEach((token, index) => {
        console.log(`      ${index + 1}. ${token.slice(0, 12)}...`);
      });
      if (activeTokens.length > 3) {
        console.log(`      ... 还有 ${activeTokens.length - 3} 个token`);
      }
    }
  }

  /**
   * 运行AI预测 - 已不再被定时器调用，仅保留以防需要手动调用
   */
  private async runAIPredictions(): Promise<void> {
    if (!this.isRunning) return;

    const activeTokens = this.subscriptionManager.getActiveTokens();
    console.log(`🤖 运行AI预测... (${activeTokens.length} 个活跃token)`);

    for (const tokenAddress of activeTokens) {
      try {
        await this.makePredictionForToken(tokenAddress);
      } catch (error) {
        console.error(`❌ Token预测失败 ${tokenAddress.slice(0, 8)}...:`, error);
      }
    }
  }

  /**
   * 为特定token进行AI预测 - 事件驱动版：每次新数据都预测
   */
  private async makePredictionForToken(tokenAddress: string): Promise<void> {
    // 获取token实例
    let instance = this.tokenInstances.get(tokenAddress);
    
    if (!instance) {
      // 🔥 新token加入监控队列管理
      await this.manageTokenMonitoringQueue(tokenAddress);
      
      // 创建新的token交易实例
      instance = await this.createTokenInstance(tokenAddress);
      this.tokenInstances.set(tokenAddress, instance);
      console.log(`🆕 创建Token交易实例: ${tokenAddress.slice(0, 8)}...`);
      
      // 🔥 初始化时，加载所有历史数据到预测器
      await this.initializeTokenPredictorData(tokenAddress, instance);
    }

    if (!instance.isActive) return;

    console.log(`🔮 Token ${tokenAddress.slice(0, 8)}... 事件驱动预测 (增量数据更新)`);
    
    // 🔥 修复：只获取新的交易数据，而不是所有历史数据
    const newTransactions = this.getNewTransactionsForToken(tokenAddress, instance);
    
    console.log(`📊 Token ${tokenAddress.slice(0, 8)}... 增量数据分析:`);
    console.log(`   🆕 新增交易: ${newTransactions.length} 笔`);
    
    // 🔥 只添加新的交易数据到预测器队列
    if (newTransactions.length > 0) {
      console.log(`   📋 向该Token预测器添加新数据...`);
      
      // 将新交易数据逐个添加到该token的预测器队列
      for (const tx of newTransactions) {
        const featureData = {
          timestamp: tx.timestamp instanceof Date ? tx.timestamp : new Date(tx.timestamp),
          action: tx.action === 'buy' ? 1 : tx.action === 'sell' ? 0 : 2,
          sol_amount: tx.solAmount,
          usd_amount: tx.usdAmount,
          is_target_wallet: (tx.wallet || '').includes(TARGET_WALLET.slice(0, 8)),
          wallet: tx.wallet || 'unknown',
          block_number: tx.slot
        };
        
        // 添加到该token专属的预测器队列
        instance.buyPredictor.addTransaction(featureData);
        instance.sellPredictor.addTransaction(featureData);
      }
      
      console.log(`   ✅ 已将 ${newTransactions.length} 笔新交易添加到Token预测器队列`);
      
      // 🔥 更新最后处理的交易时间戳
      if (newTransactions.length > 0) {
        const latestTransaction = newTransactions[newTransactions.length - 1];
        instance.lastProcessedTransactionTime = latestTransaction.timestamp;
      }
    } else {
      console.log(`   ℹ️ 无新交易数据，使用现有队列进行预测`);
    }

    // 获取该token预测器的队列状态
    const buyQueueStatus = instance.buyPredictor.getQueueStatus();
    const sellQueueStatus = instance.sellPredictor.getQueueStatus();
    
    console.log(`   📊 Token独立队列状态:`);
    console.log(`      🔢 买入预测器: ${buyQueueStatus.currentSize} 笔数据 (可预测: ${buyQueueStatus.canPredict})`);
    console.log(`      🔢 卖出预测器: ${sellQueueStatus.currentSize} 笔数据 (可预测: ${sellQueueStatus.canPredict})`);

    // 检查是否有足够的数据进行预测
    if (!buyQueueStatus.canPredict || !sellQueueStatus.canPredict) {
      const buyNeeded = buyQueueStatus.neededForPrediction;
      const sellNeeded = sellQueueStatus.neededForPrediction;
      console.log(`⚠️ Token ${tokenAddress.slice(0, 8)}... 数据不足，跳过预测:`);
      console.log(`   📊 买入预测器需要: ${buyNeeded} 笔额外数据`);
      console.log(`   📊 卖出预测器需要: ${sellNeeded} 笔额外数据`);
      return;
    }

    console.log(`   📊 队列状态: 买入${buyQueueStatus.currentSize} 卖出${sellQueueStatus.currentSize} | 数据充足: ✅`);
    console.log(`   🚀 立即执行AI预测 (${newTransactions.length > 0 ? '新数据触发' : '现有数据'})`);

    // 进行买入预测和卖出预测（基于该token独立队列中的数据）
    try {
      console.log(`🔮 开始进行AI预测 ${tokenAddress.slice(0, 8)}...`);
      console.log(`   📊 预测配置:`);
      console.log(`      🎯 买入阈值: ${(this.config.models.buyThreshold * 100).toFixed(1)}%`);
      console.log(`      🎯 卖出阈值: ${(this.config.models.sellThreshold * 100).toFixed(1)}%`);
      console.log(`      📋 当前持仓: ${instance.currentHolding ? '有持仓' : '无持仓'}`);
      
      const buyPredictionResult = await instance.buyPredictor.predictBuy();
      const sellPredictionResult = await instance.sellPredictor.predictSell();

      console.log(`🎯 ================== AI预测结果 ==================`);
      console.log(`   🪙 Token: ${tokenAddress.slice(0, 8)}...`);
      console.log(`   📊 独立队列状态:`);
      console.log(`      🔢 买入队列: ${buyQueueStatus.currentSize} 笔数据`);
      console.log(`      🔢 卖出队列: ${sellQueueStatus.currentSize} 笔数据`);
      console.log(`   🤖 预测结果:`);
      console.log(`      📈 买入预测: ${(buyPredictionResult.probability * 100).toFixed(3)}%`);
      console.log(`      📉 卖出预测: ${(sellPredictionResult.probability * 100).toFixed(3)}%`);
      console.log(`      ⏱️ 买入预测耗时: ${buyPredictionResult.predictionTimeMicros.toFixed(2)}μs`);
      console.log(`      ⏱️ 卖出预测耗时: ${sellPredictionResult.predictionTimeMicros.toFixed(2)}μs`);
      console.log(`   🎯 阈值比较:`);
      console.log(`      📈 买入: ${(buyPredictionResult.probability * 100).toFixed(3)}% ${buyPredictionResult.probability >= this.config.models.buyThreshold ? '≥' : '<'} ${(this.config.models.buyThreshold * 100).toFixed(1)}% (${buyPredictionResult.probability >= this.config.models.buyThreshold ? '✅ 达到' : '❌ 未达到'})`);
      console.log(`      📉 卖出: ${(sellPredictionResult.probability * 100).toFixed(3)}% ${sellPredictionResult.probability >= this.config.models.sellThreshold ? '≥' : '<'} ${(this.config.models.sellThreshold * 100).toFixed(1)}% (${sellPredictionResult.probability >= this.config.models.sellThreshold ? '✅ 达到' : '❌ 未达到'})`);
      console.log(`   💼 交易条件检查:`);
      console.log(`      🔍 买入条件: 无持仓(${!instance.currentHolding}) AND 买入≥阈值(${buyPredictionResult.probability >= this.config.models.buyThreshold}) = ${!instance.currentHolding && buyPredictionResult.probability >= this.config.models.buyThreshold ? '✅ 满足' : '❌ 不满足'}`);
      console.log(`      🔍 卖出条件: 有持仓(${!!instance.currentHolding}) AND 卖出≥阈值(${sellPredictionResult.probability >= this.config.models.sellThreshold}) = ${!!instance.currentHolding && sellPredictionResult.probability >= this.config.models.sellThreshold ? '✅ 满足' : '❌ 不满足'}`);
      console.log(`================================================`);

      // 根据预测结果进行交易决策
      await this.executeTradeDecision(tokenAddress, buyPredictionResult.probability, sellPredictionResult.probability, instance);
      
      // 更新最后预测时间
      instance.lastPrediction = new Date();
      
    } catch (error) {
      console.error(`❌ AI预测错误 ${tokenAddress.slice(0, 8)}...:`, error);
      console.error(`   错误详情:`, error.stack);
    }
  }

  /**
   * 🔥 新增：初始化Token预测器数据 - 只在创建实例时调用一次
   */
  private async initializeTokenPredictorData(tokenAddress: string, instance: TokenTradingInstance): Promise<void> {
    console.log(`🔄 初始化Token预测器数据: ${tokenAddress.slice(0, 8)}...`);
    
    // 获取所有历史数据用于初始化
    const allHistoricalTransactions = this.getTokenSpecificTransactions(tokenAddress);
    
    console.log(`📊 初始化数据加载:`);
    console.log(`   📋 历史交易总数: ${allHistoricalTransactions.length} 笔`);
    
    if (allHistoricalTransactions.length > 0) {
      // 批量添加历史数据到预测器
      for (const tx of allHistoricalTransactions) {
        const featureData = {
          timestamp: tx.timestamp instanceof Date ? tx.timestamp : new Date(tx.timestamp),
          action: tx.action === 'buy' ? 1 : tx.action === 'sell' ? 0 : 2,
          sol_amount: tx.solAmount,
          usd_amount: tx.usdAmount,
          is_target_wallet: (tx.wallet || '').includes(TARGET_WALLET.slice(0, 8)),
          wallet: tx.wallet || 'unknown',
          block_number: tx.slot
        };
        
        instance.buyPredictor.addTransaction(featureData);
        instance.sellPredictor.addTransaction(featureData);
      }
      
      // 设置最后处理的交易时间戳
      const latestTransaction = allHistoricalTransactions[allHistoricalTransactions.length - 1];
      instance.lastProcessedTransactionTime = latestTransaction.timestamp;
      
      console.log(`   ✅ 已加载 ${allHistoricalTransactions.length} 笔历史交易到预测器`);
      console.log(`   ⏰ 最后交易时间: ${latestTransaction.timestamp.toLocaleTimeString()}`);
    } else {
      // 没有历史数据，设置初始时间戳
      instance.lastProcessedTransactionTime = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24小时前
      console.log(`   ℹ️ 无历史数据，设置初始时间戳`);
    }
  }

  /**
   * 🔥 新增：获取新的交易数据 - 只返回上次处理后的新交易
   */
  private getNewTransactionsForToken(tokenAddress: string, instance: TokenTradingInstance): any[] {
    // 从动态订阅管理器获取该token的专属交易历史
    const allTransactions = this.subscriptionManager.getWalletTransactionHistory(24); // 24小时
    
    // 只获取该token的交易，并过滤小额交易
    const tokenTransactions = allTransactions.filter(tx => 
      tx.tokenAddress === tokenAddress && 
      tx.solAmount >= MIN_TRANSACTION_SOL
    );
    
    // 🔥 关键修复：只返回上次处理后的新交易
    const lastProcessedTime = instance.lastProcessedTransactionTime || new Date(0);
    const newTransactions = tokenTransactions.filter(tx => 
      tx.timestamp > lastProcessedTime
    );
    
    console.log(`🎯 Token ${tokenAddress.slice(0, 8)}... 增量数据提取:`);
    console.log(`   📋 全部历史: ${allTransactions.length} 笔`);
    console.log(`   🎯 该Token总数: ${tokenTransactions.length} 笔`);
    console.log(`   ⏰ 上次处理时间: ${lastProcessedTime.toLocaleTimeString()}`);
    console.log(`   🆕 新增交易: ${newTransactions.length} 笔`);
    console.log(`   💰 过滤标准: ≥${MIN_TRANSACTION_SOL} SOL`);
    
    // 显示新交易详情
    if (newTransactions.length > 0) {
      console.log(`   📈 新增交易详情:`);
      newTransactions.forEach((tx, i) => {
        const isTarget = (tx.wallet || '').includes(TARGET_WALLET.slice(0, 8));
        const walletLabel = isTarget ? '🎯' : '👤';
        console.log(`      ${i + 1}. ${walletLabel} ${tx.action} ${tx.solAmount.toFixed(4)} SOL (${tx.timestamp.toLocaleTimeString()})`);
      });
    }
    
    return newTransactions;
  }

  /**
   * 获取特定token的交易数据 - 新增方法，每个token独立数据
   */
  private getTokenSpecificTransactions(tokenAddress: string): any[] {
    // 从动态订阅管理器获取该token的专属交易历史
    const allTransactions = this.subscriptionManager.getWalletTransactionHistory(24); // 24小时
    
    // 只获取该token的交易，并过滤小额交易
    const tokenTransactions = allTransactions.filter(tx => 
      tx.tokenAddress === tokenAddress && 
      tx.solAmount >= MIN_TRANSACTION_SOL
    );
    
    console.log(`🎯 Token ${tokenAddress.slice(0, 8)}... 独立数据提取:`);
    console.log(`   📋 全部历史: ${allTransactions.length} 笔`);
    console.log(`   🎯 该Token: ${tokenTransactions.length} 笔`);
    console.log(`   💰 过滤标准: ≥${MIN_TRANSACTION_SOL} SOL`);
    
    // 显示该token最近的交易
    if (tokenTransactions.length > 0) {
      console.log(`   📈 最近交易:`);
      tokenTransactions.slice(-5).forEach((tx, i) => {
        const isTarget = (tx.wallet || '').includes(TARGET_WALLET.slice(0, 8));
        const walletLabel = isTarget ? '🎯' : '👤';
        console.log(`      ${i + 1}. ${walletLabel} ${tx.action} ${tx.solAmount.toFixed(4)} SOL (${tx.timestamp.toLocaleTimeString()})`);
      });
    }
    
    return tokenTransactions;
  }

  /**
   * 创建token交易实例
   */
  private async createTokenInstance(tokenAddress: string): Promise<TokenTradingInstance> {
    const activity = this.tokenActivities.get(tokenAddress) || {
      address: tokenAddress,
      firstSeen: new Date(),
      lastSeen: new Date(),
      transactionCount: 0,
      actions: []
    };

    // 为PumpSwapManager创建正确的配置
    const swapConfig = {
      paperTrading: this.config.trading.paperTrading,
      rpcEndpoint: process.env.SOLANA_RPC_ENDPOINT || 'https://api.mainnet-beta.solana.com',
      privateKey: process.env.PRIVATE_KEY
    };

    // 使用绝对路径解决模型文件加载问题
    const path = require('path');
    const buyModelPath = path.resolve(__dirname, 'catboost_focal_blocktol_2025.cbm');
    const sellModelPath = path.resolve(__dirname, 'best_catboost_sell_model.cbm');

    console.log(`🤖 创建Token实例 ${tokenAddress.slice(0, 8)}...:`);
    console.log(`   📊 买入模型: ${buyModelPath}`);
    console.log(`   📊 卖出模型: ${sellModelPath}`);

    return {
      address: tokenAddress,
      activity,
      swapManager: new PumpSwapManager(swapConfig),
      buyPredictor: new BuyPredictor(buyModelPath, AI_CONFIG.featureWindowSize, this.config.models.buyThreshold),
      sellPredictor: new SellPredictor(sellModelPath, AI_CONFIG.featureWindowSize, this.config.models.sellThreshold),
      featureWindow: [],
      lastPrediction: new Date(0), // 初始时间，确保立即进行第一次预测
      isActive: true,
      isTrading: false,
      lastTradeTime: new Date(0),
      tradingHistory: [],
      currentHolding: null,
      stats: {
        totalTrades: 0,
        successfulTrades: 0,
        totalPnL: 0,
        currentPosition: 0
      }
    };
  }

  /**
   * 构建特征窗口 - 增强版，包含所有钱包的交易数据，修复时间戳类型问题，过滤小额交易
   */
  private async buildFeatureWindow(tokenAddress: string): Promise<any[]> {
    // 获取更长时间的交易历史用于调试
    const allTransactions = this.subscriptionManager.getWalletTransactionHistory(24); // 24小时
    
    // 分类交易数据
    const targetWalletTransactions = allTransactions.filter(tx => 
      tx.tokenAddress === tokenAddress // 只要是该token的交易
    );
    
    // 进一步过滤小额交易
    const filteredTransactions = targetWalletTransactions.filter(tx => tx.solAmount >= MIN_TRANSACTION_SOL);
    
    // 详细的调试信息
    console.log(`📊 构建特征窗口 ${tokenAddress.slice(0, 8)}... 详细分析:`);
    console.log(`   📋 全部交易历史: ${allTransactions.length} 笔`);
    console.log(`   🎯 该Token交易: ${targetWalletTransactions.length} 笔`);
    console.log(`   ✅ 过滤后交易: ${filteredTransactions.length} 笔 (≥${MIN_TRANSACTION_SOL} SOL)`);
    
    // 显示最近的几笔该token交易，区分目标钱包和其他钱包
    if (targetWalletTransactions.length > 0) {
      console.log(`   📈 该Token最近交易分析:`);
      
      // 按钱包分类显示
      const transactionsByWallet = new Map<string, number>();
      targetWalletTransactions.forEach(tx => {
        const walletKey = tx.wallet || 'unknown';
        transactionsByWallet.set(walletKey, (transactionsByWallet.get(walletKey) || 0) + 1);
      });
      
      // 显示钱包统计
      for (const [wallet, count] of transactionsByWallet) {
        const isTarget = wallet === TARGET_WALLET || wallet.includes(TARGET_WALLET.slice(0, 8));
        console.log(`      ${isTarget ? '🎯' : '👤'} ${wallet.slice(0, 8)}...: ${count} 笔${isTarget ? ' (目标钱包)' : ' (其他钱包)'}`);
      }
      
      // 显示最近5笔交易详情
      console.log(`   📋 最近交易详情:`);
      filteredTransactions.slice(0, 5).forEach((tx, i) => {
        const isTarget = (tx.wallet || '').includes(TARGET_WALLET.slice(0, 8));
        const walletLabel = isTarget ? '🎯' : '👤';
        console.log(`      ${i + 1}. ${walletLabel} ${tx.action} ${tx.solAmount.toFixed(4)} SOL (${tx.timestamp.toLocaleTimeString()})`);
      });
    }
    
    // 显示其他token的交易（用于调试）
    const otherTokenTransactions = allTransactions.filter(tx => tx.tokenAddress !== tokenAddress);
    if (otherTokenTransactions.length > 0) {
      console.log(`   🔍 其他Token交易: ${otherTokenTransactions.length} 笔`);
      const uniqueTokens = new Set(otherTokenTransactions.map(tx => tx.tokenAddress));
      console.log(`      涉及 ${uniqueTokens.size} 个不同Token`);
    }
    
    // 构建特征数据 - 确保时间戳是Date对象，包含所有钱包的交易
    const features = filteredTransactions.map(tx => {
      const isTargetWallet = (tx.wallet || '').includes(TARGET_WALLET.slice(0, 8));
      
      return {
        timestamp: tx.timestamp instanceof Date ? tx.timestamp : new Date(tx.timestamp), // 确保是Date对象
        action: tx.action === 'buy' ? 1 : tx.action === 'sell' ? 0 : 2,
        sol_amount: tx.solAmount,
        usd_amount: tx.usdAmount,
        is_target_wallet: isTargetWallet, // 标记是否为目标钱包
        wallet: tx.wallet || 'unknown',
        block_number: tx.slot
      };
    });

    console.log(`   📊 构建特征数据: ${features.length} 笔`);
    
    // 显示特征数据统计
    const targetWalletFeatures = features.filter(f => f.is_target_wallet);
    const otherWalletFeatures = features.filter(f => !f.is_target_wallet);
    
    console.log(`   🎯 目标钱包特征: ${targetWalletFeatures.length} 笔`);
    console.log(`   👥 其他钱包特征: ${otherWalletFeatures.length} 笔`);

    // 如果数据不足，用默认值填充
    let paddingCount = 0;
    while (features.length < AI_CONFIG.featureWindowSize) {
      features.unshift({
        timestamp: new Date(Date.now() - (features.length + paddingCount + 1) * 60000), // 使用Date对象
        action: 2,
        sol_amount: MIN_TRANSACTION_SOL, // 使用最小交易金额作为默认值
        usd_amount: MIN_TRANSACTION_SOL * 150,
        is_target_wallet: false,
        wallet: '',
        block_number: 0
      });
      paddingCount++;
    }
    
    if (paddingCount > 0) {
      console.log(`   🔧 填充默认数据: ${paddingCount} 笔`);
    }
    
    console.log(`   ✅ 最终特征窗口: ${features.length} 笔`);
    console.log(`       ├─ 真实数据: ${features.length - paddingCount} 笔 (目标钱包: ${targetWalletFeatures.length}, 其他钱包: ${otherWalletFeatures.length})`);
    console.log(`       └─ 填充数据: ${paddingCount} 笔`);

    return features.slice(-AI_CONFIG.featureWindowSize);
  }

  /**
   * 记录真实价格数据
   */
  private recordRealPrice(
    tokenAddress: string, 
    solAmount: number, 
    tokenAmount: number, 
    action: 'buy' | 'sell',
    source: 'stream' | 'own_trade' = 'stream'
  ): void {
    if (tokenAmount <= 0) {
      console.log(`⚠️ Token数量无效，跳过价格记录: ${tokenAmount}`);
      return;
    }

    const price = solAmount / tokenAmount;
    const priceRecord = {
      price,
      timestamp: new Date(),
      solAmount,
      tokenAmount,
      action,
      source
    };

    if (!this.realPriceData.has(tokenAddress)) {
      this.realPriceData.set(tokenAddress, []);
    }

    const records = this.realPriceData.get(tokenAddress)!;
    records.push(priceRecord);

    // 保留最近50条价格记录
    if (records.length > 50) {
      records.shift();
    }

    console.log(`📊 记录真实价格: ${tokenAddress.slice(0, 8)}... ${action} ${price.toFixed(8)} SOL/token`);
    console.log(`   📋 计算: ${solAmount.toFixed(4)} SOL ÷ ${tokenAmount.toFixed(2)} tokens = ${price.toFixed(8)}`);
    console.log(`   📈 来源: ${source} | 时间: ${priceRecord.timestamp.toLocaleTimeString()}`);
  }

  /**
   * 获取最近成交价格 - 优先使用最新市场价格
   */
  private getLastTradePrice(tokenAddress: string): number | undefined {
    console.log(`📊 获取Token最新市场价格: ${tokenAddress.slice(0, 8)}...`);
    
    // 🔥 优先使用真实价格记录系统中的最新市场价格
    const realPrices = this.realPriceData.get(tokenAddress);
    if (realPrices && realPrices.length > 0) {
      // 🔥 直接使用最新的市场价格记录（这是最准确的当前市场价格）
      const latestPrice = realPrices[realPrices.length - 1]; // 获取最新的价格记录
      
      const ageMinutes = Math.floor((Date.now() - latestPrice.timestamp.getTime()) / (1000 * 60));
      console.log(`   ✅ 使用最新市场价格:`);
      console.log(`      ${latestPrice.action} ${latestPrice.price.toFixed(8)} SOL/token (${ageMinutes}分钟前)`);
      console.log(`      基于最新交易: ${latestPrice.solAmount.toFixed(4)} SOL ÷ ${latestPrice.tokenAmount.toFixed(2)} tokens`);
      console.log(`      📊 价格来源: ${latestPrice.source === 'stream' ? '市场交易流' : '自己交易'}`);
      
      return latestPrice.price;
    }
    
    // 🔥 备选：如果没有市场价格记录，才使用自己的交易历史（但会警告）
    const instance = this.tokenInstances.get(tokenAddress);
    if (instance && instance.tradingHistory.length > 0) {
      const lastTrade = instance.tradingHistory[instance.tradingHistory.length - 1];
      console.log(`   ⚠️ 无市场价格，使用自己的交易价格（可能过时）:`);
      console.log(`      ${lastTrade.type} ${lastTrade.price.toFixed(8)} SOL/token (${lastTrade.timestamp.toLocaleTimeString()})`);
      console.log(`      ⚠️ 警告：此价格可能不反映当前市场状况！`);
      return lastTrade.price;
    }
    
    console.log(`   ❌ 无任何价格记录，无法确定市场价格`);
    console.log(`   📋 建议：等待更多交易数据积累后再进行交易`);
    return undefined;
  }

  /**
   * 执行交易决策 - 严格1买1卖模式，全仓买入卖出，增强调试信息
   */
  private async executeTradeDecision(
    tokenAddress: string, 
    buyPrediction: number, 
    sellPrediction: number, 
    instance: TokenTradingInstance
  ): Promise<void> {
    const config = this.config;
    const hasPosition = instance.currentHolding !== null && instance.currentHolding.amount > 0;

    console.log(`🔍 交易决策分析 ${tokenAddress.slice(0, 8)}...:`);
    console.log(`   📊 当前持仓状态: ${hasPosition ? '✅ 有持仓' : '❌ 无持仓'}`);
    
    // 🔥 检查交易锁状态
    if (instance.isTrading) {
      console.log(`🔒 交易锁定中，跳过交易决策 ${tokenAddress.slice(0, 8)}...`);
      return;
    }
    
    console.log(`🔓 交易锁状态: 可以交易 ${tokenAddress.slice(0, 8)}...`);
    
    if (hasPosition && instance.currentHolding) {
      console.log(`   💰 持仓详情:`);
      console.log(`      📦 持仓数量: ${instance.currentHolding.amount.toFixed(2)} tokens`);
      const buyPriceDisplay = await this.formatPriceDisplay(instance.currentHolding.buyPrice);
      console.log(`      💲 买入价格: ${buyPriceDisplay}/token`);
      console.log(`      💵 买入金额: ${instance.currentHolding.buySolAmount.toFixed(4)} SOL`);
      const holdingTimeMs = Date.now() - instance.currentHolding.buyTime.getTime();
      const holdingHours = Math.floor(holdingTimeMs / (1000 * 60 * 60));
      const holdingMinutes = Math.floor((holdingTimeMs % (1000 * 60 * 60)) / (1000 * 60));
      const holdingSeconds = Math.floor((holdingTimeMs % (1000 * 60)) / 1000);
      const holdingMilliseconds = holdingTimeMs % 1000;
      console.log(`      ⏰ 持仓时间: ${holdingHours}h ${holdingMinutes}m ${holdingSeconds}s ${holdingMilliseconds}ms`);
    }
    
    console.log(`   🤖 AI预测: 买入=${(buyPrediction*100).toFixed(1)}% | 卖出=${(sellPrediction*100).toFixed(1)}%`);
    console.log(`   🎯 交易阈值: 买入≥${(config.models.buyThreshold*100).toFixed(1)}% | 卖出≥${(config.models.sellThreshold*100).toFixed(1)}%`);
    console.log(`   📋 买入条件: 无持仓(${!hasPosition}) AND 买入预测≥阈值(${buyPrediction >= config.models.buyThreshold})`);
    console.log(`   📋 卖出条件: 有持仓(${hasPosition}) AND 卖出预测≥阈值(${sellPrediction >= config.models.sellThreshold})`);

    // 严格1买1卖逻辑：只有无持仓时才能买入
    if (!hasPosition && buyPrediction >= config.models.buyThreshold) {
      console.log(`📈 🚀 触发买入信号 ${tokenAddress.slice(0, 8)}... (预测: ${(buyPrediction*100).toFixed(1)}% ≥ ${(config.models.buyThreshold*100).toFixed(1)}%)`);
      
      // 🔥 设置交易锁
      instance.isTrading = true;
      console.log(`🔒 设置交易锁: 买入开始 ${tokenAddress.slice(0, 8)}...`);
      
      try {
        // 获取最近成交价格
        const lastTradePrice = this.getLastTradePrice(tokenAddress);
        
        const result = await instance.swapManager.buy(
          tokenAddress,
          config.trading.tradeAmountSol,
          config.trading.slippageTolerance,
          lastTradePrice
        );
        
        if (result.success) {
          // 计算实际收到的token数量
          const actualPrice = result.actualPrice;
          if (!actualPrice) {
            console.log(`❌ 买入失败：无法获取真实执行价格`);
            return;
          }
          
          const tokensReceived = config.trading.tradeAmountSol / actualPrice;
          
          // 🔥 计算买入时的手续费
          const buyGasFee = result.gasFee || 0.000005; // 买入Gas费
          const platformFeeRate = 0.003; // 0.3%平台手续费
          const buyPlatformFee = config.trading.tradeAmountSol * platformFeeRate; // 买入平台费
          const totalBuyCost = config.trading.tradeAmountSol + buyGasFee + buyPlatformFee; // 买入总成本
          
          // 记录完整持仓信息
          instance.currentHolding = {
            amount: tokensReceived,
            buyPrice: actualPrice,
            buyTime: new Date(),
            buySolAmount: config.trading.tradeAmountSol,
            buyGasFee: buyGasFee,
            buyPlatformFee: buyPlatformFee,
            totalBuyCost: totalBuyCost
          };
          
          // 🔥 记录自己的买入交易价格
          this.recordRealPrice(
            tokenAddress,
            config.trading.tradeAmountSol,
            tokensReceived,
            'buy',
            'own_trade'
          );
          
          // 记录交易历史
          instance.tradingHistory.push({
            type: 'buy',
            timestamp: new Date(),
            tokenAmount: tokensReceived,
            solAmount: config.trading.tradeAmountSol,
            price: actualPrice,
            prediction: buyPrediction
          });
          
          // 更新统计
          instance.stats.currentPosition = tokensReceived;
          instance.stats.totalTrades++;
          
          // 🔥 更新最后交易时间
          instance.lastTradeTime = new Date();
          
          console.log(`✅ 💰 买入成功 ${tokenAddress.slice(0, 8)}...:`);
          console.log(`   📦 获得Token: ${tokensReceived.toFixed(2)} 个`);
          console.log(`   💵 投入SOL: ${config.trading.tradeAmountSol.toFixed(4)} SOL`);
          console.log(`   💸 买入手续费:`);
          console.log(`      🔹 Gas费: ${buyGasFee.toFixed(8)} SOL`);
          console.log(`      🔹 平台费: ${buyPlatformFee.toFixed(6)} SOL (${(platformFeeRate*100).toFixed(1)}%)`);
          console.log(`      🔹 总成本: ${totalBuyCost.toFixed(4)} SOL (含所有费用)`);
          const buyPriceDisplay = await this.formatPriceDisplay(actualPrice);
          console.log(`   💲 买入价格: ${buyPriceDisplay}/token`);
          console.log(`   🔄 持仓状态更新: 无持仓 → 有持仓`);
          
          // 模拟交易信息
          const mockTransactionId = `BUY_${tokenAddress.slice(0, 8)}_${Date.now()}`;
          const mockSlippage = Math.random() * 0.01; // 0-1%随机滑点
          const mockGasFee = 0.000005; // 模拟5000 lamports gas费
          
          console.log(`   📋 交易ID: ${mockTransactionId}`);
          console.log(`   📈 滑点: ${(mockSlippage * 100).toFixed(2)}%`);
          console.log(`   ⛽ Gas: ${mockGasFee.toFixed(8)} SOL`);
          
          // 发送增强的Telegram通知
          await this.telegramNotifier.notifyBuy(
            tokenAddress,
            config.trading.tradeAmountSol,
            tokensReceived,
            actualPrice,
            buyPrediction,
            mockTransactionId,
            mockSlippage,
            mockGasFee
          );
          
          // 通知trading bot
          await this.notifyTradingBot(tokenAddress, 'buy', config.trading.tradeAmountSol);
          
        } else {
          console.log(`❌ 买入失败 ${tokenAddress.slice(0, 8)}...: ${result.error}`);
        }
        
      } catch (error) {
        console.error(`❌ 买入错误 ${tokenAddress.slice(0, 8)}...:`, error);
      } finally {
        // 🔥 释放交易锁
        instance.isTrading = false;
        console.log(`🔓 释放交易锁: 买入完成 ${tokenAddress.slice(0, 8)}...`);
      }
    }
    // 严格1买1卖逻辑：只有有持仓时才能卖出，且全仓卖出
    else if (hasPosition && sellPrediction >= config.models.sellThreshold && instance.currentHolding) {
      console.log(`📉 🚀 触发卖出信号 ${tokenAddress.slice(0, 8)}... (预测: ${(sellPrediction*100).toFixed(1)}% ≥ ${(config.models.sellThreshold*100).toFixed(1)}%)`);
      console.log(`🔄 准备全仓卖出: ${instance.currentHolding.amount.toFixed(2)} tokens`);
      
      // 🔥 设置交易锁
      instance.isTrading = true;
      console.log(`🔒 设置交易锁: 卖出开始 ${tokenAddress.slice(0, 8)}...`);
      
      try {
        // 全仓卖出：卖出所有持有的token
        const fullPosition = instance.currentHolding.amount;
        
        // 获取最近成交价格
        const lastTradePrice = this.getLastTradePrice(tokenAddress);
        
        const result = await instance.swapManager.sell(
          tokenAddress,
          fullPosition, // 全仓卖出
          config.trading.slippageTolerance,
          lastTradePrice
        );
        
        if (result.success) {
          // 计算实际收益
          const actualSellPrice = result.actualPrice;
          if (!actualSellPrice) {
            console.log(`❌ 卖出失败：无法获取真实执行价格`);
            return;
          }
          
          const solReceived = fullPosition * actualSellPrice;
          
          // 🔥 计算真实手续费
          const sellGasFee = result.gasFee || 0.000005; // 卖出Gas费
          const buyGasFee = instance.currentHolding.buyGasFee; // 从记录中获取买入Gas费
          const platformFeeRate = 0.003; // 0.3%平台手续费
          const sellPlatformFee = solReceived * platformFeeRate; // 卖出平台费
          const buyPlatformFee = instance.currentHolding.buyPlatformFee; // 从记录中获取买入平台费
          
          const totalFees = sellGasFee + buyGasFee + sellPlatformFee + buyPlatformFee;
          const netSolReceived = solReceived - sellGasFee - sellPlatformFee; // 扣除卖出费用后的净收入
          const netBuyCost = instance.currentHolding.totalBuyCost; // 使用记录的买入总成本
          
          const profit = netSolReceived - netBuyCost; // 真实净收益
          const profitPercentage = ((netSolReceived / netBuyCost - 1) * 100);
          const holdingTimeMs = Date.now() - instance.currentHolding.buyTime.getTime();
          
          // 🔥 记录自己的卖出交易价格
          this.recordRealPrice(
            tokenAddress,
            solReceived,
            fullPosition,
            'sell',
            'own_trade'
          );
          
          // 记录交易历史
          instance.tradingHistory.push({
            type: 'sell',
            timestamp: new Date(),
            tokenAmount: fullPosition,
            solAmount: solReceived,
            price: actualSellPrice,
            prediction: sellPrediction
          });
          
          // 🔥 更新最后交易时间
          instance.lastTradeTime = new Date();
          
          console.log(`✅ 💰 全仓卖出成功 ${tokenAddress.slice(0, 8)}...:`);
          console.log(`   📦 卖出Token: ${fullPosition.toFixed(2)} 个`);
          console.log(`   💵 总收入: ${solReceived.toFixed(4)} SOL`);
          console.log(`   💸 手续费详情:`);
          console.log(`      🔹 卖出Gas费: ${sellGasFee.toFixed(8)} SOL`);
          console.log(`      🔹 买入Gas费: ${buyGasFee.toFixed(8)} SOL`);
          console.log(`      🔹 卖出平台费: ${sellPlatformFee.toFixed(6)} SOL (${(platformFeeRate*100).toFixed(1)}%)`);
          console.log(`      🔹 买入平台费: ${buyPlatformFee.toFixed(6)} SOL (${(platformFeeRate*100).toFixed(1)}%)`);
          console.log(`      🔹 总手续费: ${totalFees.toFixed(6)} SOL`);
          console.log(`   💰 净收入: ${netSolReceived.toFixed(4)} SOL (扣除卖出费用)`);
          console.log(`   💰 买入成本: ${netBuyCost.toFixed(4)} SOL (含买入费用)`);
          const sellPriceDisplay = await this.formatPriceDisplay(actualSellPrice);
          const buyPriceDisplay = await this.formatPriceDisplay(instance.currentHolding.buyPrice);
          console.log(`   💲 卖出价格: ${sellPriceDisplay}/token`);
          console.log(`   💲 买入价格: ${buyPriceDisplay}/token`);
          console.log(`   📈 净盈亏: ${profit > 0 ? '+' : ''}${profit.toFixed(4)} SOL (${profit > 0 ? '+' : ''}${profitPercentage.toFixed(2)}%) [已扣除所有费用]`);
          const holdingHours = Math.floor(holdingTimeMs / (1000 * 60 * 60));
          const holdingMinutes = Math.floor((holdingTimeMs % (1000 * 60 * 60)) / (1000 * 60));
          const holdingSeconds = Math.floor((holdingTimeMs % (1000 * 60)) / 1000);
          const holdingMilliseconds = holdingTimeMs % 1000;
          console.log(`   ⏱️ 持仓时间: ${holdingHours}h ${holdingMinutes}m ${holdingSeconds}s ${holdingMilliseconds}ms`);
          console.log(`   🔄 持仓状态更新: 有持仓 → 无持仓`);
          
          // 模拟交易信息
          const mockTransactionId = `SELL_${tokenAddress.slice(0, 8)}_${Date.now()}`;
          const mockSlippage = Math.random() * 0.015; // 0-1.5%随机滑点
          const mockGasFee = 0.000005; // 模拟5000 lamports gas费
          
          console.log(`   📋 交易ID: ${mockTransactionId}`);
          console.log(`   📈 滑点: ${(mockSlippage * 100).toFixed(2)}%`);
          console.log(`   ⛽ Gas: ${mockGasFee.toFixed(8)} SOL`);
          
          // 发送增强的Telegram通知
          await this.telegramNotifier.notifySell(
            tokenAddress,
            fullPosition,
            solReceived,
            actualSellPrice,
            instance.currentHolding.buyPrice,
            instance.currentHolding.buySolAmount,
            Math.floor(holdingTimeMs / (1000 * 60)), // 转换为分钟
            sellPrediction,
            mockTransactionId,
            mockSlippage,
            mockGasFee
          );
          
          // 更新统计
          instance.stats.currentPosition = 0; // 已全仓卖出，持仓归零
          instance.stats.totalTrades++;
          instance.stats.totalPnL += profit;
          
          if (profit > 0) {
            instance.stats.successfulTrades++;
          }
          
          // 完全清空持仓信息，准备下次1买1卖
          instance.currentHolding = null;
          
          // 通知trading bot
          await this.notifyTradingBot(tokenAddress, 'sell', solReceived);
          
          // 🔥 卖出完成后，从监控队列中移除该token
          console.log(`🎯 卖出完成，从监控队列中移除该Token...`);
          await this.removeTokenFromMonitoring(tokenAddress, '卖出交易完成');
          
        } else {
          console.log(`❌ 卖出失败 ${tokenAddress.slice(0, 8)}...: ${result.error}`);
        }
        
      } catch (error) {
        console.error(`❌ 卖出错误 ${tokenAddress.slice(0, 8)}...:`, error);
      } finally {
        // 🔥 释放交易锁
        instance.isTrading = false;
        console.log(`🔓 释放交易锁: 卖出完成 ${tokenAddress.slice(0, 8)}...`);
      }
    }
    else {
      // 记录为什么没有交易，增强版
      if (hasPosition) {
        if (sellPrediction < config.models.sellThreshold) {
          console.log(`⏸️ 有持仓但卖出预测不足: ${(sellPrediction*100).toFixed(1)}% < ${(config.models.sellThreshold*100).toFixed(1)}%`);
        } else {
          console.log(`⚠️ 有持仓且卖出预测足够，但没有进入卖出分支 - 需要检查逻辑`);
        }
      } else {
        if (buyPrediction < config.models.buyThreshold) {
          console.log(`⏸️ 无持仓但买入预测不足: ${(buyPrediction*100).toFixed(1)}% < ${(config.models.buyThreshold*100).toFixed(1)}%`);
        } else {
          console.log(`⚠️ 无持仓且买入预测足够，但没有进入买入分支 - 需要检查逻辑`);
        }
      }
    }
  }

  /**
   * 通知trading bot新的交易
   */
  private async notifyTradingBot(tokenAddress: string, action: 'buy' | 'sell', amount: number): Promise<void> {
    const transaction: TransactionData = {
      timestamp: new Date(),
      action: action === 'buy' ? 1 : 0,
      sol_amount: amount,
      usd_amount: amount * 150, // 假设SOL价格
      is_target_wallet: false, // 这是我们自己的交易
      wallet: 'paper_trading',
      block_number: Date.now()
    };

    // 🔥 已禁用TradingBot - 不再转发交易通知
    // await this.tradingBot.onNewTransaction(tokenAddress, transaction);
    console.log(`📊 模拟交易通知已记录: ${tokenAddress.slice(0, 8)}... ${action.toUpperCase()} ${amount.toFixed(4)} SOL`);
  }

  /**
   * 停止系统 - 增强版，包含最终统计发送
   */
  public async stop(): Promise<void> {
    this.isRunning = false;
    
    console.log('🛑 停止Paper Trading系统...');
    
    // 首先清理所有持仓
    await this.liquidateAllPositions();
    
    if (this.subscriptionUpdateTimer) {
      clearInterval(this.subscriptionUpdateTimer);
    }
    // 定时器已禁用，无需清理
    // if (this.predictionTimer) {
    //   clearInterval(this.predictionTimer);
    // }
    // if (this.tokenStatsTimer) {
    //   clearInterval(this.tokenStatsTimer);
    // }
    if (this.currentStream) {
      this.currentStream.end();
    }
    
    // 发送最终统计
    const finalStats = this.getFinalStats();
    await this.telegramNotifier.notifyDailyStats(finalStats);
    
    // 停止子系统
    // await this.tradingBot.stop(); // 🔥 已禁用TradingBot
    this.subscriptionManager.stop();
    
    // 停止Telegram Webhook服务器
    await this.telegramNotifier.stopWebhook();
    
    // 清理所有token实例
    for (const instance of this.tokenInstances.values()) {
      instance.isActive = false;
    }
    
    console.log('🛑 动态交易管理器已停止');
  }

  /**
   * 清理所有持仓 - 增强版，包含Telegram通知
   */
  private async liquidateAllPositions(): Promise<void> {
    console.log('💼 正在清理所有持仓...');
    
    let totalPositions = 0;
    let successfulLiquidations = 0;
    let totalPnL = 0;

    for (const [tokenAddress, instance] of this.tokenInstances) {
      if (instance.currentHolding && instance.currentHolding.amount > 0) {
        totalPositions++;
        console.log(`💸 清仓Token: ${tokenAddress.slice(0, 8)}... (持仓: ${instance.currentHolding.amount.toFixed(2)})`);
        
        try {
          // 获取最近成交价格
          const lastTradePrice = this.getLastTradePrice(tokenAddress);
          
          const result = await instance.swapManager.sell(
            tokenAddress,
            instance.currentHolding.amount,
            this.config.trading.slippageTolerance,
            lastTradePrice
          );
          
          if (result.success) {
            // 估算收到的SOL数量
            const estimatedPrice = result.actualPrice;
            if (!estimatedPrice) {
              console.log(`❌ 清仓失败：无法获取真实执行价格 ${tokenAddress.slice(0, 8)}...`);
              continue; // 跳过这个token的清仓
            }
            
            const solReceived = instance.currentHolding.amount * estimatedPrice;
            const pnl = solReceived - instance.currentHolding.buySolAmount;
            
            totalPnL += pnl;
            successfulLiquidations++;
            
            console.log(`✅ 清仓成功: ${solReceived.toFixed(4)} SOL (盈亏: ${pnl > 0 ? '+' : ''}${pnl.toFixed(4)} SOL)`);
            
            // 🔥 记录清仓交易价格
            this.recordRealPrice(
              tokenAddress,
              solReceived,
              instance.currentHolding.amount,
              'sell',
              'own_trade'
            );
            
            // 🔥 记录清仓交易到交易历史 - 这很重要！
            instance.tradingHistory.push({
              type: 'sell',
              timestamp: new Date(),
              tokenAmount: instance.currentHolding.amount,
              solAmount: solReceived,
              price: estimatedPrice,
              prediction: 0.5 // 清仓不是基于AI预测
            });
            
            // 发送Telegram清仓通知
            await this.telegramNotifier.notifyLiquidation(
              tokenAddress,
              '系统停止清仓',
              instance.currentHolding.amount,
              solReceived,
              pnl
            );
            
            // 更新状态
            instance.stats.currentPosition = 0;
            instance.stats.totalTrades++;
            instance.stats.totalPnL += pnl;
            
            if (pnl > 0) {
              instance.stats.successfulTrades++;
            }
            
            // 清空持仓
            instance.currentHolding = null;
            
            // 通知trading bot
            await this.notifyTradingBot(tokenAddress, 'sell', solReceived);
            
          } else {
            console.log(`❌ 清仓失败: ${result.error}`);
          }
          
        } catch (error) {
          console.error(`❌ 清仓错误 ${tokenAddress.slice(0, 8)}...:`, error);
        }
      }
    }

    if (totalPositions > 0) {
      console.log(`\n📊 清仓完成:`);
      console.log(`   💼 总持仓数: ${totalPositions}`);
      console.log(`   ✅ 成功清仓: ${successfulLiquidations}`);
      console.log(`   💰 清仓总盈亏: ${totalPnL > 0 ? '+' : ''}${totalPnL.toFixed(4)} SOL`);
      console.log(`   📈 清仓成功率: ${((successfulLiquidations / totalPositions) * 100).toFixed(1)}%`);
      
      // 发送最终统计到Telegram
      await this.telegramNotifier.notifySystem(
        `🛑 系统停止 - 最终清仓统计\n💼 总持仓: ${totalPositions}\n✅ 成功清仓: ${successfulLiquidations}\n💰 总盈亏: ${totalPnL > 0 ? '+' : ''}${totalPnL.toFixed(4)} SOL`
      );
    } else {
      console.log('✅ 无持仓需要清理');
    }
  }

  /**
   * 强制清仓指定token - 增强版，包含Telegram通知
   */
  public async forceLiquidateToken(tokenAddress: string): Promise<boolean> {
    const instance = this.tokenInstances.get(tokenAddress);
    if (!instance || !instance.currentHolding || instance.currentHolding.amount === 0) {
      console.log(`⚠️ Token ${tokenAddress.slice(0, 8)}... 无持仓`);
      return false;
    }

    console.log(`🔴 强制清仓 ${tokenAddress.slice(0, 8)}... (持仓: ${instance.currentHolding.amount.toFixed(2)})`);

    try {
      // 获取最近成交价格
      const lastTradePrice = this.getLastTradePrice(tokenAddress);
      
      const result = await instance.swapManager.sell(
        tokenAddress,
        instance.currentHolding.amount,
        this.config.trading.slippageTolerance,
        lastTradePrice
      );
      
      if (result.success) {
        const estimatedPrice = result.actualPrice || 0.000001; // 🔥 修复：使用更小的默认价格
        const solReceived = instance.currentHolding.amount * estimatedPrice;
        const pnl = solReceived - instance.currentHolding.buySolAmount;
        
        console.log(`✅ 强制清仓成功: ${solReceived.toFixed(4)} SOL (盈亏: ${pnl > 0 ? '+' : ''}${pnl.toFixed(4)} SOL)`);
        
        // 🔥 记录强制清仓交易到交易历史
        instance.tradingHistory.push({
          type: 'sell',
          timestamp: new Date(),
          tokenAmount: instance.currentHolding.amount,
          solAmount: solReceived,
          price: estimatedPrice,
          prediction: 0.5 // 强制清仓不是基于AI预测
        });
        
        // 发送Telegram通知
        await this.telegramNotifier.notifyLiquidation(
          tokenAddress,
          '手动强制清仓',
          instance.currentHolding.amount,
          solReceived,
          pnl
        );
        
        // 更新状态
        instance.stats.currentPosition = 0;
        instance.stats.totalTrades++;
        instance.stats.totalPnL += pnl;
        
        if (pnl > 0) {
          instance.stats.successfulTrades++;
        }
        
        // 清空持仓
        instance.currentHolding = null;
        
        await this.notifyTradingBot(tokenAddress, 'sell', solReceived);
        return true;
        
      } else {
        console.log(`❌ 强制清仓失败: ${result.error}`);
        return false;
      }
      
    } catch (error) {
      console.error(`❌ 强制清仓错误 ${tokenAddress.slice(0, 8)}...:`, error);
      return false;
    }
  }

  /**
   * 获取最终统计数据 - 修复版：正确计算交易对和盈亏
   */
  private getFinalStats() {
    let totalOperations = 0; // 总操作数（买入+卖出）
    let completedTradePairs = 0; // 完成的交易对数
    let winningTrades = 0;
    let losingTrades = 0;
    let totalProfit = 0;
    
    console.log(`📊 计算最终统计数据...`);
    
    for (const [tokenAddress, instance] of this.tokenInstances) {
      console.log(`   🪙 Token ${tokenAddress.slice(0, 8)}... 交易历史分析:`);
      
      // 统计总操作数
      const tokenTotalOps = instance.tradingHistory.length;
      totalOperations += tokenTotalOps;
      
      // 累计总盈亏
      totalProfit += instance.stats.totalPnL;
      
      console.log(`      📋 操作历史: ${tokenTotalOps} 笔 (买入+卖出)`);
      console.log(`      💰 Token盈亏: ${instance.stats.totalPnL.toFixed(4)} SOL`);
      
      // 分析交易历史，计算完成的交易对
      const buyTrades = instance.tradingHistory.filter(trade => trade.type === 'buy');
      const sellTrades = instance.tradingHistory.filter(trade => trade.type === 'sell');
      
      console.log(`      📈 买入记录: ${buyTrades.length} 笔`);
      console.log(`      📉 卖出记录: ${sellTrades.length} 笔`);
      
      // 为每笔卖出交易找到对应的买入交易，计算盈亏
      for (const sellTrade of sellTrades) {
        // 找到这笔卖出之前最近的买入交易
        const correspondingBuyTrade = buyTrades
          .filter(buyTrade => buyTrade.timestamp < sellTrade.timestamp)
          .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0]; // 最近的买入
        
        if (correspondingBuyTrade) {
          completedTradePairs++;
          
          // 计算这笔交易的盈亏
          const tradeProfit = sellTrade.solAmount - correspondingBuyTrade.solAmount;
          
          console.log(`      📊 交易对 ${completedTradePairs}: 买入${correspondingBuyTrade.solAmount.toFixed(4)} -> 卖出${sellTrade.solAmount.toFixed(4)} = ${tradeProfit > 0 ? '+' : ''}${tradeProfit.toFixed(4)} SOL`);
          
          if (tradeProfit > 0) {
            winningTrades++;
          } else if (tradeProfit < 0) {
            losingTrades++;
          }
          // 如果tradeProfit === 0，则既不算盈利也不算亏损
        } else {
          console.log(`      ⚠️ 发现未配对的卖出交易: ${sellTrade.solAmount.toFixed(4)} SOL (时间: ${sellTrade.timestamp.toLocaleTimeString()})`);
        }
      }
      
      // 检查是否有未配对的买入交易
      const unpaireBuyTrades = buyTrades.filter(buyTrade => {
        // 检查是否有对应的卖出交易
        return !sellTrades.some(sellTrade => sellTrade.timestamp > buyTrade.timestamp);
      });
      
      if (unpaireBuyTrades.length > 0) {
        console.log(`      ⚠️ 发现 ${unpaireBuyTrades.length} 笔未配对的买入交易 (可能仍有持仓)`);
        unpaireBuyTrades.forEach(buyTrade => {
          console.log(`         📈 未清仓买入: ${buyTrade.solAmount.toFixed(4)} SOL (${buyTrade.timestamp.toLocaleTimeString()})`);
        });
      }
    }
    
    const winRate = completedTradePairs > 0 ? (winningTrades / completedTradePairs) * 100 : 0;
    const activeTokens = this.tokenInstances.size;
    
    console.log(`📊 最终统计汇总:`);
    console.log(`   📋 总操作数: ${totalOperations} 笔 (所有买入+卖出)`);
    console.log(`   🔄 完成交易对: ${completedTradePairs} 对 (买入->卖出配对)`);
    console.log(`   ✅ 盈利交易对: ${winningTrades} 对`);
    console.log(`   ❌ 亏损交易对: ${losingTrades} 对`);
    console.log(`   📈 胜率: ${winRate.toFixed(1)}%`);
    console.log(`   💰 总盈亏: ${totalProfit > 0 ? '+' : ''}${totalProfit.toFixed(4)} SOL`);
    console.log(`   🪙 活跃Token: ${activeTokens} 个`);
    
    return {
      totalTrades: completedTradePairs, // 返回完成的交易对数，这是最有意义的指标
      totalOperations, // 新增：总操作数
      winningTrades,
      losingTrades,
      totalProfit,
      winRate,
      activeTokens
    };
  }

  /**
   * 处理GRPC流
   */
  private async handleStream(client: Client, args: SubscribeRequest): Promise<void> {
    while (this.isRunning) {
      try {
        console.log(`🔗 尝试建立GRPC流连接...`);
        const stream = await client.subscribe();
        this.currentStream = stream;
        console.log(`✅ GRPC流连接已建立`);

        const streamClosed = new Promise<void>((resolve, reject) => {
          stream.on("error", (error) => {
            console.log("❌ GRPC Stream ERROR:", error);
            reject(error);
            stream.end();
          });
          stream.on("end", () => {
            console.log("🔚 GRPC流已结束");
            resolve();
          });
          stream.on("close", () => {
            console.log("🔐 GRPC流已关闭");
            resolve();
          });
        });

        let lastReceivedTxTime = 0;
        let totalDataReceived = 0;
        let connectionStartTime = Date.now();
        
        // 定期状态显示
        const statusInterval = setInterval(() => {
          if (!this.isRunning) {
            clearInterval(statusInterval);
            return;
          }
          
          const now = Date.now();
          const secondsSinceLastTx = (now - lastReceivedTxTime) / 1000;
          const secondsSinceStart = (now - connectionStartTime) / 1000;
          
          console.log(`\n📊 GRPC连接状态 (${Math.floor(secondsSinceStart / 60)}分${Math.floor(secondsSinceStart % 60)}秒):`);
          console.log(`   📡 连接状态: ${this.currentStream ? '✅ 已连接' : '❌ 未连接'}`);
          console.log(`   📦 数据包总数: ${totalDataReceived}`);
          
          if (lastReceivedTxTime > 0) {
            console.log(`   📡 上次交易: ${secondsSinceLastTx.toFixed(0)} 秒前`);
          } else {
            console.log(`   📡 交易状态: ⚠️ 无交易数据`);
          }
          
        }, STATUS_UPDATE_INTERVAL);

        // 处理数据
        stream.on("data", async (data) => {
          if (!this.isRunning) return;
          
          totalDataReceived++;
          
          if (data?.transaction) {
            lastReceivedTxTime = Date.now();
            
            const txn = TXN_FORMATTER.formTransactionFromJson(data.transaction, Date.now());
            const parsedTxn = this.decodePumpFunTxn(txn);
            if (!parsedTxn) {
              return;
            }

            // 检查交易是否涉及目标钱包或监控的token
            const isTargetWalletInvolved = await this.isWalletInvolvedInTransaction(txn, TARGET_WALLET);
            const accountKeys = txn.transaction.message.staticAccountKeys?.map(key => key.toBase58()) || [];
            const monitoredTokensInvolved = Array.from(this.currentTokenSubscriptions).filter(token => 
              accountKeys.includes(token)
            );
            
            // 如果涉及监控的token但不涉及目标钱包，也要记录
            if (monitoredTokensInvolved.length > 0 && !isTargetWalletInvolved) {
              console.log(`\n🔍 发现监控Token的其他交易:`);
              const signature = txn.transaction.signatures[0] || 'unknown';
              console.log(`   📋 交易ID: ${signature}`);
              console.log(`   ⏰ 时间: ${new Date().toLocaleString()}`);
              console.log(`   🪙 涉及Token: ${monitoredTokensInvolved.map(t => t.slice(0, 8) + '...').join(', ')}`);
              
              // 提取token信息
              const tokenInfo = this.extractTokenInfoFromTransaction(parsedTxn);
              if (tokenInfo) {
                for (const token of tokenInfo.tokens) {
                  if (this.currentTokenSubscriptions.has(token.address) && token.solAmount && token.solAmount >= MIN_TRANSACTION_SOL) {
                    console.log(`   💰 Token交易: ${token.action} ${token.solAmount.toFixed(4)} SOL`);
                    console.log(`   🔢 Token数量: ${token.tokenAmount?.toFixed(2) || 'N/A'} tokens`);
                    
                    // 🔥 记录其他钱包的真实价格数据
                    if (token.solAmount && token.tokenAmount && token.tokenAmount > 0) {
                      this.recordRealPrice(
                        token.address,
                        token.solAmount,
                        token.tokenAmount,
                        token.action as 'buy' | 'sell',
                        'stream'
                      );
                    }
                    
                    // 将其他钱包的交易也记录到历史中，这对AI预测很重要
                    await this.subscriptionManager.processWalletTransaction(
                      signature,
                      new Date(),
                      typeof data.transaction?.slot === 'number' ? data.transaction.slot : 0,
                      token.address,
                      token.action,
                      token.solAmount || 0,
                      token.usdAmount || 0,
                      TARGET_WALLET // 这是目标钱包的交易
                    );
                    
                    console.log(`   📊 已更新Token交易历史`);
                    
                    // 🔥 其他钱包的交易也能触发AI预测
                    console.log(`🚀 其他钱包交易数据到达，立即尝试AI预测...`);
                    try {
                      await this.makePredictionForToken(token.address);
                    } catch (error) {
                      console.error(`❌ 立即AI预测失败 ${token.address.slice(0, 8)}...:`, error);
                    }
                  }
                }
              }
              console.log(`   🔗 交易链接: https://translator.shyft.to/tx/${signature}`);
              console.log('-'.repeat(60));
              return;
            }
            
            if (!isTargetWalletInvolved) return;

            const signature = txn.transaction.signatures[0] || 'unknown';
            const slot = data.transaction?.slot || 'unknown';
            const timestamp = new Date();

            console.log(`\n🎯 发现目标钱包交易:`);
            console.log(`   钱包: ${TARGET_WALLET.slice(0, 8)}...`);
            console.log(`   交易ID: ${signature}`);
            console.log(`   时间: ${timestamp.toLocaleString()}`);

            // 提取token信息
            const tokenInfo = this.extractTokenInfoFromTransaction(parsedTxn);
            if (tokenInfo) {
              for (const token of tokenInfo.tokens) {
                // 再次检查SOL金额，确保没有小额交易漏过
                if (token.solAmount && token.solAmount < MIN_TRANSACTION_SOL) {
                  console.log(`🚫 跳过小额交易Token: ${token.address.slice(0, 8)}... (${token.solAmount.toFixed(6)} SOL)`);
                  continue;
                }

                console.log(`   🪙 Token: ${token.address.slice(0, 8)}... (${token.action})`);
                console.log(`   💰 金额: ${token.solAmount?.toFixed(4) || 'N/A'} SOL`);
                console.log(`   🔢 Token数量: ${token.tokenAmount?.toFixed(2) || 'N/A'} tokens`);
                
                // 🔥 记录真实价格数据到价格记录系统
                if (token.solAmount && token.tokenAmount && token.tokenAmount > 0) {
                  this.recordRealPrice(
                    token.address,
                    token.solAmount,
                    token.tokenAmount,
                    token.action as 'buy' | 'sell',
                    'stream'
                  );
                }
                
                // 如果是已监控的token，特别标记
                if (this.currentTokenSubscriptions.has(token.address)) {
                  console.log(`   🔥 这是已监控的Token - 将更新交易历史并立即AI预测！`);
                  
                  // 🔥 检查是否是缓冲队列中的token
                  if (this.sellOnlyBufferQueue.has(token.address)) {
                    console.log(`📨 缓冲队列token收到数据: ${token.address.slice(0, 8)}... ${token.action} ${token.solAmount?.toFixed(4)}SOL`);
                    console.log(`   缓冲队列大小: ${this.sellOnlyBufferQueue.size}`);
                    console.log(`   将触发AI预测...`);
                  }
                }
                
                // 记录钱包交易到动态订阅管理器
                await this.subscriptionManager.processWalletTransaction(
                  signature,
                  timestamp,
                  typeof slot === 'number' ? slot : 0,
                  token.address,
                  token.action,
                  token.solAmount || 0,
                  token.usdAmount || 0,
                  TARGET_WALLET // 这是目标钱包的交易
                );

                // 将交易数据传递给trading bot进行AI处理
                await this.processTokenTransaction(token.address, {
                  timestamp: timestamp,
                  action: token.action === 'buy' ? 1 : token.action === 'sell' ? 0 : 2,
                  sol_amount: token.solAmount || 0,
                  usd_amount: token.usdAmount || 0,
                  is_target_wallet: true,
                  wallet: TARGET_WALLET,
                  block_number: typeof slot === 'number' ? slot : 0
                });
                
                // 🔥 立即尝试AI预测 - 事件驱动
                console.log(`🚀 新交易数据到达，立即尝试AI预测...`);
                try {
                  await this.makePredictionForToken(token.address);
                } catch (error) {
                  console.error(`❌ 立即AI预测失败 ${token.address.slice(0, 8)}...:`, error);
                }
              }

              // 更新token订阅
              await this.updateTokenSubscriptions();
            }

            console.log(`   🔗 交易链接: https://translator.shyft.to/tx/${signature}`);
            console.log('-'.repeat(80));
          }
        });

        // 发送订阅请求
        console.log(`📤 发送初始订阅请求...`);
        await new Promise<void>((resolve, reject) => {
          stream.write(args, (err: any) => {
            if (err === null || err === undefined) {
              console.log(`✅ 订阅请求发送成功`);
              resolve();
            } else {
              console.error(`❌ 订阅请求发送失败:`, err);
              reject(err);
            }
          });
        });

        console.log(`🎧 开始监听数据流...`);
        await streamClosed;
        
      } catch (error) {
        if (this.isRunning) {
          console.error("❌ Stream error, restarting in 5 seconds...", error);
          await new Promise((resolve) => setTimeout(resolve, 5000));
        }
      }
    }
  }

  /**
   * 解析PumpFun交易
   */
  private decodePumpFunTxn(tx: VersionedTransactionResponse) {
    if (tx.meta?.err) return null;

    const originalConsole = console;
    const silentConsole = { log: () => {}, error: () => {}, warn: () => {}, info: () => {}, debug: () => {} };
    
    // @ts-ignore
    global.console = silentConsole;
    
    try {
      const paredIxs = PUMP_FUN_IX_PARSER.parseTransactionData(
        tx.transaction.message,
        tx.meta.loadedAddresses
      );
      
      const filteredIxs = paredIxs.filter(ix => !ix.programId.toString().includes('ComputeBudget'));
      const pumpFunIxs = filteredIxs.filter((ix) => ix.programId.equals(PUMP_FUN_AMM_PROGRAM_ID));

      if (pumpFunIxs.length === 0) return null;
      
      const events = PUMP_FUN_EVENT_PARSER.parseEvent(tx);
      const result = { instructions: pumpFunIxs, events };
      bnLayoutFormatter(result);
      return result;
    } finally {
      // @ts-ignore
      global.console = originalConsole;
    }
  }

  /**
   * 检查钱包是否参与交易
   */
  private async isWalletInvolvedInTransaction(tx: VersionedTransactionResponse, wallet: string): Promise<boolean> {
    try {
      const accountKeys = tx.transaction.message.staticAccountKeys?.map(key => key.toBase58()) || [];
      return accountKeys.includes(wallet);
    } catch (error) {
      console.error('检查钱包参与交易时出错:', error);
      return false;
    }
  }

  /**
   * 从交易中提取token信息 - 增强版，过滤小额交易
   */
  private extractTokenInfoFromTransaction(parsedTxn: any): { tokens: Array<{ address: string, action: 'buy' | 'sell' | 'unknown', solAmount?: number, tokenAmount?: number, usdAmount?: number }> } | null {
    const tokens: Array<{ address: string, action: 'buy' | 'sell' | 'unknown', solAmount?: number, tokenAmount?: number, usdAmount?: number }> = [];

    try {
      if (parsedTxn.events && Array.isArray(parsedTxn.events)) {
        for (const event of parsedTxn.events) {
          let action: 'buy' | 'sell' | 'unknown' = 'unknown';
          let solAmount = 0;
          let tokenAmount = 0;

          if (event.name === 'BuyEvent') {
            action = 'buy';
            solAmount = parseFloat(event.data.quote_amount_in || 0) / 1e9;
            tokenAmount = parseFloat(event.data.base_amount_out || 0) / 1e6;
          } else if (event.name === 'SellEvent') {
            action = 'sell';
            solAmount = parseFloat(event.data.quote_amount_out || 0) / 1e9;
            tokenAmount = parseFloat(event.data.base_amount_in || 0) / 1e6;
          }

          // 过滤小额交易
          if (solAmount < MIN_TRANSACTION_SOL) {
            console.log(`🚫 过滤小额交易: ${solAmount.toFixed(6)} SOL < ${MIN_TRANSACTION_SOL} SOL (${action})`);
            continue; // 跳过小额交易
          }

          tokens.push({
            address: event.data.base_mint || 'unknown',
            action,
            solAmount,
            tokenAmount,
            usdAmount: solAmount * 150 // 假设SOL价格
          });
        }
      }

      if (parsedTxn.instructions) {
        for (let i = 0; i < parsedTxn.instructions.length; i++) {
          const ix = parsedTxn.instructions[i];
          if (ix.accounts && tokens[i]) {
            const baseMintAccount = ix.accounts.find((a: any) => a.name === 'base_mint');
            if (baseMintAccount) {
              tokens[i].address = baseMintAccount.pubkey;
            }
          }
        }
      }

      return tokens.length > 0 ? { tokens } : null;
    } catch (error) {
      console.error('提取token信息时出错:', error);
      return null;
    }
  }

  /**
   * 处理token交易数据，传递给trading bot
   */
  private async processTokenTransaction(tokenAddress: string, transaction: TransactionData): Promise<void> {
    try {
      // 🔥 已禁用TradingBot - 不再转发交易数据
      // await this.tradingBot.onNewTransaction(tokenAddress, transaction);
      console.log(`📊 Token交易数据已记录: ${tokenAddress.slice(0, 8)}... ${transaction.action === 1 ? 'BUY' : 'SELL'} ${transaction.sol_amount.toFixed(4)} SOL`);
    } catch (error) {
      console.error(`❌ 处理token交易数据失败 (${tokenAddress.slice(0, 8)}...):`, error);
    }
  }

  /**
   * 更新token订阅 - 修复为OR逻辑，包含通知
   */
  private async updateTokenSubscriptions(): Promise<void> {
    const activeTokens = this.subscriptionManager.getActiveTokens();
    const newTokenSet = new Set(activeTokens);

    // 检查是否有变化
    const tokensToAdd = activeTokens.filter(token => !this.currentTokenSubscriptions.has(token));
    const tokensToRemove = Array.from(this.currentTokenSubscriptions).filter(token => !newTokenSet.has(token));

    if (tokensToAdd.length === 0 && tokensToRemove.length === 0) {
      return; // 没有变化
    }

    console.log(`🔄 更新token订阅: +${tokensToAdd.length} -${tokensToRemove.length}`);
    
    // 发送取消订阅通知
    for (const tokenAddress of tokensToRemove) {
      // await this.telegramNotifier.notifySubscription(tokenAddress, 'unsubscribe'); // 已移除
    }
    
    // 更新当前订阅集合
    this.currentTokenSubscriptions = newTokenSet;
    
    // 实现动态订阅更新逻辑 - 使用OR逻辑
    if (this.currentStream && (tokensToAdd.length > 0 || tokensToRemove.length > 0)) {
      console.log(`📤 通过stream.write()动态更新订阅 (+${tokensToAdd.length} -${tokensToRemove.length})...`);
      
      // 构建包含所有地址的列表：目标钱包 + 所有活跃token
      const allIncludeAddresses = [
        TARGET_WALLET, // 目标钱包
        ...Array.from(this.currentTokenSubscriptions) // 所有活跃token
      ];
      
      console.log(`📋 订阅地址列表:`);
      console.log(`   🎯 必须条件: PumpFun AMM程序 (${PUMP_FUN_AMM_PROGRAM_ID.toBase58().slice(0, 8)}...)`);
      console.log(`   📋 OR条件 (${allIncludeAddresses.length} 个):`);
      console.log(`       🎯 目标钱包: ${TARGET_WALLET.slice(0, 8)}...`);
      
      if (this.currentTokenSubscriptions.size > 0) {
        console.log(`       🪙 监控Token (${this.currentTokenSubscriptions.size} 个):`);
        Array.from(this.currentTokenSubscriptions).slice(0, 5).forEach((token, i) => {
          console.log(`          ${i + 1}. ${token.slice(0, 8)}...`);
        });
        if (this.currentTokenSubscriptions.size > 5) {
          console.log(`          ... 还有 ${this.currentTokenSubscriptions.size - 5} 个`);
        }
      }
      
      // 动态更新订阅 - 正确的逻辑：必须是PumpFun + (目标钱包 OR 监控token)
      const updatedSubscription = {
        accounts: {},
        slots: {},
        transactions: {
          pumpFun_updated: {
            vote: false,
            failed: false,
            signature: undefined,
            accountInclude: allIncludeAddresses, // OR逻辑：包含目标钱包或任何监控token
            accountExclude: [],
            accountRequired: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()], // AND逻辑：必须是PumpFun AMM的交易
          },
        },
        transactionsStatus: {},
        entry: {},
        blocks: {},
        blocksMeta: {},
        accountsDataSlice: [],
        ping: undefined,
        commitment: CommitmentLevel.PROCESSED,
      };

      this.currentStream.write(updatedSubscription, (err: any) => {
        if (err === null || err === undefined) {
          console.log(`✅ 动态订阅更新成功!`);
          console.log(`   📊 必须: PumpFun AMM程序`);
          console.log(`   📊 包含: ${allIncludeAddresses.length} 个地址 (OR逻辑)`);
          console.log(`   📊 组合: 必须是PumpFun + (钱包 OR Token)`);
          this.stats.subscriptionUpdates++;
        } else {
          console.error(`❌ 动态订阅更新失败:`, err);
        }
      });
    }
  }

  // =============================================================================
  // TradingDataProvider 接口实现 - 为Telegram机器人提供数据
  // =============================================================================

  /**
   * 获取当前活跃的Token列表
   */
  public getActiveTokens(): string[] {
    return this.subscriptionManager.getActiveTokens();
  }

  /**
   * 获取每个Token的交易统计
   */
  public getTokenStats(): Array<{
    tokenAddress: string;
    totalTrades: number;
    totalPnL: number;
    transactionCount: number;
    winRate: number;
  }> {
    const tokenStats: Array<{
      tokenAddress: string;
      totalTrades: number;
      totalPnL: number;
      transactionCount: number;
      winRate: number;
    }> = [];

    for (const [tokenAddress, instance] of this.tokenInstances) {
      // 计算胜率
      const sellTrades = instance.tradingHistory.filter(trade => trade.type === 'sell');
      const winningTrades = sellTrades.filter(trade => {
        // 找到对应的买入交易来计算是否盈利
        const buyTrade = instance.tradingHistory
          .reverse()
          .find(t => t.type === 'buy' && t.timestamp < trade.timestamp);
        if (buyTrade) {
          return trade.solAmount > buyTrade.solAmount; // 简化的盈利判断
        }
        return false;
      });

      const winRate = sellTrades.length > 0 ? winningTrades.length / sellTrades.length : 0;

      // 获取交易数量 - 从动态订阅管理器获取该token的交易历史
      const tokenTransactions = this.subscriptionManager.getWalletTransactionHistory(24)
        .filter(tx => tx.tokenAddress === tokenAddress);

      tokenStats.push({
        tokenAddress,
        totalTrades: instance.stats.totalTrades,
        totalPnL: instance.stats.totalPnL,
        transactionCount: tokenTransactions.length,
        winRate
      });
    }

    return tokenStats;
  }

  /**
   * 获取当前所有持仓信息
   */
  public getCurrentPositions(): Array<{
    tokenAddress: string;
    position: number;
    pnl: number;
    buyPrice: number;
    currentPrice: number;
    holdingTime: number;
  }> {
    const positions: Array<{
      tokenAddress: string;
      position: number;
      pnl: number;
      buyPrice: number;
      currentPrice: number;
      holdingTime: number;
    }> = [];

    for (const [tokenAddress, instance] of this.tokenInstances) {
      if (instance.currentHolding && instance.currentHolding.amount > 0) {
        // 简化的当前价格计算 - 实际应该从市场获取
        const currentPrice = instance.currentHolding.buyPrice * 1.02; // 假设当前价格
        const currentValue = instance.currentHolding.amount * currentPrice;
        const unrealizedPnL = currentValue - instance.currentHolding.buySolAmount;
        const holdingTimeMs = Date.now() - instance.currentHolding.buyTime.getTime();
        const holdingTimeMinutes = Math.floor(holdingTimeMs / (1000 * 60));

        positions.push({
          tokenAddress,
          position: instance.currentHolding.amount,
          pnl: unrealizedPnL,
          buyPrice: instance.currentHolding.buyPrice,
          currentPrice,
          holdingTime: holdingTimeMinutes
        });
      }
    }

    return positions;
  }

  /**
   * 打印Token详细统计 - 每2分钟执行一次，同时发送到Telegram
   */
  private async printTokenStats(): Promise<void> {
    if (!this.isRunning) return;
    
    const now = new Date();
    console.log(`\n📊 ================ Token统计报告 [${now.toLocaleTimeString()}] ================`);
    
    if (this.tokenInstances.size === 0) {
      const message = `ℹ️ 暂无活跃Token交易实例\n📋 监控中Token: ${this.subscriptionManager.getActiveTokens().length} 个\n⏰ 等待AI预测数据累积...`;
      console.log(`   ${message}`);
      console.log(`==========================================\n`);
      
      // 发送到Telegram
      await this.telegramNotifier.notifySystem(`📊 Token统计 [${now.toLocaleTimeString()}]\n${message}`);
      return;
    }
    
    let totalTokens = 0;
    let totalTrades = 0;
    let totalProfit = 0;
    let activePositions = 0;
    
    // 构建Telegram消息
    let telegramMessage = `📊 Token统计报告 [${now.toLocaleTimeString()}]\n\n`;
    
    console.log(`   📈 活跃Token详情:`);
    console.log(`   ┌─────────────┬──────┬────────────┬──────┬──────────┬─────────┐`);
    console.log(`   │ Token地址   │ 交易 │ 盈亏(SOL)  │ 胜率 │ 持仓状态 │ 预测器  │`);
    console.log(`   ├─────────────┼──────┼────────────┼──────┼──────────┼─────────┤`);
    
    for (const [tokenAddress, instance] of this.tokenInstances) {
      totalTokens++;
      
      // 计算交易次数
      const trades = instance.stats.totalTrades;
      totalTrades += trades;
      
      // 计算盈亏
      const pnl = instance.stats.totalPnL;
      totalProfit += pnl;
      
      // 计算胜率
      const sellTrades = instance.tradingHistory.filter(trade => trade.type === 'sell');
      const winningTrades = sellTrades.filter(trade => {
        // 找到对应的买入交易
        const buyTrade = instance.tradingHistory
          .slice()
          .reverse()
          .find(t => t.type === 'buy' && t.timestamp < trade.timestamp);
        return buyTrade && trade.solAmount > buyTrade.solAmount;
      });
      const winRate = sellTrades.length > 0 ? (winningTrades.length / sellTrades.length * 100) : 0;
      
      // 持仓状态
      const hasPosition = instance.currentHolding && instance.currentHolding.amount > 0;
      if (hasPosition) activePositions++;
      const positionStatus = hasPosition ? '✅有持仓' : '❌无持仓';
      
      // 预测器数据状态
      const buyQueueStatus = instance.buyPredictor.getQueueStatus();
      const sellQueueStatus = instance.sellPredictor.getQueueStatus();
      const predictorStatus = `${buyQueueStatus.currentSize}|${sellQueueStatus.currentSize}`;
      
      // 格式化显示
      const tokenShort = tokenAddress.slice(0, 11);
      const tradesStr = trades.toString().padStart(4);
      const pnlStr = pnl.toFixed(4).padStart(10);
      const winRateStr = winRate.toFixed(1).padStart(4) + '%';
      
      console.log(`   │ ${tokenShort} │ ${tradesStr} │ ${pnlStr} │ ${winRateStr} │ ${positionStatus} │ ${predictorStatus.padStart(7)} │`);
      
      // 构建Telegram消息的Token信息
      telegramMessage += `🪙 ${tokenShort}...\n`;
      telegramMessage += `   📈 交易: ${trades}笔 | 盈亏: ${pnl > 0 ? '+' : ''}${pnl.toFixed(4)} SOL\n`;
      telegramMessage += `   📊 胜率: ${winRate.toFixed(1)}% | ${positionStatus}\n`;
      telegramMessage += `   🤖 预测器: 买${buyQueueStatus.currentSize}/卖${sellQueueStatus.currentSize}\n`;
      
      // 如果有持仓，显示详细信息
      if (hasPosition && instance.currentHolding) {
        const holdingTime = Math.floor((now.getTime() - instance.currentHolding.buyTime.getTime()) / (1000 * 60));
        const unrealizedPnL = (instance.currentHolding.amount * instance.currentHolding.buyPrice * 1.01) - instance.currentHolding.buySolAmount; // 假设小幅上涨
        console.log(`   │   持仓详情: ${instance.currentHolding.amount.toFixed(2)} tokens, 持仓${holdingTime}分钟, 浮盈${unrealizedPnL.toFixed(4)} SOL              │`);
        telegramMessage += `   💼 持仓: ${instance.currentHolding.amount.toFixed(2)} tokens (${holdingTime}分钟)\n`;
        telegramMessage += `   💰 浮盈: ${unrealizedPnL > 0 ? '+' : ''}${unrealizedPnL.toFixed(4)} SOL\n`;
      }
      telegramMessage += '\n';
    }
    
    console.log(`   └─────────────┴──────┴────────────┴──────┴──────────┴─────────┘`);
    
    // 总体统计
    const avgPnL = totalTokens > 0 ? (totalProfit / totalTokens).toFixed(4) : '0.0000';
    const overallStats = [
      `🪙 活跃Token: ${totalTokens} 个`,
      `📈 总交易数: ${totalTrades} 笔`,
      `💰 总盈亏: ${totalProfit > 0 ? '+' : ''}${totalProfit.toFixed(4)} SOL`,
      `💼 活跃持仓: ${activePositions} 个`,
      `📊 平均盈亏: ${avgPnL} SOL/Token`
    ];
    
    console.log(`   📊 总体统计:`);
    overallStats.forEach(stat => console.log(`      ${stat}`));
    
    // AI预测配置提醒
    const aiConfig = [
      `📈 买入阈值: ${(this.config.models.buyThreshold * 100).toFixed(1)}%`,
      `📉 卖出阈值: ${(this.config.models.sellThreshold * 100).toFixed(1)}%`,
      `⏱️ 预测间隔: ${AI_CONFIG.predictionInterval / 1000} 秒`
    ];
    
    console.log(`   🤖 AI配置:`);
    aiConfig.forEach(config => console.log(`      ${config}`));
    
    // 添加到Telegram消息
    telegramMessage += `📊 总体统计:\n${overallStats.join('\n')}\n\n`;
    telegramMessage += `🤖 AI配置:\n${aiConfig.join('\n')}\n\n`;
    
    // 🔥 添加当前订阅Token列表
    telegramMessage += `📋 当前订阅Token列表 (${this.tokenMonitoringQueue.length}/${MAX_MONITORED_TOKENS}):\n`;
    if (this.tokenMonitoringQueue.length === 0) {
      telegramMessage += `   暂无订阅Token\n`;
    } else {
      this.tokenMonitoringQueue.forEach((item, i) => {
        const age = Math.floor((Date.now() - item.addedTime.getTime()) / (1000 * 60));
        const hasPosition = this.tokenInstances.get(item.address)?.currentHolding?.amount ? '💼' : '📊';
        telegramMessage += `   ${i + 1}. ${hasPosition} ${item.address.slice(0, 12)}... (${age}分钟前)\n`;
      });
    }
    telegramMessage += '\n';
    
    // 🔥 添加缓冲队列信息
    if (this.sellOnlyBufferQueue.size > 0) {
      telegramMessage += `🔄 缓冲队列 (只卖出不买入) - ${this.sellOnlyBufferQueue.size} 个token:\n`;
      for (const [tokenAddress, bufferInfo] of this.sellOnlyBufferQueue.entries()) {
        const age = Math.floor((Date.now() - bufferInfo.addedTime.getTime()) / (1000 * 60));
        const hasPosition = this.tokenInstances.get(tokenAddress)?.currentHolding?.amount ? '💼有持仓' : '📊无持仓';
        telegramMessage += `   🔄 ${tokenAddress.slice(0, 12)}... (${bufferInfo.reason})\n`;
        telegramMessage += `      ${hasPosition} | 缓冲${age}分钟\n`;
      }
      telegramMessage += '\n';
    }
    
    // 最近交易活动
    const allRecentTrades = [];
    for (const [tokenAddress, instance] of this.tokenInstances) {
      for (const trade of instance.tradingHistory.slice(-2)) { // 最近2笔交易
        allRecentTrades.push({
          token: tokenAddress.slice(0, 8),
          ...trade
        });
      }
    }
    
    if (allRecentTrades.length > 0) {
      console.log(`   📋 最近交易活动:`);
      telegramMessage += `📋 最近交易活动:\n`;
      
      const recentTrades = allRecentTrades
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, 5);
        
      recentTrades.forEach((trade, i) => {
        const timeAgo = Math.floor((now.getTime() - trade.timestamp.getTime()) / (1000 * 60));
        const typeIcon = trade.type === 'buy' ? '📈' : '📉';
        const tradeInfo = `${i + 1}. ${typeIcon} ${trade.token}... ${trade.type} ${trade.solAmount.toFixed(4)} SOL (${timeAgo}分钟前)`;
        console.log(`      ${tradeInfo}`);
        telegramMessage += `${tradeInfo}\n`;
      });
    }
    
    console.log(`==========================================\n`);
    
    // 🔥 发送到Telegram (使用队列系统，自动处理429错误)
    try {
      if (telegramMessage.length > 4000) {
        // 如果消息太长，分段发送
        const parts = this.splitTelegramMessage(telegramMessage, 4000);
        for (let i = 0; i < parts.length; i++) {
          await this.telegramNotifier.notifySystem(`${parts[i]}${i < parts.length - 1 ? '\n📄 (续下一条消息...)' : ''}`);
          // 🔥 移除延迟，队列系统会自动控制发送间隔
        }
      } else {
        await this.telegramNotifier.notifySystem(telegramMessage);
      }
      console.log(`📱 Token统计已加入Telegram队列`);
    } catch (error) {
      console.error(`❌ 发送Token统计到Telegram失败:`, error);
    }
  }
  
  /**
   * 分割Telegram消息以避免长度限制
   */
  private splitTelegramMessage(message: string, maxLength: number): string[] {
    if (message.length <= maxLength) return [message];
    
    const parts: string[] = [];
    const lines = message.split('\n');
    let currentPart = '';
    
    for (const line of lines) {
      if ((currentPart + line + '\n').length <= maxLength) {
        currentPart += line + '\n';
      } else {
        if (currentPart) parts.push(currentPart.trim());
        currentPart = line + '\n';
      }
    }
    
    if (currentPart) parts.push(currentPart.trim());
    return parts;
  }



  /**
   * 管理Token监控队列 - FIFO策略，限制数量上限
   */
  private async manageTokenMonitoringQueue(newTokenAddress: string): Promise<void> {
    // 检查是否已经在监控
    const existingIndex = this.tokenMonitoringQueue.findIndex(item => item.address === newTokenAddress);
    
    if (existingIndex !== -1) {
      // 如果已存在，更新时间（移到队列末尾）
      this.tokenMonitoringQueue.splice(existingIndex, 1);
      console.log(`🔄 Token ${newTokenAddress.slice(0, 8)}... 已在监控中，更新优先级`);
    }
    
    // 添加到队列末尾
    this.tokenMonitoringQueue.push({
      address: newTokenAddress,
      addedTime: new Date()
    });
    
    console.log(`➕ Token监控队列: 添加 ${newTokenAddress.slice(0, 8)}... (队列长度: ${this.tokenMonitoringQueue.length}/${MAX_MONITORED_TOKENS})`);
    
    // 检查是否超过上限
    while (this.tokenMonitoringQueue.length > MAX_MONITORED_TOKENS) {
      const removedToken = this.tokenMonitoringQueue.shift(); // 移除最旧的
      if (removedToken) {
        console.log(`📤 Token监控队列满，移除最旧Token: ${removedToken.address.slice(0, 8)}...`);
        await this.removeTokenFromMonitoring(removedToken.address, '队列满员被移除');
      }
    }
    
    // 显示当前队列状态
    console.log(`📋 当前监控队列 (${this.tokenMonitoringQueue.length}/${MAX_MONITORED_TOKENS}):`);
    this.tokenMonitoringQueue.forEach((item, i) => {
      const age = Math.floor((Date.now() - item.addedTime.getTime()) / (1000 * 60));
      console.log(`   ${i + 1}. ${item.address.slice(0, 8)}... (${age}分钟前加入)`);
    });
  }
  
  /**
   * 从监控中移除Token - 优化版：有持仓的token进入缓冲队列
   */
  private async removeTokenFromMonitoring(tokenAddress: string, reason: string): Promise<void> {
    console.log(`🗑️ 移除Token监控: ${tokenAddress.slice(0, 8)}... (原因: ${reason})`);
    
    const instance = this.tokenInstances.get(tokenAddress);
    const hasPosition = instance && instance.currentHolding && instance.currentHolding.amount > 0;
    
    if (hasPosition) {
      // 有持仓：进入缓冲队列，只卖出不买入
      console.log(`💼 检测到持仓，将Token加入缓冲队列 (只卖出不买入)`);
      this.sellOnlyBufferQueue.set(tokenAddress, {
        address: tokenAddress,
        addedTime: new Date(),
        reason: reason,
        lastCheckTime: new Date()
      });
      
      // 🔥 缓冲队列调试检查
      console.log(`🔍 缓冲队列检查: ${tokenAddress.slice(0, 8)}...`);
      console.log(`   在currentTokenSubscriptions中: ${this.currentTokenSubscriptions.has(tokenAddress)}`);
      console.log(`   实例isActive: ${instance?.isActive}`);
      
      // 🔥 确保token仍在订阅中
      if (!this.currentTokenSubscriptions.has(tokenAddress)) {
        this.currentTokenSubscriptions.add(tokenAddress);
        console.log(`✅ 重新添加缓冲队列token到订阅`);
      }
      
      // 从主监控队列移除，但保持实例活跃且继续订阅
      const queueIndex = this.tokenMonitoringQueue.findIndex(item => item.address === tokenAddress);
      if (queueIndex !== -1) {
        this.tokenMonitoringQueue.splice(queueIndex, 1);
      }
      
      console.log(`✅ Token ${tokenAddress.slice(0, 8)}... 已移入缓冲队列 (${reason})`);
      console.log(`   📋 缓冲队列状态: ${this.sellOnlyBufferQueue.size} 个待清仓token`);
      
    } else {
      // 无持仓：直接完全移除
      await this.completelyRemoveToken(tokenAddress, reason);
    }
  }
  
  /**
   * 完全移除Token - 从所有队列和订阅中移除
   */
  private async completelyRemoveToken(tokenAddress: string, reason: string): Promise<void> {
    console.log(`🗑️ 完全移除Token: ${tokenAddress.slice(0, 8)}... (${reason})`);
    
    // 从实例映射中移除
    const instance = this.tokenInstances.get(tokenAddress);
    if (instance) {
      instance.isActive = false;
      this.tokenInstances.delete(tokenAddress);
    }
    
    // 从订阅中移除
    this.currentTokenSubscriptions.delete(tokenAddress);
    
    // 从主监控队列中移除
    const queueIndex = this.tokenMonitoringQueue.findIndex(item => item.address === tokenAddress);
    if (queueIndex !== -1) {
      this.tokenMonitoringQueue.splice(queueIndex, 1);
    }
    
    // 从缓冲队列中移除
    this.sellOnlyBufferQueue.delete(tokenAddress);
    
    console.log(`✅ Token ${tokenAddress.slice(0, 8)}... 已完全移除`);
  }

  /**
   * 处理买入决策
   */
  private async processBuyDecision(tokenAddress: string, buyPrediction: number): Promise<void> {
    const instance = this.tokenInstances.get(tokenAddress);
    if (!instance || !instance.isActive) return;

    // 检查是否在缓冲队列中（只卖出不买入）
    if (this.sellOnlyBufferQueue.has(tokenAddress)) {
      console.log(`⚠️ Token ${tokenAddress.slice(0, 8)}... 在缓冲队列中，跳过买入信号`);
      return;
    }

    // 检查是否已有持仓
    if (instance.currentHolding && instance.currentHolding.amount > 0) {
      console.log(`⚠️ Token ${tokenAddress.slice(0, 8)}... 已有持仓，跳过买入信号`);
      return;
    }

    // 检查资金是否足够
    const stats = this.tradingBot.getStats();
    if (stats.currentBalance < this.config.trading.tradeAmountSol) {
      console.log(`⚠️ 资金不足，跳过买入 (余额: ${stats.currentBalance.toFixed(6)} SOL)`);
      return;
    }

    // 触发买入决策流程 - 让原有的executeTradeDecision处理
    console.log(`🎯 缓冲队列外token，允许买入考虑 ${tokenAddress.slice(0, 8)}...`);
  }

  /**
   * 管理缓冲队列 - 检查缓冲队列中token的持仓状态
   */
  private async manageSellOnlyBufferQueue(): Promise<void> {
    if (this.sellOnlyBufferQueue.size === 0) return;

    console.log(`🔍 检查缓冲队列状态 (${this.sellOnlyBufferQueue.size} 个待清仓token)`);
    
    // 🔥 缓冲队列详细状态监控
    console.log(`🔍 缓冲队列详细状态:`);
    for (const [tokenAddress, bufferInfo] of this.sellOnlyBufferQueue.entries()) {
      const instance = this.tokenInstances.get(tokenAddress);
      const inSubscription = this.currentTokenSubscriptions.has(tokenAddress);
      const lastPrediction = instance?.lastPrediction;
      const hasPosition = instance && instance.currentHolding && instance.currentHolding.amount > 0;
      
      console.log(`   Token: ${tokenAddress.slice(0, 8)}...`);
      console.log(`     在订阅中: ${inSubscription}`);
      console.log(`     实例活跃: ${instance?.isActive}`);
      console.log(`     最后预测: ${lastPrediction?.toLocaleTimeString() || "从未"}`);
      console.log(`     持仓: ${instance?.currentHolding?.amount || 0} tokens`);
      console.log(`     原因: ${bufferInfo.reason}`);
      console.log(`     缓冲时间: ${Math.floor((Date.now() - bufferInfo.addedTime.getTime()) / (1000 * 60))} 分钟`);
      
      // 🔥 如果token不在订阅中，重新添加
      if (!inSubscription && instance?.isActive) {
        console.log(`⚠️ 发现缓冲队列token不在订阅中，重新添加...`);
        this.currentTokenSubscriptions.add(tokenAddress);
      }
      
      if (!hasPosition) {
        // 无持仓了，可以完全移除
        console.log(`✅ Token ${tokenAddress.slice(0, 8)}... 持仓已清空，完全移除`);
        await this.completelyRemoveToken(tokenAddress, '缓冲队列-持仓已清空');
      } else {
        // 更新最后检查时间
        bufferInfo.lastCheckTime = new Date();
        console.log(`⏳ Token ${tokenAddress.slice(0, 8)}... 仍有持仓，继续等待卖出`);
      }
    }
  }

  /**
   * 获取缓冲队列状态信息
   */
  public getSellOnlyBufferInfo(): Array<{
    tokenAddress: string;
    reason: string;
    addedTime: Date;
    bufferDurationMinutes: number;
    hasPosition: boolean;
  }> {
    const result: Array<{
      tokenAddress: string;
      reason: string;
      addedTime: Date;
      bufferDurationMinutes: number;
      hasPosition: boolean;
    }> = [];
    
    for (const [tokenAddress, bufferInfo] of this.sellOnlyBufferQueue.entries()) {
      const instance = this.tokenInstances.get(tokenAddress);
      const hasPosition = instance && instance.currentHolding && instance.currentHolding.amount > 0;
      
      result.push({
        tokenAddress,
        reason: bufferInfo.reason,
        addedTime: bufferInfo.addedTime,
        bufferDurationMinutes: Math.floor((Date.now() - bufferInfo.addedTime.getTime()) / (1000 * 60)),
        hasPosition: !!hasPosition
      });
    }
    
    return result;
  }
}

// 主函数
async function main() {
  const paperTrading = new PaperTradingWithTracker();
  
  process.on('SIGINT', async () => {
    console.log('\n收到停止信号，正在关闭Paper Trading系统...');
    await paperTrading.stop();
    process.exit(0);
  });
  
  try {
    await paperTrading.start();
    
    // 保持运行
    await new Promise(() => {}); // 永远等待
    
  } catch (error) {
    console.error('❌ Paper Trading系统启动失败:', error);
    process.exit(1);
  }
}

// 运行系统
if (require.main === module) {
  main().catch(console.error);
}