import { BuyPredictor } from './predictors/buy-predictor';
import { SellPredictor } from './predictors/sell-predictor';
import { TransactionData } from './predictors/buy-predictor';

// 生成模拟交易数据
function generateMockTransactionData(count: number): TransactionData[] {
  const transactions: TransactionData[] = [];
  const startTime = new Date();
  
  for (let i = 0; i < count; i++) {
    const timestamp = new Date(startTime.getTime() + i * 1000 * 60); // 每分钟一个交易
    const action = Math.floor(Math.random() * 3); // 0: sell, 1: buy, 2: hold
    
    transactions.push({
      timestamp,
      action,
      sol_amount: Math.random() * 10 + 0.1, // 0.1-10.1 SOL
      usd_amount: Math.random() * 1000 + 10, // 10-1010 USD
      is_target_wallet: Math.random() > 0.8, // 20%概率为目标钱包
      wallet: `wallet_${i % 10}`, // 10个不同的钱包地址
      block_number: 100000 + i
    });
  }
  
  return transactions;
}

async function testPredictors() {
  console.log('🧪 Testing CatBoost Predictors with Timing');
  console.log('==========================================');
  
  // 创建预测器实例
  const buyPredictor = new BuyPredictor();
  const sellPredictor = new SellPredictor();
  
  console.log('\n📊 Model Status:');
  console.log('Buy Model:', buyPredictor.getModelInfo());
  console.log('Sell Model:', sellPredictor.getModelInfo());
  
  // 生成测试数据
  console.log('\n📈 Generating mock transaction data...');
  const transactions = generateMockTransactionData(100);
  console.log(`Generated ${transactions.length} mock transactions`);
  
  // 添加交易数据到预测器
  console.log('\n🔄 Adding transactions to predictors...');
  for (const transaction of transactions) {
    buyPredictor.addTransaction(transaction);
    sellPredictor.addTransaction(transaction);
  }
  
  console.log(`Buy predictor history length: ${buyPredictor.getHistoryLength()}`);
  console.log(`Sell predictor history length: ${sellPredictor.getHistoryLength()}`);
  
  // 测试买点预测
  console.log('\n🟢 Testing Buy Predictions:');
  console.log('==========================');
  
  const buyTimings: number[] = [];
  for (let i = 0; i < 10; i++) {
    const result = await buyPredictor.predictBuy();
    buyTimings.push(result.predictionTimeMicros);
    
    console.log(`Test ${i + 1}: probability=${result.probability.toFixed(4)}, ` +
               `prediction=${result.prediction}, confidence=${result.confidence.toFixed(3)}, ` +
               `time=${result.predictionTimeMicros.toFixed(2)}μs`);
  }
  
  // 测试卖点预测
  console.log('\n🔴 Testing Sell Predictions:');
  console.log('===========================');
  
  const sellTimings: number[] = [];
  for (let i = 0; i < 10; i++) {
    const result = await sellPredictor.predictSell();
    sellTimings.push(result.predictionTimeMicros);
    
    console.log(`Test ${i + 1}: probability=${result.probability.toFixed(4)}, ` +
               `prediction=${result.prediction}, confidence=${result.confidence.toFixed(3)}, ` +
               `time=${result.predictionTimeMicros.toFixed(2)}μs`);
  }
  
  // 预测时间统计
  console.log('\n⏱️ Timing Statistics:');
  console.log('====================');
  
  const buyAvgTime = buyTimings.reduce((a, b) => a + b, 0) / buyTimings.length;
  const buyMinTime = Math.min(...buyTimings);
  const buyMaxTime = Math.max(...buyTimings);
  
  const sellAvgTime = sellTimings.reduce((a, b) => a + b, 0) / sellTimings.length;
  const sellMinTime = Math.min(...sellTimings);
  const sellMaxTime = Math.max(...sellTimings);
  
  console.log(`Buy Predictions:`);
  console.log(`  Average time: ${buyAvgTime.toFixed(2)}μs (${(buyAvgTime/1000).toFixed(3)}ms)`);
  console.log(`  Min time: ${buyMinTime.toFixed(2)}μs`);
  console.log(`  Max time: ${buyMaxTime.toFixed(2)}μs`);
  
  console.log(`\nSell Predictions:`);
  console.log(`  Average time: ${sellAvgTime.toFixed(2)}μs (${(sellAvgTime/1000).toFixed(3)}ms)`);
  console.log(`  Min time: ${sellMinTime.toFixed(2)}μs`);
  console.log(`  Max time: ${sellMaxTime.toFixed(2)}μs`);
  
  // 测试批量预测（如果支持）
  if (buyPredictor.predictBuyBatch) {
    console.log('\n🚀 Testing Batch Predictions:');
    console.log('=============================');
    
    const batchTransactions = Array.from({length: 5}, () => transactions);
    const batchResults = await buyPredictor.predictBuyBatch(batchTransactions);
    
    console.log(`Batch prediction results: ${batchResults.length} predictions`);
    const batchAvgTime = batchResults.reduce((sum, r) => sum + r.predictionTimeMicros, 0) / batchResults.length;
    console.log(`Average batch prediction time: ${batchAvgTime.toFixed(2)}μs per prediction`);
  }
  
  // 测试特征提取
  console.log('\n🔬 Testing Feature Extraction:');
  console.log('==============================');
  
  const buyFeatures = buyPredictor.getLatestFeatures();
  const sellFeatures = sellPredictor.getLatestFeatures();
  
  if (buyFeatures) {
    console.log('Buy Features Sample:');
    console.log(`  sol_amount_mean: ${buyFeatures.sol_amount_mean.toFixed(4)}`);
    console.log(`  buy_count: ${buyFeatures.buy_count}`);
    console.log(`  sell_count: ${buyFeatures.sell_count}`);
    console.log(`  hour_last: ${buyFeatures.hour_last}`);
  }
  
  if (sellFeatures) {
    console.log('\nSell Features Sample (first 10):');
    const featureKeys = Object.keys(sellFeatures).slice(0, 10);
    featureKeys.forEach(key => {
      console.log(`  ${key}: ${(sellFeatures as any)[key]}`);
    });
    console.log(`  ... and ${Object.keys(sellFeatures).length - 10} more features`);
  }
  
  console.log('\n✅ Test completed successfully!');
}

// 运行测试
testPredictors().catch(error => {
  console.error('❌ Test failed:', error);
  process.exit(1);
}); 