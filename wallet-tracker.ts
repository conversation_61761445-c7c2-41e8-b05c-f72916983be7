import "dotenv/config";
import Client, {
  CommitmentLevel,
  SubscribeRequestAccountsDataSlice,
  SubscribeRequestFilterAccounts,
  SubscribeRequestFilterBlocks,
  SubscribeRequestFilterBlocksMeta,
  SubscribeRequestFilterEntry,
  SubscribeRequestFilterSlots,
  SubscribeRequestFilterTransactions,
} from "@triton-one/yellowstone-grpc";
import { PublicKey, VersionedTransactionResponse } from "@solana/web3.js";
import { Idl } from "@coral-xyz/anchor";
import { SolanaParser } from "@shyft-to/solana-transaction-parser";
import { SubscribeRequestPing } from "@triton-one/yellowstone-grpc/dist/types/grpc/geyser";
import { TransactionFormatter } from "./utils/transaction-formatter";
import { SolanaEventParser } from "./utils/event-parser";
import { bnLayoutFormatter } from "./utils/bn-layout-formatter";
import pumpFunAmmIdl from "./idls/pump_amm_0.1.0.json";
import { DynamicSubscriptionManager } from './dynamic-subscription-manager';
import { TradingBot } from './trading-bot';

interface SubscribeRequest {
  accounts: { [key: string]: SubscribeRequestFilterAccounts };
  slots: { [key: string]: SubscribeRequestFilterSlots };
  transactions: { [key: string]: SubscribeRequestFilterTransactions };
  transactionsStatus: { [key: string]: SubscribeRequestFilterTransactions };
  blocks: { [key: string]: SubscribeRequestFilterBlocks };
  blocksMeta: { [key: string]: SubscribeRequestFilterBlocksMeta };
  entry: { [key: string]: SubscribeRequestFilterEntry };
  commitment?: CommitmentLevel | undefined;
  accountsDataSlice: SubscribeRequestAccountsDataSlice[];
  ping?: SubscribeRequestPing | undefined;
}

const TXN_FORMATTER = new TransactionFormatter();
const PUMP_FUN_AMM_PROGRAM_ID = new PublicKey(
  "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA"
);
const PUMP_FUN_IX_PARSER = new SolanaParser([]);
PUMP_FUN_IX_PARSER.addParserFromIdl(
  PUMP_FUN_AMM_PROGRAM_ID.toBase58(),
  pumpFunAmmIdl as Idl
);
const PUMP_FUN_EVENT_PARSER = new SolanaEventParser([], console);
PUMP_FUN_EVENT_PARSER.addParserFromIdl(
  PUMP_FUN_AMM_PROGRAM_ID.toBase58(),
  pumpFunAmmIdl as Idl
);

// 从环境变量读取目标钱包地址
const TARGET_WALLET = process.env.TARGET_WALLET || '8J5GUAf7hr3LTPHJSkwrKFDNJPtAXtLHhnNHq6XxTLrW';
const WINDOW_HOURS = parseFloat(process.env.WINDOW_HOURS || '1');
const UPDATE_INTERVAL_MS = parseInt(process.env.UPDATE_INTERVAL_MS || '60000');

// 验证钱包地址格式
function validateWalletAddress(address: string): boolean {
  try {
    new PublicKey(address);
    return true;
  } catch {
    return false;
  }
}

if (!validateWalletAddress(TARGET_WALLET)) {
  console.error(`❌ 无效的钱包地址: ${TARGET_WALLET}`);
  process.exit(1);
}

console.log(`🎯 钱包追踪器配置:`);
console.log(`   目标钱包: ${TARGET_WALLET}`);
console.log(`   活动窗口: ${WINDOW_HOURS} 小时`);
console.log(`   更新间隔: ${UPDATE_INTERVAL_MS / 1000} 秒`);

// 初始化动态订阅管理器
const subscriptionManager = new DynamicSubscriptionManager(
  TARGET_WALLET,
  WINDOW_HOURS,
  UPDATE_INTERVAL_MS
);

// 初始化交易机器人（可选）
let tradingBot: TradingBot | undefined;
if (process.env.ENABLE_TRADING === 'true') {
  console.log('🤖 启用交易机器人集成');
  tradingBot = new TradingBot();
}

// 当前活跃的token订阅
let currentTokenSubscriptions: Set<string> = new Set();
let grpcClient: Client | null = null;
let currentStream: any = null;

async function handleStream(client: Client, args: SubscribeRequest) {
  grpcClient = client;
  const stream = await client.subscribe();
  currentStream = stream;

  // Create `error` / `end` handler
  const streamClosed = new Promise<void>((resolve, reject) => {
    stream.on("error", (error) => {
      console.log("GRPC Stream ERROR", error);
      reject(error);
      stream.end();
    });
    stream.on("end", () => {
      resolve();
    });
    stream.on("close", () => {
      resolve();
    });
  });

  let lastReceivedTxTime = 0;
  
  // 定期状态显示
  setInterval(() => {
    const now = Date.now();
    const secondsSinceLastTx = (now - lastReceivedTxTime) / 1000;
    
    if (lastReceivedTxTime > 0) {
      console.log(`🔄 监听钱包 ${TARGET_WALLET.slice(0, 8)}... 上次交易 ${secondsSinceLastTx.toFixed(0)} 秒前`);
    } else {
      console.log(`🔄 等待钱包 ${TARGET_WALLET.slice(0, 8)}... 的交易活动`);
    }
    
    // 显示当前活跃token
    const activeTokens = subscriptionManager.getActiveTokens();
    if (activeTokens.length > 0) {
      console.log(`   当前跟踪 ${activeTokens.length} 个token`);
    }
  }, 30000);

  // Handle updates
  stream.on("data", async (data) => {
    if (data?.transaction) {
      lastReceivedTxTime = Date.now();
      
      const txn = TXN_FORMATTER.formTransactionFromJson(
        data.transaction,
        Date.now()
      );

      const parsedTxn = decodePumpFunTxn(txn);
      if (!parsedTxn) return;

      // 检查交易是否涉及目标钱包
      const isTargetWalletInvolved = await isWalletInvolvedInTransaction(txn, TARGET_WALLET);
      if (!isTargetWalletInvolved) return;

      const signature = txn.transaction.signatures[0] || 'unknown';
      const slot = data.transaction?.slot || 'unknown';
      const timestamp = new Date();

      console.log(`\n🎯 发现目标钱包交易:`);
      console.log(`   钱包: ${TARGET_WALLET.slice(0, 8)}...`);
      console.log(`   交易ID: ${signature}`);
      console.log(`   时间: ${timestamp.toLocaleString()}`);
      console.log(`   Slot: ${slot}`);

      // 提取token信息
      const tokenInfo = extractTokenInfoFromTransaction(parsedTxn);
      if (tokenInfo) {
        for (const token of tokenInfo.tokens) {
          console.log(`   🪙 Token: ${token.address.slice(0, 8)}... (${token.action})`);
          
          // 记录钱包交易
          await subscriptionManager.processWalletTransaction(
            signature,
            timestamp,
            typeof slot === 'number' ? slot : 0,
            token.address,
            token.action,
            token.solAmount || 0,
            token.usdAmount || 0
          );
        }

        // 更新token订阅
        await updateTokenSubscriptions();
      }

      console.log(`   链接: https://translator.shyft.to/tx/${signature}`);
      console.log('-'.repeat(80));
    }
  });

  // Send subscribe request
  await new Promise<void>((resolve, reject) => {
    stream.write(args, (err: any) => {
      if (err === null || err === undefined) {
        resolve();
      } else {
        reject(err);
      }
    });
  }).catch((reason) => {
    console.error("Failed to send subscribe request:", reason);
    throw reason;
  });

  await streamClosed;
}

// 检查钱包是否参与了交易
async function isWalletInvolvedInTransaction(txn: VersionedTransactionResponse, walletAddress: string): Promise<boolean> {
  try {
    // 检查交易账户列表
    const accounts = txn.transaction.message.staticAccountKeys || [];
    const accountKeys = accounts.map(key => key.toBase58());
    
    // 检查主要账户
    if (accountKeys.includes(walletAddress)) {
      return true;
    }

    // 检查加载的账户（如果有）
    if (txn.meta?.loadedAddresses) {
      const readonlyAccounts = txn.meta.loadedAddresses.readonly || [];
      const writableAccounts = txn.meta.loadedAddresses.writable || [];
      
      for (const account of [...readonlyAccounts, ...writableAccounts]) {
        if (account.toBase58() === walletAddress) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    console.error('检查钱包参与度时出错:', error);
    return false;
  }
}

// 从交易中提取token信息
function extractTokenInfoFromTransaction(parsedTxn: any): { tokens: Array<{ address: string, action: 'buy' | 'sell' | 'unknown', solAmount?: number, usdAmount?: number }> } | null {
  const tokens: Array<{ address: string, action: 'buy' | 'sell' | 'unknown', solAmount?: number, usdAmount?: number }> = [];

  try {
    // 从事件中提取token信息
    if (parsedTxn.events && Array.isArray(parsedTxn.events)) {
      for (const event of parsedTxn.events) {
        if (event.data) {
          // 提取base_mint（主要的token）
          if (event.data.base_mint) {
            let action: 'buy' | 'sell' | 'unknown' = 'unknown';
            let solAmount = 0;
            let usdAmount = 0;

            // 根据交易量判断买卖方向
            if (event.data.base_amount_in && event.data.quote_amount_out) {
              action = 'sell'; // 卖出base token，获得quote token
              solAmount = parseFloat(event.data.quote_amount_out) / 1e9; // 假设quote是SOL
            } else if (event.data.quote_amount_in && event.data.base_amount_out) {
              action = 'buy'; // 用quote token买入base token
              solAmount = parseFloat(event.data.quote_amount_in) / 1e9;
            }

            tokens.push({
              address: event.data.base_mint,
              action,
              solAmount,
              usdAmount
            });
          }
        }
      }
    }

    // 从指令中提取token信息作为备用
    if (tokens.length === 0 && parsedTxn.instructions) {
      for (const ix of parsedTxn.instructions) {
        if (ix.accounts) {
          const baseMintAccounts = ix.accounts.filter((a: any) => a.name === 'base_mint');
          for (const account of baseMintAccounts) {
            tokens.push({
              address: account.pubkey,
              action: 'unknown'
            });
          }
        }
      }
    }

    return tokens.length > 0 ? { tokens } : null;
  } catch (error) {
    console.error('提取token信息时出错:', error);
    return null;
  }
}

// 更新token订阅
async function updateTokenSubscriptions(): Promise<void> {
  const activeTokens = subscriptionManager.getActiveTokens();
  const newTokenSet = new Set(activeTokens);

  // 检查是否有变化
  const tokensToAdd = activeTokens.filter(token => !currentTokenSubscriptions.has(token));
  const tokensToRemove = Array.from(currentTokenSubscriptions).filter(token => !newTokenSet.has(token));

  if (tokensToAdd.length === 0 && tokensToRemove.length === 0) {
    return; // 没有变化
  }

  console.log(`🔄 更新token订阅:`);
  if (tokensToAdd.length > 0) {
    console.log(`   ➕ 新增: ${tokensToAdd.map(t => t.slice(0, 8) + '...').join(', ')}`);
  }
  if (tokensToRemove.length > 0) {
    console.log(`   ➖ 移除: ${tokensToRemove.map(t => t.slice(0, 8) + '...').join(', ')}`);
  }

  // 更新当前订阅
  currentTokenSubscriptions = newTokenSet;

  // 如果有GRPC流，更新订阅
  if (currentStream && grpcClient) {
    try {
      // 创建新的订阅请求，包含当前活跃的token
      const newReq: SubscribeRequest = {
        accounts: {},
        slots: {},
        transactions: {
          pumpFun: {
            vote: false,
            failed: false,
            signature: undefined,
            accountInclude: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()],
            accountExclude: [],
            // 包含目标钱包和所有活跃token
            accountRequired: [TARGET_WALLET, ...activeTokens],
          },
        },
        transactionsStatus: {},
        entry: {},
        blocks: {},
        blocksMeta: {},
        accountsDataSlice: [],
        ping: undefined,
        commitment: CommitmentLevel.CONFIRMED,
      };

      // 使用stream.write更新订阅
      await new Promise<void>((resolve, reject) => {
        currentStream.write(newReq, (err: any) => {
          if (err === null || err === undefined) {
            resolve();
          } else {
            reject(err);
          }
        });
      });

      console.log(`✅ GRPC订阅已更新 (${activeTokens.length} 个token)`);
    } catch (error) {
      console.error('❌ 更新GRPC订阅失败:', error);
    }
  }

  // 如果有交易机器人，也更新其订阅
  if (tradingBot) {
    // 添加新token
    for (const token of tokensToAdd) {
      tradingBot.subscribeToken(token);
    }
    
    // 移除旧token
    for (const token of tokensToRemove) {
      tradingBot.unsubscribeToken(token);
    }
  }
}

async function subscribeCommand(client: Client, args: SubscribeRequest) {
  while (true) {
    try {
      await handleStream(client, args);
    } catch (error) {
      console.error("Stream error, restarting in 5 seconds...", error);
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }
  }
}

// 静默控制台，用于避免解析器警告
const silentConsole = {
  log: () => {},
  error: () => {},
  warn: () => {},
  info: () => {},
  debug: () => {}
};

function decodePumpFunTxn(tx: VersionedTransactionResponse) {
  if (tx.meta?.err) return;

  const originalConsole = console;
  // @ts-ignore
  global.console = silentConsole;
  
  try {
    const paredIxs = PUMP_FUN_IX_PARSER.parseTransactionData(
      tx.transaction.message,
      tx.meta.loadedAddresses
    );
    
    const filteredIxs = paredIxs.filter(ix => 
      !ix.programId.toString().includes('ComputeBudget')
    );

    const pumpFunIxs = filteredIxs.filter((ix) =>
      ix.programId.equals(PUMP_FUN_AMM_PROGRAM_ID)
    );

    if (pumpFunIxs.length === 0) return;
    const events = PUMP_FUN_EVENT_PARSER.parseEvent(tx);
    const result = { instructions: pumpFunIxs, events };
    bnLayoutFormatter(result);
    return result;
  } finally {
    // @ts-ignore
    global.console = originalConsole;
  }
}

// 主程序
async function main() {
  console.log('🚀 启动钱包追踪器...\n');
  
  // 启动动态订阅管理器
  subscriptionManager.start(tradingBot);

  // 启动交易机器人（如果启用）
  if (tradingBot) {
    await tradingBot.start();
  }

  const client = new Client(
    process.env.ENDPOINT!,
    process.env.X_TOKEN,
    undefined
  );

  // 初始订阅请求：只监控目标钱包
  const req: SubscribeRequest = {
    accounts: {},
    slots: {},
    transactions: {
      pumpFun: {
        vote: false,
        failed: false,
        signature: undefined,
        accountInclude: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()],
        accountExclude: [],
        accountRequired: [TARGET_WALLET], // 只监控目标钱包
      },
    },
    transactionsStatus: {},
    entry: {},
    blocks: {},
    blocksMeta: {},
    accountsDataSlice: [],
    ping: undefined,
    commitment: CommitmentLevel.CONFIRMED,
  };

  console.log('📡 开始监控钱包交易活动...\n');
  
  // 优雅关闭处理
  process.on('SIGINT', async () => {
    console.log('\n🛑 收到停止信号，正在关闭...');
    subscriptionManager.stop();
    if (tradingBot) {
      await tradingBot.stop();
    }
    process.exit(0);
  });

  await subscribeCommand(client, req);
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ 钱包追踪器启动失败:', error);
    process.exit(1);
  });
} 