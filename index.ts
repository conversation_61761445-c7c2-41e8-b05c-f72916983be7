import "dotenv/config";
import Client, {
  CommitmentLevel,
  SubscribeRequestAccountsDataSlice,
  SubscribeRequestFilterAccounts,
  SubscribeRequestFilterBlocks,
  SubscribeRequestFilterBlocksMeta,
  SubscribeRequestFilterEntry,
  SubscribeRequestFilterSlots,
  SubscribeRequestFilterTransactions,
} from "@triton-one/yellowstone-grpc";
import { PublicKey, VersionedTransactionResponse } from "@solana/web3.js";
import { Idl } from "@coral-xyz/anchor";
import { SolanaParser } from "@shyft-to/solana-transaction-parser";
import { SubscribeRequestPing } from "@triton-one/yellowstone-grpc/dist/types/grpc/geyser";
import { TransactionFormatter } from "./utils/transaction-formatter";
import { SolanaEventParser } from "./utils/event-parser";
import { bnLayoutFormatter } from "./utils/bn-layout-formatter";
import pumpFunAmmIdl from "./idls/pump_amm_0.1.0.json";


interface SubscribeRequest {
  accounts: { [key: string]: SubscribeRequestFilterAccounts };
  slots: { [key: string]: SubscribeRequestFilterSlots };
  transactions: { [key: string]: SubscribeRequestFilterTransactions };
  transactionsStatus: { [key: string]: SubscribeRequestFilterTransactions };
  blocks: { [key: string]: SubscribeRequestFilterBlocks };
  blocksMeta: { [key: string]: SubscribeRequestFilterBlocksMeta };
  entry: { [key: string]: SubscribeRequestFilterEntry };
  commitment?: CommitmentLevel | undefined;
  accountsDataSlice: SubscribeRequestAccountsDataSlice[];
  ping?: SubscribeRequestPing | undefined;
}

const TXN_FORMATTER = new TransactionFormatter();
const PUMP_FUN_AMM_PROGRAM_ID = new PublicKey(
  "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA"
);
const PUMP_FUN_IX_PARSER = new SolanaParser([]);
PUMP_FUN_IX_PARSER.addParserFromIdl(
  PUMP_FUN_AMM_PROGRAM_ID.toBase58(),
  pumpFunAmmIdl as Idl
);
const PUMP_FUN_EVENT_PARSER = new SolanaEventParser([], console);
PUMP_FUN_EVENT_PARSER.addParserFromIdl(
  PUMP_FUN_AMM_PROGRAM_ID.toBase58(),
  pumpFunAmmIdl as Idl
);

// 从环境变量读取要监控的token地址，多个地址用逗号分隔
// 使用 TARGET_TOKEN_ADDRESSES 或者兼容旧的 TARGET_BASE_MINT_ADDRESSES
const TARGET_TOKEN_ADDRESSES = process.env.TARGET_TOKEN_ADDRESSES
  ? process.env.TARGET_TOKEN_ADDRESSES.split(',')
  : (process.env.TARGET_BASE_MINT_ADDRESSES 
     ? process.env.TARGET_BASE_MINT_ADDRESSES.split(',') 
     : []);

// 汉化TARGET_TOKEN_ADDRESSES，去除空值、空格等
const FILTERED_TOKEN_ADDRESSES = TARGET_TOKEN_ADDRESSES
  .map(addr => addr.trim())
  .filter(addr => addr.length > 0);

// 查看是否强制使用所有地址，无论格式
const FORCE_USE_ALL_ADDRESSES = process.env.FORCE_USE_ALL_ADDRESSES === 'true';

// 验证地址格式是否正确
const VERIFIED_TOKEN_ADDRESSES = FORCE_USE_ALL_ADDRESSES 
  ? FILTERED_TOKEN_ADDRESSES // 如果强制使用，则使用所有地址
  : FILTERED_TOKEN_ADDRESSES.filter(addr => {
    // 简单验证，仅允许base58字符集(1-9, A-H, J-N, P-Z, a-k, m-z)
    const isValidFormat = /^[1-9A-HJ-NP-Za-km-z]+$/.test(addr);
    if (!isValidFormat) {
      console.warn(`警告: 地址"${addr}"不是有效的base58格式`);
      console.warn(`如果你确定这是正确的地址，可以在.env文件中设置FORCE_USE_ALL_ADDRESSES=true强制使用它`);
    }
    return isValidFormat; 
  });
  
// 显示目标token地址，方便调试
console.log(`原始输入的token地址: ${FILTERED_TOKEN_ADDRESSES.length > 0 ? FILTERED_TOKEN_ADDRESSES.join(', ') : '无过滤，监控所有地址'}`);
console.log(`强制使用所有地址: ${FORCE_USE_ALL_ADDRESSES ? '是' : '否'}`);
console.log(`实际使用的token地址: ${VERIFIED_TOKEN_ADDRESSES.length > 0 ? VERIFIED_TOKEN_ADDRESSES.join(', ') : '无有效地址，将监控所有交易'}`);

async function handleStream(client: Client, args: SubscribeRequest) {
  // Subscribe for events
  const stream = await client.subscribe();

  // Create `error` / `end` handler
  const streamClosed = new Promise<void>((resolve, reject) => {
    stream.on("error", (error) => {
      console.log("ERROR", error);
      reject(error);
      stream.end();
    });
    stream.on("end", () => {
      resolve();
    });
    stream.on("close", () => {
      resolve();
    });
  });

  // 保存最近接收到的交易的时间戳
  let lastReceivedTxTime = 0;
  // 定期显示状态信息
  setInterval(() => {
    const now = Date.now();
    const secondsSinceLastTx = (now - lastReceivedTxTime) / 1000;
    if (lastReceivedTxTime > 0) {
      if (VERIFIED_TOKEN_ADDRESSES.length > 0) {
        console.log(`🔄 监听中... 上次收到匹配交易在 ${secondsSinceLastTx.toFixed(0)} 秒前 (GRPC服务器端过滤)`);
      } else {
        console.log(`🔄 监听中... 上次收到交易在 ${secondsSinceLastTx.toFixed(0)} 秒前 (监听所有Pump Fun交易)`);
      }
    } else {
      if (VERIFIED_TOKEN_ADDRESSES.length > 0) {
        console.log(`🔄 监听中... 等待包含目标地址的交易 (GRPC服务器端过滤)`);
        console.log(`   监控地址: ${VERIFIED_TOKEN_ADDRESSES.slice(0, 2).join(', ')}${VERIFIED_TOKEN_ADDRESSES.length > 2 ? '...' : ''}`);
      } else {
        console.log(`🔄 监听中... 等待Pump Fun交易 (无地址过滤)`);
      }
    }
  }, 30000); // 每30秒显示一次

  // Handle updates
  stream.on("data", (data) => {
    if (data?.transaction) {
      // 更新最近收到交易的时间
      lastReceivedTxTime = Date.now();
      
      const txn = TXN_FORMATTER.formTransactionFromJson(
        data.transaction,
        Date.now()
      );

      const parsedTxn = decodePumpFunTxn(txn);

      // 如果解析失败，直接返回，不显示任何信息
      if (!parsedTxn) {
        return;
      }
      
      // 获取交易签名和区块信息
      const signature = txn.transaction.signatures[0] || 'unknown';
      // 从原始交易数据中获取slot信息
      const slot = data.transaction?.slot || data.transaction?.meta?.slot || 'unknown';
      
      // 🎯 GRPC服务器已经为我们过滤了包含目标token地址的交易
      // 所以这里收到的交易都是相关的，无需再次过滤
      
      // 可选：显示找到的token地址（用于调试）
      if (VERIFIED_TOKEN_ADDRESSES.length > 0) {
        const tokenInfo = findAllTokensInTransaction(parsedTxn, false);
        if (tokenInfo && tokenInfo.allTokens.size > 0) {
          const foundTargets = VERIFIED_TOKEN_ADDRESSES.filter(addr => 
            tokenInfo.allTokens.has(addr)
          );
          if (foundTargets.length > 0) {
            console.log(`🎯 匹配的目标token: ${foundTargets.join(', ')}`);
          }
        }
      }

      // 简化输出，只展示关键信息
      console.log(
        "\n" + "-".repeat(100) + "\n",
        `时间: ${new Date().toLocaleString()}\n`,
        `交易ID: ${signature}\n`,
        `区块高度(Slot): ${slot}\n`,
        `交易链接: https://translator.shyft.to/tx/${signature}\n`
      );
      
      // 提取并输出指令信息
      if (parsedTxn.instructions && parsedTxn.instructions.length > 0) {
        console.log("指令详情:");
        parsedTxn.instructions.forEach((ix: any, index: number) => {
          console.log(`  [${index}] ${ix.name || '未知指令'}`);
          if (ix.args) {
            console.log("  参数:", JSON.stringify(ix.args, null, 2));
          }
        });
      }
      
      // 提取并输出事件信息
      if (parsedTxn.events && parsedTxn.events.length > 0) {
        console.log("\n事件详情:");
        parsedTxn.events.forEach((event: any, index: number) => {
          console.log(`  [${index}] ${event.name || '未知事件'}`);
          if (event.data) {
                        // 输出关键字段，包含所有相关token信息
            const keyData = {
              base_mint: event.data.base_mint || '未知',
              quote_mint: event.data.quote_mint || '未知',
              base_amount_in: event.data.base_amount_in,
              quote_amount_out: event.data.quote_amount_out,
              pool: event.data.pool,
              user: event.data.user
            };
            console.log("  数据:", JSON.stringify(keyData, null, 2));
          }
        });
      }
      
      console.log("-".repeat(100));
    }
  });

  // Send subscribe request
  await new Promise<void>((resolve, reject) => {
    stream.write(args, (err: any) => {
      if (err === null || err === undefined) {
        resolve();
      } else {
        reject(err);
      }
    });
  }).catch((reason) => {
    console.error(reason);
    throw reason;
  });

  await streamClosed;
}

async function subscribeCommand(client: Client, args: SubscribeRequest) {
  while (true) {
    try {
      await handleStream(client, args);
    } catch (error) {
      console.error("Stream error, restarting in 1 second...", error);
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  }
}

const client = new Client(
  process.env.ENDPOINT!,
  process.env.X_TOKEN,
  undefined
);

// 创建订阅请求
// 修改为在GRPC层面直接过滤token地址，减少客户端负担
const req: SubscribeRequest = {
  accounts: {},
  slots: {},
  transactions: {
    pumpFun: {
      vote: false,
      failed: false,
      signature: undefined,
      // 包含Pump Fun AMM程序
      accountInclude: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()],
      accountExclude: [],
      // 🎯 在GRPC层面过滤：只发送包含监控token地址的交易
      accountRequired: VERIFIED_TOKEN_ADDRESSES.length > 0 ? VERIFIED_TOKEN_ADDRESSES : [],
    },
  },
  transactionsStatus: {},
  entry: {},
  blocks: {},
  blocksMeta: {},
  accountsDataSlice: [],
  ping: undefined,
  commitment: CommitmentLevel.CONFIRMED,
};

// 显示当前过滤配置
if (VERIFIED_TOKEN_ADDRESSES.length > 0) {
  console.log(`🎯 GRPC服务器端过滤已启用`);
  console.log(`监听 Pump Fun AMM 程序交易，且必须包含以下地址之一:`);
  VERIFIED_TOKEN_ADDRESSES.forEach((addr, index) => {
    console.log(`  ${index + 1}. ${addr}`);
  });
  console.log(`这样可以大大减少网络传输和客户端处理负担！\n`);
} else {
  console.log(`⚠️  未设置监控地址，将监听所有 Pump Fun AMM 交易`);
  console.log(`建议在 TARGET_TOKEN_ADDRESSES 环境变量中设置要监控的地址\n`);
}

subscribeCommand(client, req);

// 隐藏ComputeBudget程序的原始控制台输出
// 创建一个静默的控制台，用于解析器
// 这样可以避免"Parser does not matching the instruction args"警告
const silentConsole = {
  log: () => {},
  error: () => {},
  warn: () => {},
  info: () => {},
  debug: () => {}
};

function decodePumpFunTxn(tx: VersionedTransactionResponse) {
  if (tx.meta?.err) return;

  // 使用静默控制台替换原始控制台，抓取警告
  const originalConsole = console;
  // @ts-ignore - 前端环境不支持global，这里假设我们在Node环境
  global.console = silentConsole;
  
  try {
    const paredIxs = PUMP_FUN_IX_PARSER.parseTransactionData(
      tx.transaction.message,
      tx.meta.loadedAddresses
    );
    
    // 过滤掉ComputeBudget程序的指令
    const filteredIxs = paredIxs.filter(ix => 
      // 只保留非ComputeBudget的指令
      !ix.programId.toString().includes('ComputeBudget')
    );

    const pumpFunIxs = filteredIxs.filter((ix) =>
      ix.programId.equals(PUMP_FUN_AMM_PROGRAM_ID)
    );

    if (pumpFunIxs.length === 0) return;
    const events = PUMP_FUN_EVENT_PARSER.parseEvent(tx);
    const result = { instructions: pumpFunIxs, events };
    bnLayoutFormatter(result);
    return result;
  } finally {
    // 恢复原始控制台
    // @ts-ignore
    global.console = originalConsole;
  }
}

// 定义token信息接口
interface TokenInfo {
  baseMints: Set<string>;  // base token地址
  quoteMints: Set<string>; // quote token地址
  allTokens: Set<string>;  // 所有token地址
}

// 显示交易中的token地址
function findAllTokensInTransaction(parsedTxn: any, showDebugLogs: boolean = true): TokenInfo | null {
  try {    
    const result: TokenInfo = {
      baseMints: new Set<string>(),
      quoteMints: new Set<string>(),
      allTokens: new Set<string>()
    };
    
    // 先将监控的地址加入应该检查的集合
    const addressesToCheck = new Set(VERIFIED_TOKEN_ADDRESSES);
    
    // 从指令中提取token地址
    if (parsedTxn.instructions && parsedTxn.instructions.length > 0) {
      if (showDebugLogs) console.log(`  指令数量: ${parsedTxn.instructions.length}`);
      
      for (const ix of parsedTxn.instructions) {
        if (showDebugLogs) console.log(`  指令名称: ${ix.name || '未知'}`);
        
        if (ix.accounts) {
          // 查找base_mint
          const baseMintAccounts = ix.accounts.filter((a: any) => a.name === 'base_mint');
          for (const account of baseMintAccounts) {
            if (showDebugLogs) console.log(`  base_mint地址: ${account.pubkey}`);
            result.baseMints.add(account.pubkey);
            result.allTokens.add(account.pubkey);
          }
          
          // 查找quote_mint
          const quoteMintAccounts = ix.accounts.filter((a: any) => a.name === 'quote_mint');
          for (const account of quoteMintAccounts) {
            if (showDebugLogs) console.log(`  quote_mint地址: ${account.pubkey}`);
            result.quoteMints.add(account.pubkey);
            result.allTokens.add(account.pubkey);
          }
          
          // 查找其他可能的token相关账户
          const tokenAccounts = ix.accounts.filter((a: any) => 
            a.name && (
              a.name.includes('token') || 
              a.name.includes('mint') || 
              a.name.includes('_spl')
            ));
          
          for (const account of tokenAccounts) {
            if (account.name !== 'base_mint' && account.name !== 'quote_mint') {
              if (showDebugLogs) console.log(`  其他token相关账户: ${account.name} = ${account.pubkey}`);
              result.allTokens.add(account.pubkey);
            }
          }
        }
      }
    }
    
    // 从事件中提取token地址
    if (parsedTxn.events && Array.isArray(parsedTxn.events)) {
      if (showDebugLogs) console.log(`  事件数量: ${parsedTxn.events.length}`);
      
      for (const event of parsedTxn.events) {
        if (showDebugLogs) console.log(`  事件名称: ${event.name || '未知'}`);
        
        if (event.data) {
          // 提取base_mint
          if (event.data.base_mint) {
            if (showDebugLogs) console.log(`  事件base_mint地址: ${event.data.base_mint}`);
            result.baseMints.add(event.data.base_mint);
            result.allTokens.add(event.data.base_mint);
          }
          
          // 提取quote_mint
          if (event.data.quote_mint) {
            if (showDebugLogs) console.log(`  事件quote_mint地址: ${event.data.quote_mint}`);
            result.quoteMints.add(event.data.quote_mint);
            result.allTokens.add(event.data.quote_mint);
          }
          
          // 检查事件中的其他可能的token地址
          Object.entries(event.data).forEach(([key, value]) => {
            if (typeof key === 'string' && 
                typeof value === 'string' && 
                (key.includes('mint') || key.includes('token')) && 
                key !== 'base_mint' && 
                key !== 'quote_mint') {
              if (showDebugLogs) console.log(`  事件其他token地址: ${key} = ${value}`);
              result.allTokens.add(value as string);
            }
          });
        }
      }
    }
    
    return result;
  } catch (error) {
    console.error('Error finding token addresses:', error);
    return null;
  }
}


