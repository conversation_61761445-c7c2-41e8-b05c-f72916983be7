"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_CONFIG = void 0;
exports.loadConfig = loadConfig;
exports.validateConfig = validateConfig;
exports.showConfigSummary = showConfigSummary;
// 默认配置
exports.DEFAULT_CONFIG = {
    wallet: {
        privateKey: process.env.PRIVATE_KEY || '',
        rpcEndpoint: process.env.SOLANA_RPC_ENDPOINT || 'https://api.mainnet-beta.solana.com'
    },
    trading: {
        tradeAmountSol: 0.1, // 每次交易0.1 SOL
        initialCapitalSol: 3.0, // 初始资金3 SOL
        maxPositions: 5, // 最多同时持有5个仓位
        paperTrading: false, // 默认真实交易
        slippageTolerance: 0.05 // 5%滑点容忍度
    },
    risk: {
        // 止损止盈
        stopLossPercentage: 0.1, // 10%止损
        takeProfitPercentage: 0.2, // 20%止盈
        maxLossPerTradeSol: 0.1, // 单笔最大亏损0.1 SOL
        // 累计风控
        maxDailyLossSol: 0.5, // 日内最大亏损0.5 SOL
        maxTotalLossSol: 1.0, // 🔴 累计最大亏损1.0 SOL（达到后停止交易）
        maxDrawdownPercentage: 0.3, // 30%最大回撤
        // 其他风控
        maxPositionSizePercentage: 0.2, // 单仓位最大20%资金
        cooldownPeriodMinutes: 5, // 连续亏损后5分钟冷却
        consecutiveLossLimit: 3 // 连续亏损3次后冷却
    },
    models: {
        buyModelPath: './catboost_focal_blocktol_2025.cbm',
        sellModelPath: './best_catboost_sell_model.cbm',
        buyThreshold: 0.5, // 50%概率买入
        sellThreshold: 0.7, // 70%概率卖出
        buyWindowSize: 10,
        sellWindowSize: 30
    },
    monitoring: {
        targetTokenAddresses: process.env.TARGET_TOKEN_ADDRESSES?.split(',').map(addr => addr.trim()) || [],
        logLevel: 'info',
        enableDetailedLogs: false,
        statsUpdateIntervalSeconds: 30
    },
    grpc: {
        endpoint: process.env.GRPC_ENDPOINT || 'https://grpc.yellowstone-mainnet.solana.com',
        token: process.env.GRPC_TOKEN || ''
    }
};
/**
 * 从环境变量和配置文件加载配置
 */
function loadConfig(overrides = {}) {
    const config = JSON.parse(JSON.stringify(exports.DEFAULT_CONFIG)); // 深拷贝
    // 应用用户覆盖配置
    return mergeConfig(config, overrides);
}
/**
 * 递归合并配置对象
 */
function mergeConfig(target, source) {
    for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
            if (!target[key])
                target[key] = {};
            mergeConfig(target[key], source[key]);
        }
        else {
            target[key] = source[key];
        }
    }
    return target;
}
/**
 * 验证配置是否有效
 */
function validateConfig(config) {
    const errors = [];
    // 验证钱包配置
    if (!config.wallet.privateKey && !config.trading.paperTrading) {
        errors.push('真实交易模式下必须设置钱包私钥 (PRIVATE_KEY)');
    }
    if (!config.wallet.rpcEndpoint) {
        errors.push('必须设置Solana RPC端点 (SOLANA_RPC_ENDPOINT)');
    }
    // 验证交易金额
    if (config.trading.tradeAmountSol <= 0) {
        errors.push('交易金额必须大于0');
    }
    if (config.trading.initialCapitalSol <= 0) {
        errors.push('初始资金必须大于0');
    }
    if (config.trading.tradeAmountSol > config.trading.initialCapitalSol) {
        errors.push('单次交易金额不能超过初始资金');
    }
    // 验证风险参数
    if (config.risk.maxTotalLossSol <= 0) {
        errors.push('累计最大亏损必须大于0');
    }
    if (config.risk.maxTotalLossSol > config.trading.initialCapitalSol) {
        errors.push('累计最大亏损不能超过初始资金');
    }
    // 验证模型文件
    if (!config.models.buyModelPath || !config.models.sellModelPath) {
        errors.push('必须设置买卖预测模型路径');
    }
    // 验证监控配置
    if (config.monitoring.targetTokenAddresses.length === 0) {
        errors.push('必须设置至少一个监控Token地址');
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
/**
 * 显示配置摘要
 */
function showConfigSummary(config) {
    console.log('\n🎯 交易系统配置摘要:');
    console.log('='.repeat(60));
    console.log('\n💰 交易配置:');
    console.log(`  每次交易金额: ${config.trading.tradeAmountSol} SOL`);
    console.log(`  初始资金: ${config.trading.initialCapitalSol} SOL`);
    console.log(`  最大仓位数: ${config.trading.maxPositions}`);
    console.log(`  交易模式: ${config.trading.paperTrading ? '📝 纸上交易' : '💸 真实交易'}`);
    console.log('\n🛡️ 风险管理:');
    console.log(`  单笔止损: ${config.risk.stopLossPercentage * 100}%`);
    console.log(`  单笔止盈: ${config.risk.takeProfitPercentage * 100}%`);
    console.log(`  日内最大亏损: ${config.risk.maxDailyLossSol} SOL`);
    console.log(`  🔴 累计最大亏损: ${config.risk.maxTotalLossSol} SOL (达到后停止交易)`);
    console.log(`  最大回撤: ${config.risk.maxDrawdownPercentage * 100}%`);
    console.log('\n🤖 AI模型:');
    console.log(`  买入阈值: ${config.models.buyThreshold * 100}%`);
    console.log(`  卖出阈值: ${config.models.sellThreshold * 100}%`);
    console.log('\n📊 监控:');
    console.log(`  监控Token数量: ${config.monitoring.targetTokenAddresses.length}`);
    console.log(`  Token地址: ${config.monitoring.targetTokenAddresses.slice(0, 3).join(', ')}${config.monitoring.targetTokenAddresses.length > 3 ? '...' : ''}`);
    console.log('\n');
}
