"use strict";
//=============================================================================
// 🔥 风险管理系统 - 独立模块
//=============================================================================
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigValidator = exports.PositionManager = exports.SimpleRiskManager = void 0;
// 🔥 增强版风险管理器 - 支持连续买入失败控制
class SimpleRiskManager {
    constructor(config) {
        this.tradeHistory = [];
        this.config = config;
        this.dailyStartBalance = config.initialCapitalSol;
        this.highWaterMark = this.dailyStartBalance;
        this.stats = {
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            winRate: 0,
            totalPnL: 0,
            totalFees: 0,
            averagePnL: 0,
            maxDrawdown: 0,
            currentBalance: config.initialCapitalSol,
            activePositions: 0,
            consecutiveLosses: 0,
            consecutiveBuyFailures: 0,
            dailyPnL: 0,
            tradingPaused: false,
            pauseReason: ''
        };
    }
    // 🔥 检查是否可以买入 - 新增买入失败限制
    canBuy() {
        // 检查基础交易条件
        const basicCheck = this.canTrade();
        if (!basicCheck.allowed) {
            return basicCheck;
        }
        // 🔥 检查连续买入失败限制
        if (this.config.consecutiveBuyFailureLimit > 0 &&
            this.stats.consecutiveBuyFailures >= this.config.consecutiveBuyFailureLimit) {
            this.pauseTrading(`连续买入失败${this.stats.consecutiveBuyFailures}次，达到限制${this.config.consecutiveBuyFailureLimit}次`);
            return { allowed: false, reason: this.stats.pauseReason };
        }
        return { allowed: true, reason: '买入风险检查通过' };
    }
    // 检查是否可以交易（通用）
    canTrade() {
        // 检查是否已暂停
        if (this.stats.tradingPaused) {
            return { allowed: false, reason: this.stats.pauseReason };
        }
        // 检查连续亏损限制 (如果限制值大于999999则视为禁用)
        if (this.config.consecutiveLossLimit < 999999 && this.stats.consecutiveLosses >= this.config.consecutiveLossLimit) {
            this.pauseTrading(`连续亏损${this.stats.consecutiveLosses}笔，达到限制${this.config.consecutiveLossLimit}笔`);
            return { allowed: false, reason: this.stats.pauseReason };
        }
        // 检查当日亏损限制
        if (this.stats.dailyPnL <= -this.config.maxDailyLossSol) {
            this.pauseTrading(`当日亏损${Math.abs(this.stats.dailyPnL).toFixed(4)} SOL，超过限制${this.config.maxDailyLossSol} SOL`);
            return { allowed: false, reason: this.stats.pauseReason };
        }
        // 检查余额是否充足
        if (this.stats.currentBalance < this.config.tradeAmountSol) {
            this.pauseTrading(`余额不足：${this.stats.currentBalance.toFixed(4)} SOL < ${this.config.tradeAmountSol} SOL`);
            return { allowed: false, reason: this.stats.pauseReason };
        }
        return { allowed: true, reason: '风险检查通过' };
    }
    // 🔥 记录买入失败
    recordBuyFailure(reason) {
        this.stats.consecutiveBuyFailures++;
        console.log(`❌ 买入失败记录: 连续失败${this.stats.consecutiveBuyFailures}次 - ${reason}`);
        // 检查是否达到连续失败限制
        if (this.config.consecutiveBuyFailureLimit > 0 &&
            this.stats.consecutiveBuyFailures >= this.config.consecutiveBuyFailureLimit) {
            this.pauseTrading(`连续买入失败${this.stats.consecutiveBuyFailures}次，达到限制${this.config.consecutiveBuyFailureLimit}次`);
        }
    }
    // 🔥 记录买入成功 - 重置失败计数
    recordBuySuccess() {
        if (this.stats.consecutiveBuyFailures > 0) {
            console.log(`✅ 买入成功，重置连续失败计数（之前: ${this.stats.consecutiveBuyFailures}次）`);
            this.stats.consecutiveBuyFailures = 0;
        }
    }
    // 记录交易结果
    recordTrade(pnl, fees = 0, tradeType = 'buy') {
        const isWinning = pnl > 0;
        const timestamp = new Date();
        // 更新余额和PnL
        this.stats.currentBalance += pnl - fees;
        this.stats.totalPnL += pnl;
        this.stats.totalFees += fees;
        this.stats.dailyPnL += pnl;
        this.stats.totalTrades++;
        // 更新高水位线和最大回撤
        if (this.stats.currentBalance > this.highWaterMark) {
            this.highWaterMark = this.stats.currentBalance;
        }
        const currentDrawdown = (this.highWaterMark - this.stats.currentBalance) / this.highWaterMark;
        if (currentDrawdown > this.stats.maxDrawdown) {
            this.stats.maxDrawdown = currentDrawdown;
        }
        // 更新胜负统计
        if (isWinning) {
            this.stats.winningTrades++;
            this.stats.consecutiveLosses = 0; // 重置连续亏损
        }
        else {
            this.stats.losingTrades++;
            this.stats.consecutiveLosses++;
        }
        // 更新胜率和平均盈亏
        this.stats.winRate = this.stats.totalTrades > 0 ? this.stats.winningTrades / this.stats.totalTrades : 0;
        this.stats.averagePnL = this.stats.totalTrades > 0 ? this.stats.totalPnL / this.stats.totalTrades : 0;
        // 添加到交易历史
        this.tradeHistory.push({ timestamp, pnl, success: isWinning, type: tradeType });
        if (this.tradeHistory.length > 100) {
            this.tradeHistory = this.tradeHistory.slice(-100); // 保留最近100笔
        }
        console.log(`📊 风险管理 - 交易记录: ${tradeType.toUpperCase()} PnL ${pnl > 0 ? '+' : ''}${pnl.toFixed(4)} SOL, 余额: ${this.stats.currentBalance.toFixed(4)} SOL, 连续亏损: ${this.stats.consecutiveLosses}, 连续买入失败: ${this.stats.consecutiveBuyFailures}`);
    }
    // 暂停交易
    pauseTrading(reason) {
        this.stats.tradingPaused = true;
        this.stats.pauseReason = reason;
        console.log(`🔴 交易已暂停: ${reason}`);
    }
    // 恢复交易 (手动调用)
    resumeTrading() {
        this.stats.tradingPaused = false;
        this.stats.pauseReason = '';
        this.stats.consecutiveLosses = 0; // 重置连续亏损计数
        this.stats.consecutiveBuyFailures = 0; // 🔥 重置连续买入失败计数
        console.log(`✅ 交易已恢复，所有计数器已重置`);
    }
    // 🔥 新增：强制重置风险状态
    forceResetRisk() {
        this.stats.tradingPaused = false;
        this.stats.pauseReason = '';
        this.stats.consecutiveLosses = 0;
        this.stats.consecutiveBuyFailures = 0;
        this.stats.dailyPnL = 0;
        console.log(`🔄 风险状态已强制重置`);
    }
    // 重置日内统计 (每日调用)
    resetDailyStats() {
        this.stats.dailyPnL = 0;
        this.stats.consecutiveBuyFailures = 0; // 🔥 每日重置买入失败计数
        this.dailyStartBalance = this.stats.currentBalance;
        console.log(`🌅 日内统计已重置，起始余额: ${this.dailyStartBalance.toFixed(4)} SOL`);
    }
    // 获取统计数据
    getStats() {
        return { ...this.stats };
    }
    // 🔥 获取详细风险报告
    getRiskReport() {
        const recent24h = this.tradeHistory.filter(t => Date.now() - t.timestamp.getTime() < 24 * 60 * 60 * 1000);
        const recentBuyFailures = recent24h.filter(t => t.type === 'buy' && !t.success).length;
        return `
🛡️ 风险管理报告:
   当前状态: ${this.stats.tradingPaused ? '🔴 已暂停' : '🟢 正常'}
   ${this.stats.tradingPaused ? `暂停原因: ${this.stats.pauseReason}` : ''}
   
📊 统计数据:
   总交易数: ${this.stats.totalTrades}
   胜率: ${(this.stats.winRate * 100).toFixed(1)}%
   总盈亏: ${this.stats.totalPnL > 0 ? '+' : ''}${this.stats.totalPnL.toFixed(4)} SOL
   当日盈亏: ${this.stats.dailyPnL > 0 ? '+' : ''}${this.stats.dailyPnL.toFixed(4)} SOL
   
⚠️ 风险指标:
   连续亏损: ${this.stats.consecutiveLosses}/${this.config.consecutiveLossLimit}
   连续买入失败: ${this.stats.consecutiveBuyFailures}/${this.config.consecutiveBuyFailureLimit}
   最大回撤: ${(this.stats.maxDrawdown * 100).toFixed(2)}%
   24h买入失败: ${recentBuyFailures}次
   `;
    }
}
exports.SimpleRiskManager = SimpleRiskManager;
// 🔥 仓位管理器
class PositionManager {
    constructor(config) {
        this.positions = new Map();
        this.positionHistory = [];
        this.config = config;
    }
    // 开仓
    openPosition(tokenAddress, amount, entryPrice) {
        const position = {
            id: `POS_${tokenAddress.slice(0, 8)}_${Date.now()}`,
            tokenAddress,
            amount,
            entryPrice,
            timestamp: new Date(),
            status: 'open',
            stopLossPrice: entryPrice * (1 - this.config.stopLossPercentage) // 计算止损价格
        };
        this.positions.set(tokenAddress, position);
        console.log(`📈 开仓: ${position.id}, 数量: ${amount.toFixed(2)}, 入场价: ${entryPrice.toFixed(8)}, 止损价: ${position.stopLossPrice?.toFixed(8)}`);
        return position;
    }
    // 平仓
    closePosition(tokenAddress, exitPrice, pnl) {
        const position = this.positions.get(tokenAddress);
        if (!position) {
            console.warn(`⚠️ 未找到仓位: ${tokenAddress.slice(0, 8)}...`);
            return null;
        }
        // 更新仓位信息
        position.exitPrice = exitPrice;
        position.exitTimestamp = new Date();
        position.pnl = pnl;
        position.status = 'closed';
        // 从活跃仓位移除，添加到历史
        this.positions.delete(tokenAddress);
        this.positionHistory.push(position);
        // 保持历史记录在合理范围
        if (this.positionHistory.length > 200) {
            this.positionHistory = this.positionHistory.slice(-200);
        }
        const holdingTimeMs = position.exitTimestamp.getTime() - position.timestamp.getTime();
        const holdingTimeMinutes = Math.round(holdingTimeMs / (1000 * 60));
        console.log(`📉 平仓: ${position.id}, 退出价: ${exitPrice.toFixed(8)}, PnL: ${pnl > 0 ? '+' : ''}${pnl.toFixed(4)} SOL, 持仓时间: ${holdingTimeMinutes}分钟`);
        return position;
    }
    // 检查止损
    checkStopLoss(tokenAddress, currentPrice) {
        const position = this.positions.get(tokenAddress);
        if (!position || !position.stopLossPrice) {
            return false;
        }
        const shouldStopLoss = currentPrice <= position.stopLossPrice;
        if (shouldStopLoss) {
            const lossPercentage = ((position.stopLossPrice - position.entryPrice) / position.entryPrice * 100);
            console.log(`🛑 触发止损: ${position.id}, 当前价格: ${currentPrice.toFixed(8)}, 止损价: ${position.stopLossPrice.toFixed(8)}, 亏损: ${lossPercentage.toFixed(2)}%`);
        }
        return shouldStopLoss;
    }
    // 获取指定仓位
    getPosition(tokenAddress) {
        return this.positions.get(tokenAddress);
    }
    // 获取所有活跃仓位
    getActivePositions() {
        return Array.from(this.positions.values());
    }
    // 检查是否可以开新仓
    canOpenNewPosition() {
        return this.positions.size < this.config.maxPositions;
    }
    // 获取仓位统计
    getPositionStats() {
        const closedPositions = this.positionHistory.filter(p => p.status === 'closed' && p.pnl !== undefined);
        const winningPositions = closedPositions.filter(p => p.pnl > 0);
        const losingPositions = closedPositions.filter(p => p.pnl <= 0);
        const totalRealizedPnL = closedPositions.reduce((sum, p) => sum + (p.pnl || 0), 0);
        return {
            activePositions: this.positions.size,
            totalPositions: this.positionHistory.length,
            winningPositions: winningPositions.length,
            losingPositions: losingPositions.length,
            winRate: closedPositions.length > 0 ? winningPositions.length / closedPositions.length : 0,
            totalRealizedPnL,
            averagePnL: closedPositions.length > 0 ? totalRealizedPnL / closedPositions.length : 0
        };
    }
}
exports.PositionManager = PositionManager;
// 🔥 配置验证器
class ConfigValidator {
    static validate(config) {
        const errors = [];
        // 基础配置验证
        if (!config.tradeAmountSol || config.tradeAmountSol <= 0) {
            errors.push('tradeAmountSol must be positive');
        }
        if (!config.initialCapitalSol || config.initialCapitalSol <= 0) {
            errors.push('initialCapitalSol must be positive');
        }
        if (!config.maxPositions || config.maxPositions < 1) {
            errors.push('maxPositions must be at least 1');
        }
        // 风险管理验证
        if (config.stopLossPercentage && (config.stopLossPercentage <= 0 || config.stopLossPercentage >= 1)) {
            errors.push('stopLossPercentage must be between 0 and 1');
        }
        if (config.consecutiveLossLimit && config.consecutiveLossLimit < 1) {
            errors.push('consecutiveLossLimit must be at least 1');
        }
        // 🔥 新增：买入失败限制验证
        if (config.consecutiveBuyFailureLimit && config.consecutiveBuyFailureLimit < 1) {
            errors.push('consecutiveBuyFailureLimit must be at least 1');
        }
        // AI模型验证
        if (config.buyThreshold && (config.buyThreshold <= 0 || config.buyThreshold > 1)) {
            errors.push('buyThreshold must be between 0 and 1');
        }
        if (config.sellThreshold && (config.sellThreshold <= 0 || config.sellThreshold > 1)) {
            errors.push('sellThreshold must be between 0 and 1');
        }
        return { valid: errors.length === 0, errors };
    }
    static showConfigSummary(config) {
        console.log('\n📋 交易配置总结:');
        console.log('==================');
        console.log(`💰 每笔交易: ${config.tradeAmountSol} SOL`);
        console.log(`💼 初始资金: ${config.initialCapitalSol} SOL`);
        console.log(`📊 最大仓位: ${config.maxPositions}`);
        console.log(`📋 交易模式: ${config.paperTrading ? '模拟' : '真实'}`);
        console.log(`📈 止损比例: ${(config.stopLossPercentage * 100).toFixed(1)}%`);
        console.log(`⚠️ 连续亏损限制: ${config.consecutiveLossLimit === 999999 ? '禁用' : config.consecutiveLossLimit + '笔'}`);
        console.log(`❌ 连续买入失败限制: ${config.consecutiveBuyFailureLimit === 0 ? '禁用' : config.consecutiveBuyFailureLimit + '次'}`);
        console.log(`🎯 AI买入阈值: ${(config.buyThreshold * 100).toFixed(1)}%`);
        console.log(`🎯 AI卖出阈值: ${(config.sellThreshold * 100).toFixed(1)}%`);
        console.log('==================\n');
    }
}
exports.ConfigValidator = ConfigValidator;
