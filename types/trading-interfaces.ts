import { PublicKey } from '@solana/web3.js';

// 基础交易配置接口
export interface TradingConfig {
  // 基础交易配置
  tradeAmountSol: number;
  initialCapitalSol: number;
  maxPositions: number;
  paperTrading: boolean;
  slippageTolerance: number;
  
  // 风险管理配置
  maxDailyLossSol: number;
  stopLossPercentage: number;
  consecutiveLossLimit: number;
  
  // AI模型配置
  buyThreshold: number;
  sellThreshold: number;
  
  // 监控配置
  logLevel: 'info' | 'warn' | 'error';
  enableDetailedLogs: boolean;
  statsUpdateIntervalSeconds: number;
}

// 交易统计接口
export interface TradingStats {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  totalPnL: number;
  totalFees: number;
  averagePnL: number;
  maxDrawdown: number;
  currentBalance: number;
  activePositions: number;
  consecutiveLosses: number;
  dailyPnL: number;
  tradingPaused: boolean;
  pauseReason: string;
}

// 仓位接口
export interface Position {
  id: string;
  tokenAddress: string;
  amount: number;
  entryPrice: number;
  timestamp: Date;
  status: 'open' | 'closed';
  exitPrice?: number;
  exitTimestamp?: Date;
  pnl?: number;
  stopLossPrice?: number;
}

// GRPC订阅相关接口
export interface SubscribeRequest {
  accounts: { [key: string]: SubscribeRequestFilterAccounts };
  slots: { [key: string]: SubscribeRequestFilterSlots };
  transactions: { [key: string]: SubscribeRequestFilterTransactions };
  transactionsStatus: { [key: string]: SubscribeRequestFilterTransactions };
  blocks: { [key: string]: SubscribeRequestFilterBlocks };
  blocksMeta: { [key: string]: SubscribeRequestFilterBlocksMeta };
  entry: { [key: string]: SubscribeRequestFilterEntry };
  commitment?: CommitmentLevel | undefined;
  accountsDataSlice: SubscribeRequestAccountsDataSlice[];
  ping?: SubscribeRequestPing | undefined;
}

// Token活动跟踪接口
export interface TokenActivity {
  address: string;
  firstSeen: Date;
  lastSeen: Date;
  transactionCount: number;
  actions: Array<{
    type: 'buy' | 'sell';
    amount: number;
    timestamp: Date;
    price?: number;
  }>;
}

// 实盘交易结果接口
export interface RealTradeResult {
  success: boolean;
  bundleId?: string;
  actualPrice?: number;
  actualAmount?: number;
  gasFee?: number;
  error?: string;
}

// 池子数据接口
export interface PoolData {
  poolAddress: PublicKey;
  mintAddress: PublicKey;
  coinCreator: PublicKey;
  poolBaseTokenAccount: PublicKey;
  poolQuoteTokenAccount: PublicKey;
  vaultAuthority: PublicKey;
  vaultAta: PublicKey;
  tokenBalance: number;
  solBalance: number;
  // 🔥 新增：vault ATA异步计算标记
  needsVaultAta?: boolean;
}

// Token交易实例接口
export interface TokenTradingInstance {
  address: string;
  activity: TokenActivity;
  buyPredictor: any; // BuyPredictor;
  sellPredictor: any; // SellPredictor;
  featureWindow: Array<any>;
  lastPrediction: Date;
  isActive: boolean;
  isTrading: boolean;
  lastTradeTime: Date;
  tradingHistory: Array<{
    type: 'buy' | 'sell';
    timestamp: Date;
    tokenAmount: number;
    solAmount: number;
    price: number;
    prediction: number;
    bundleId?: string;
  }>;
  currentHolding: {
    amount: number;
    buyPrice: number;
    buyTime: Date;
    buySolAmount: number;
    buyGasFee: number;
    buyPlatformFee: number;
    totalBuyCost: number;
    bundleId: string;
  } | null;
  stats: {
    totalTrades: number;
    successfulTrades: number;
    totalPnL: number;
    currentPosition: number;
  };
  lastProcessedTransactionTime?: Date;
  // 🔥 新增：Pool信息提取状态
  poolExtracted?: boolean;
  realPoolData?: PoolData;
  poolData?: PoolData; // 备用池子数据
  // 🔥 新增：交易状态跟踪
  pendingTransactions: Map<string, {
    type: 'buy' | 'sell';
    timestamp: Date;
    signature: string;
    confirmed: boolean;
  }>;
  lastConfirmedSellTime?: Date; // 最后一次确认卖出的时间
}

// 交易决策相关类型
export interface TradingDecision {
  action: 'buy' | 'sell' | 'hold';
  confidence: number;
  reason: string;
}

// 价格数据接口
export interface PriceData {
  price: number;
  timestamp: Date;
  solAmount: number;
  tokenAmount: number;
  action: 'buy' | 'sell';
  source: 'stream' | 'own_trade';
}

// 交易数据提供者接口
export interface TradingDataProvider {
  getActiveTokens(): string[];
  getTokenStats(): Array<{
    tokenAddress: string;
    totalTrades: number;
    totalPnL: number;
    transactionCount: number;
    winRate: number;
  }>;
  getCurrentPositions(): Array<{
    tokenAddress: string;
    position: number;
    pnl: number;
    buyPrice: number;
    currentPrice: number;
    holdingTime: number;
  }>;
  getSellOnlyBufferInfo(): Array<{
    tokenAddress: string;
    reason: string;
    addedTime: Date;
    bufferDurationMinutes: number;
    hasPosition: boolean;
  }>;
  isSystemRunning(): boolean;
}

// 事务数据接口
export interface TransactionData {
  signature: string;
  timestamp: Date;
  success: boolean;
  data: any;
}

// 占位符类型（需要从实际实现中导入）
export type CommitmentLevel = any;
export type SubscribeRequestFilterAccounts = any;
export type SubscribeRequestFilterSlots = any;
export type SubscribeRequestFilterTransactions = any;
export type SubscribeRequestFilterBlocks = any;
export type SubscribeRequestFilterBlocksMeta = any;
export type SubscribeRequestFilterEntry = any;
export type SubscribeRequestAccountsDataSlice = any;
export type SubscribeRequestPing = any; 