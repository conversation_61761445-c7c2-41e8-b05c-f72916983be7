# 启用鼠标支持（包括鼠标滚轮浏览）
set -g mouse on

# 设置终端类型
set -g default-terminal "screen-256color"

# 设置历史记录行数
set -g history-limit 10000

# 启用鼠标滚轮复制模式
bind -n WheelUpPane if-shell -F -t = "#{mouse_any_flag}" "send-keys -M" "if -Ft= '#{pane_in_mode}' 'send-keys -M' 'select-pane -t=; copy-mode -e; send-keys -M'"
bind -n WheelDownPane select-pane -t= \; send-keys -M

# 重新加载配置文件的快捷键
bind r source-file ~/.tmux.conf \; display-message "Config reloaded!"

# 更好的窗口和面板索引（从1开始）
set -g base-index 1
setw -g pane-base-index 1

# 自动重新编号窗口
set -g renumber-windows on 