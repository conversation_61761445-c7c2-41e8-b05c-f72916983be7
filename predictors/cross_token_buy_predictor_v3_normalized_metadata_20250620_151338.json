{"model_version": "V3_BUY", "description": "Top 20 features optimized buy prediction model", "feature_count": 20, "top_20_features": ["sell_volume_sol_5s", "volume_sol_mean_5s", "volume_sol_mean_10s", "sell_volume_sol_10s", "buy_volume_sol_60s", "buy_price_mean_60s", "volume_sol_max_5s", "volume_sol_mean_30s", "buy_price_mean_10s", "buy_volume_sol_30s", "volume_sol_total_5s", "buy_volume_sol_5s", "buy_price_mean_30s", "volume_sol_total_10s", "buy_price_mean_5s", "buy_volume_sol_10s", "buy_sell_volume_ratio_60s", "buy_ratio_60s", "volume_sol_max_10s", "volume_sol_max_30s"], "selected_features": ["sell_volume_sol_5s", "volume_sol_mean_5s", "volume_sol_mean_10s", "sell_volume_sol_10s", "buy_volume_sol_60s", "buy_price_mean_60s", "volume_sol_max_5s", "volume_sol_mean_30s", "buy_price_mean_10s", "buy_volume_sol_30s", "volume_sol_total_5s", "buy_volume_sol_5s", "buy_price_mean_30s", "volume_sol_total_10s", "buy_price_mean_5s", "buy_volume_sol_10s", "buy_sell_volume_ratio_60s", "buy_ratio_60s", "volume_sol_max_10s", "volume_sol_max_30s"], "training_time": "20250620_151338", "model_type": "catboost", "model_params": {"iterations": 200, "learning_rate": 0.1, "depth": 6, "random_seed": 42, "verbose": false, "early_stopping_rounds": 20}, "prediction_target": "target_wallet_buy_events", "sample_generation": {"positive_samples": "target_wallet_buy_timestamps", "negative_samples": "buy_segments_and_random_timestamps", "first_buy_handling": "buy_time_minus_120_seconds"}, "scaler_format": "json_parameters", "compatibility": {"typescript_compatible": true, "model_format": "cbm", "scaler_format": "json"}}