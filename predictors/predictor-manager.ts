import { BuyPredictor } from './buy-predictor';
import { SellPredictor } from './sell-predictor';
import path from 'path';

/**
 * 单例预测器管理器 - 避免重复加载模型
 */
export class PredictorManager {
  private static instance: PredictorManager | null = null;
  private buyPredictor: BuyPredictor | null = null;
  private sellPredictor: SellPredictor | null = null;
  private isInitialized: boolean = false;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): PredictorManager {
    if (!PredictorManager.instance) {
      PredictorManager.instance = new PredictorManager();
    }
    return PredictorManager.instance;
  }

  /**
   * 初始化预测器（只执行一次）
   */
  public async initialize(
    buyThreshold: number = 0.5,
    sellThreshold: number = 0.3,
    windowSize: number = 20
  ): Promise<void> {
    if (this.isInitialized) {
      console.log('📊 预测器管理器已经初始化，跳过重复初始化');
      return;
    }

    console.log('🚀 初始化预测器管理器...');
    
    try {
      // 使用绝对路径解决模型文件加载问题
      const buyModelPath = path.resolve(__dirname, '../catboost_focal_blocktol_2025.cbm');
      const sellModelPath = path.resolve(__dirname, '../best_catboost_sell_model.cbm');

      console.log(`📊 加载买入模型: ${buyModelPath}`);
      console.log(`📊 加载卖出模型: ${sellModelPath}`);

      // 创建单例预测器
      this.buyPredictor = new BuyPredictor(buyModelPath, windowSize, buyThreshold);
      this.sellPredictor = new SellPredictor(sellModelPath, windowSize, sellThreshold);

      // 等待模型加载完成
      await this.waitForModelsToLoad();

      this.isInitialized = true;
      console.log('✅ 预测器管理器初始化完成');
      console.log(`   🎯 买入阈值: ${(buyThreshold * 100).toFixed(1)}%`);
      console.log(`   🎯 卖出阈值: ${(sellThreshold * 100).toFixed(1)}%`);
      console.log(`   📊 窗口大小: ${windowSize}`);
      
    } catch (error) {
      console.error('❌ 预测器管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 等待模型加载完成
   */
  private async waitForModelsToLoad(): Promise<void> {
    let attempts = 0;
    const maxAttempts = 50; // 最多等待5秒
    
    while (attempts < maxAttempts) {
      if (this.buyPredictor?.isReady() && this.sellPredictor?.isReady()) {
        console.log('✅ 所有预测器模型加载完成');
        return;
      }
      
      await new Promise(resolve => setTimeout(resolve, 100)); // 等待100ms
      attempts++;
    }
    
    throw new Error('模型加载超时');
  }

  /**
   * 获取买入预测器
   */
  public getBuyPredictor(): BuyPredictor {
    if (!this.isInitialized || !this.buyPredictor) {
      throw new Error('预测器管理器未初始化');
    }
    return this.buyPredictor;
  }

  /**
   * 获取卖出预测器
   */
  public getSellPredictor(): SellPredictor {
    if (!this.isInitialized || !this.sellPredictor) {
      throw new Error('预测器管理器未初始化');
    }
    return this.sellPredictor;
  }

  /**
   * 检查是否已初始化
   */
  public isReady(): boolean {
    return this.isInitialized && 
           this.buyPredictor?.isReady() === true && 
           this.sellPredictor?.isReady() === true;
  }

  /**
   * 获取预测器状态
   */
  public getStatus(): {
    isInitialized: boolean;
    buyPredictorReady: boolean;
    sellPredictorReady: boolean;
    buyModelPath: string | null;
    sellModelPath: string | null;
  } {
    return {
      isInitialized: this.isInitialized,
      buyPredictorReady: this.buyPredictor?.isReady() === true,
      sellPredictorReady: this.sellPredictor?.isReady() === true,
      buyModelPath: this.buyPredictor ? this.buyPredictor.getModelInfo().modelPath : null,
      sellModelPath: this.sellPredictor ? this.sellPredictor.getModelInfo().modelPath : null
    };
  }

  /**
   * 清理所有预测器
   */
  public cleanup(): void {
    console.log('🧹 清理预测器管理器...');
    
    if (this.buyPredictor) {
      this.buyPredictor.clearHistory();
    }
    
    if (this.sellPredictor) {
      this.sellPredictor.clearHistory();
    }
    
    this.isInitialized = false;
    console.log('✅ 预测器管理器清理完成');
  }

  /**
   * 重置单例（用于测试）
   */
  public static reset(): void {
    if (PredictorManager.instance) {
      PredictorManager.instance.cleanup();
      PredictorManager.instance = null;
    }
  }
} 