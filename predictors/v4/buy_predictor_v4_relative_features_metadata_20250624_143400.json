{"model_version": "V4_BUY_RELATIVE_FEATURES", "version_name": "Buy Predictor V4 - Relative Features for Multi-Token", "description": "Multi-token adaptive buy prediction model using 100% relative features", "save_timestamp": "20250624_143400", "model_type": "catboost", "model_format": "CBM", "scaler_format": "JSON", "feature_engineering": "pure_relative_features", "training_approach": "multi_token_sussy_data", "feature_count": 48, "feature_type": "relative_ratios_and_comparisons", "prediction_target": "buy", "target_description": "target_wallet_buy_events_multi_token", "datasets_used": "<PERSON>ssy_pumpswap", "cross_token_adaptability": "high", "recommended_thresholds": {"conservative": 0.7, "balanced": 0.5, "aggressive": 0.3}, "feature_categories": {"buy_sell_ratios": 8, "volume_ratios": 12, "price_changes": 12, "cross_window_comparisons": 8, "buy_specific_features": 8, "total_features": 48}, "time_windows": [5, 10, 30, 60], "relative_feature_advantages": {"cross_token_compatibility": true, "scale_invariant": true, "price_agnostic": true, "volume_normalized": true}, "model_params": {"iterations": 300, "learning_rate": 0.08, "depth": 8, "random_seed": 42, "verbose": false, "eval_metric": "AUC", "early_stopping_rounds": 30}, "training_info": {"training_time": "20250624_143400", "model_approach": "relative_features_multi_token", "sample_generation": "sussy_target_wallet_events"}, "deployment_ready": true, "production_notes": "Optimized for multi-token environments with relative features", "compatibility": {"typescript_compatible": true, "python_compatible": true, "json_only": true, "no_pickle_dependencies": true, "multi_token_ready": true}}