import * as catboost from 'catboost';
import * as fs from 'fs';
import * as path from 'path';
import { performance } from 'perf_hooks';

// Based on SELL_PREDICTOR_V4_COMPLETE_FEATURE_CONSTRUCTION_GUIDE.md

export interface TransactionDataForSellV4 {
  timestamp: Date;
  transaction_type: 'buy' | 'sell';
  sol_amount: number;
}

export interface PriceDataV4 {
  timestamp: Date;
  price: number;
}

export interface SellFeaturesV4 {
  [key: string]: number;
}

export interface SellPredictionResultV4 {
  probability: number;
  prediction: boolean;
  confidence: number;
  timestamp: Date;
  predictionTimeMs: number;
  features?: SellFeaturesV4;
}

export interface ScalerParamsV4 {
  center_?: number[]; // RobustScaler
  scale_?: number[];
  feature_names: string[];
}

function safeDivide(numerator: number, denominator: number, defaultValue: number = 0.0): number {
  if (Math.abs(denominator) < 1e-9) {
      return defaultValue;
  }
  return numerator / denominator;
}

function calculateTrend(values: number[]): number {
    if (values.length < 2) {
        return 0.0;
    }
    const x = Array.from({ length: values.length }, (_, i) => i);
    const n = values.length;
    
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * values[i], 0);
    const sumX2 = x.reduce((sum, xi) => sum + xi * xi, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = n * sumX2 - sumX * sumX;

    if (Math.abs(denominator) < 1e-9) {
        return 0.0;
    }

    const slope = numerator / denominator;
    const range = Math.max(...values) - Math.min(...values);

    return safeDivide(slope, range);
}

export class SellPredictorV4 {
  private model: any;
  private modelPath: string;
  private scalerPath: string;
  private featuresPath: string;
  private scalerParams: ScalerParamsV4 | null = null;
  private featureNames: string[] = [];
  private threshold: number;
  private transactionHistory: TransactionDataForSellV4[] = [];
  private priceHistory: PriceDataV4[] = [];
  private isModelLoaded: boolean = false;
  private maxHistorySize: number = 5000;

  constructor(
    modelPath: string = 'predictors/v4/sell_predictor_v4_json_20250624_124138.cbm',
    scalerPath: string = 'predictors/v4/sell_predictor_v4_json_scaler_20250624_124138.json',
    featuresPath: string = 'predictors/v4/sell_predictor_v4_json_features_20250624_124138.json',
    threshold: number = 0.5
  ) {
    this.modelPath = path.resolve(modelPath);
    this.scalerPath = path.resolve(scalerPath);
    this.featuresPath = path.resolve(featuresPath);
    this.threshold = threshold;

    console.log(`🤖 SellPredictorV4 initializing: Threshold=${(threshold * 100).toFixed(1)}%`);
    this.initializeModel();
  }

  private initializeModel(): void {
    try {
      if (!fs.existsSync(this.modelPath)) {
        console.warn(`Sell model V4 file not found: ${this.modelPath}`);
        return;
      }
      this.model = new catboost.Model();
      this.model.loadModel(this.modelPath);

      if (fs.existsSync(this.scalerPath)) {
        this.scalerParams = JSON.parse(fs.readFileSync(this.scalerPath, 'utf8'));
      } else {
        console.warn(`Scaler file not found: ${this.scalerPath}`);
      }

      if (fs.existsSync(this.featuresPath)) {
        this.featureNames = JSON.parse(fs.readFileSync(this.featuresPath, 'utf8'));
      } else {
        console.warn(`Features file not found: ${this.featuresPath}`);
      }

      this.isModelLoaded = true;
      console.log(`✅ SellPredictorV4 model loaded successfully (Features: ${this.featureNames.length})`);
    } catch (error) {
      console.error(`❌ Failed to load SellPredictorV4 model: ${error}`);
      this.isModelLoaded = false;
    }
  }

  public addTransaction(transaction: TransactionDataForSellV4): void {
    this.transactionHistory.push(transaction);
    if (this.transactionHistory.length > this.maxHistorySize) {
      this.transactionHistory.shift();
    }
  }

  public addPriceData(price: PriceDataV4): void {
    this.priceHistory.push(price);
    if (this.priceHistory.length > this.maxHistorySize) {
      this.priceHistory.shift();
    }
  }

  public addTransactionBatch(transactions: TransactionDataForSellV4[]): void {
    this.transactionHistory.push(...transactions);
    if (this.transactionHistory.length > this.maxHistorySize) {
        this.transactionHistory = this.transactionHistory.slice(-this.maxHistorySize);
    }
  }

  public addPriceDataBatch(prices: PriceDataV4[]): void {
    this.priceHistory.push(...prices);
    if (this.priceHistory.length > this.maxHistorySize) {
        this.priceHistory = this.priceHistory.slice(-this.maxHistorySize);
    }
  }

  private extractFeaturesV4(currentTimestamp: Date): SellFeaturesV4 | null {
    const sixtySecondsAgo = currentTimestamp.getTime() - (60 + 5) * 1000; // 60s window + 5s buffer
    const recentTransactions = this.transactionHistory.filter(tx => tx.timestamp.getTime() >= sixtySecondsAgo && tx.timestamp < currentTimestamp);
    const recentPrices = this.priceHistory.filter(p => p.timestamp.getTime() >= sixtySecondsAgo && p.timestamp < currentTimestamp);
  
    if (recentTransactions.length < 2 || recentPrices.length < 2) {
      return null;
    }
  
    const features: SellFeaturesV4 = {};
    const windows = [5, 10, 30, 60];
    const bufferSeconds = 5;
  
    for (const windowSize of windows) {
        const windowEnd = currentTimestamp.getTime() - bufferSeconds * 1000;
        const windowStart = windowEnd - windowSize * 1000;

        const windowTxData = recentTransactions.filter(tx => tx.timestamp.getTime() >= windowStart && tx.timestamp.getTime() < windowEnd);
        const windowPriceData = recentPrices.filter(p => p.timestamp.getTime() >= windowStart && p.timestamp.getTime() < windowEnd);

        // Features calculation
        const buyTxs = windowTxData.filter(t => t.transaction_type === 'buy');
        const sellTxs = windowTxData.filter(t => t.transaction_type === 'sell');
        const buy_count = buyTxs.length;
        const sell_count = sellTxs.length;
        const total_count = windowTxData.length;
        features[`buy_ratio_${windowSize}s`] = safeDivide(buy_count, total_count);
        features[`sell_ratio_${windowSize}s`] = safeDivide(sell_count, total_count);
        
        const buy_volume = buyTxs.reduce((s, t) => s + t.sol_amount, 0);
        const sell_volume = sellTxs.reduce((s, t) => s + t.sol_amount, 0);
        const total_volume = buy_volume + sell_volume;
        features[`buy_volume_ratio_${windowSize}s`] = safeDivide(buy_volume, total_volume);
        features[`sell_volume_ratio_${windowSize}s`] = safeDivide(sell_volume, total_volume);
        features[`buy_sell_volume_ratio_${windowSize}s`] = safeDivide(buy_volume, sell_volume, buy_volume > 0 ? 10.0 : 0.0);

        const prices = windowPriceData.map(p => p.price);
        if (prices.length > 1) {
            const price_start = prices[0];
            const price_end = prices[prices.length - 1];
            features[`price_change_ratio_${windowSize}s`] = safeDivide(price_end - price_start, price_start);
            
            const price_mean = prices.reduce((a,b) => a+b, 0) / prices.length;
            const price_max = Math.max(...prices);
            const price_min = Math.min(...prices);
            features[`price_max_ratio_${windowSize}s`] = safeDivide(price_max, price_mean);
            features[`price_min_ratio_${windowSize}s`] = safeDivide(price_min, price_mean);
            
            const price_std = Math.sqrt(prices.map(x => Math.pow(x - price_mean, 2)).reduce((a, b) => a + b) / prices.length);
            features[`price_volatility_${windowSize}s`] = safeDivide(price_std, price_mean);
            features[`price_trend_${windowSize}s`] = calculateTrend(prices);
        } else {
            features[`price_change_ratio_${windowSize}s`] = 0;
            features[`price_max_ratio_${windowSize}s`] = 0;
            features[`price_min_ratio_${windowSize}s`] = 0;
            features[`price_volatility_${windowSize}s`] = 0;
            features[`price_trend_${windowSize}s`] = 0;
        }

        const volumes = windowTxData.map(t => t.sol_amount);
        if (volumes.length > 1) {
            const volume_mean = volumes.reduce((a, b) => a + b, 0) / volumes.length;
            const volume_std = Math.sqrt(volumes.map(x => Math.pow(x - volume_mean, 2)).reduce((a, b) => a + b) / volumes.length);
            features[`volume_volatility_${windowSize}s`] = safeDivide(volume_std, volume_mean);
            features[`volume_trend_${windowSize}s`] = calculateTrend(volumes);

            const mid = Math.floor(volumes.length / 2);
            const first_half_avg = volumes.slice(0, mid).reduce((a,b) => a+b, 0) / mid;
            const second_half_avg = volumes.slice(mid).reduce((a,b) => a+b, 0) / (volumes.length - mid);
            features[`volume_change_ratio_${windowSize}s`] = safeDivide(second_half_avg - first_half_avg, first_half_avg, second_half_avg > 0 ? 1.0 : 0.0);
        } else {
            features[`volume_volatility_${windowSize}s`] = 0;
            features[`volume_trend_${windowSize}s`] = 0;
            features[`volume_change_ratio_${windowSize}s`] = 0;
        }

        if (prices.length > 2) {
            // Calculate velocity (price differences)
            const velocity = [];
            for (let i = 1; i < prices.length; i++) {
                velocity.push(prices[i] - prices[i-1]);
            }
            
            if (velocity.length > 1) {
                // Calculate acceleration (velocity differences)
                const acceleration = [];
                for (let i = 1; i < velocity.length; i++) {
                    acceleration.push(velocity[i] - velocity[i-1]);
                }
                
                const avg_acceleration = acceleration.reduce((a, b) => a + b, 0) / acceleration.length;
                const price_mean = prices.reduce((a, b) => a + b, 0) / prices.length;
                features[`price_acceleration_${windowSize}s`] = safeDivide(avg_acceleration, price_mean);
            } else {
                features[`price_acceleration_${windowSize}s`] = 0;
            }
        } else {
            features[`price_acceleration_${windowSize}s`] = 0;
        }
        
        // 添加缺失的 buy_sell_price_ratio 特征
        // 使用已经定义的 buyTxs 和 sellTxs
        if (buyTxs.length > 0 && sellTxs.length > 0 && windowPriceData.length > 0) {
            // 计算买入交易的平均价格
            const buyTxPrices = buyTxs.map(tx => {
                const closestPrice = windowPriceData.find(p => 
                    Math.abs(p.timestamp.getTime() - tx.timestamp.getTime()) < 2000
                );
                return closestPrice ? closestPrice.price : null;
            }).filter(p => p !== null) as number[];
            
            // 计算卖出交易的平均价格
            const sellTxPrices = sellTxs.map(tx => {
                const closestPrice = windowPriceData.find(p => 
                    Math.abs(p.timestamp.getTime() - tx.timestamp.getTime()) < 2000
                );
                return closestPrice ? closestPrice.price : null;
            }).filter(p => p !== null) as number[];
            
            if (buyTxPrices.length > 0 && sellTxPrices.length > 0) {
                const avgBuyPrice = buyTxPrices.reduce((a, b) => a + b, 0) / buyTxPrices.length;
                const avgSellPrice = sellTxPrices.reduce((a, b) => a + b, 0) / sellTxPrices.length;
                features[`buy_sell_price_ratio_${windowSize}s`] = safeDivide(avgBuyPrice, avgSellPrice);
            } else {
                features[`buy_sell_price_ratio_${windowSize}s`] = 1.0;
            }
        } else {
            features[`buy_sell_price_ratio_${windowSize}s`] = 1.0;
        }
        
        // 添加缺失的 volume_acceleration 特征
        if (volumes.length > 2) {
            // 计算交易量的一阶差分（速度）
            const volumeVelocity = [];
            for (let i = 1; i < volumes.length; i++) {
                volumeVelocity.push(volumes[i] - volumes[i-1]);
            }
            
            if (volumeVelocity.length > 1) {
                // 计算交易量的二阶差分（加速度）
                const volumeAcceleration = [];
                for (let i = 1; i < volumeVelocity.length; i++) {
                    volumeAcceleration.push(volumeVelocity[i] - volumeVelocity[i-1]);
                }
                
                const avgVolumeAcceleration = volumeAcceleration.reduce((a, b) => a + b, 0) / volumeAcceleration.length;
                const volumeMean = volumes.reduce((a, b) => a + b, 0) / volumes.length;
                features[`volume_acceleration_${windowSize}s`] = safeDivide(avgVolumeAcceleration, volumeMean);
            } else {
                features[`volume_acceleration_${windowSize}s`] = 0;
            }
        } else {
            features[`volume_acceleration_${windowSize}s`] = 0;
        }
    }

    // Cross-window features
    features['buy_volume_5s_vs_60s'] = safeDivide(features['buy_volume_ratio_5s'], features['buy_volume_ratio_60s']);
    features['volatility_5s_vs_30s'] = safeDivide(features['volume_volatility_5s'], features['volume_volatility_30s']);
    
    const trend_signs = [5, 10, 30, 60].map(w => Math.sign(features[`price_trend_${w}s`]));
    features['price_trend_consistency'] = Math.abs(trend_signs.reduce((a,b) => a+b, 0)) / trend_signs.length;

    const short_term_activity = (features['buy_volume_ratio_5s'] + features['buy_volume_ratio_10s']) / 2;
    const long_term_activity = (features['buy_volume_ratio_30s'] + features['buy_volume_ratio_60s']) / 2;
    features['market_acceleration'] = short_term_activity - long_term_activity;

    for (const key of this.featureNames) {
        if (features[key] === undefined || isNaN(features[key]) || !isFinite(features[key])) {
            features[key] = 0.0;
        }
    }
  
    return features;
  }
  
  private featuresToVector(features: SellFeaturesV4): number[] {
    return this.featureNames.map(name => features[name] || 0.0);
  }

  private standardizeFeatures(features: number[]): number[] {
    if (!this.scalerParams || !this.scalerParams.center_ || !this.scalerParams.scale_) {
      console.warn('Scaler params not loaded for sell predictor. Returning original features.');
      return features;
    }
    const { center_, scale_ } = this.scalerParams;
    return features.map((val, i) => safeDivide(val - center_![i], scale_![i], 0.0));
  }

  public async predictSell(currentTimestamp?: Date): Promise<SellPredictionResultV4> {
    const startTime = performance.now();
    const timestamp = currentTimestamp || new Date();

    if (!this.isModelLoaded || !this.canPredict(timestamp)) {
      return {
        probability: 0,
        prediction: false,
        confidence: 0,
        timestamp,
        predictionTimeMs: performance.now() - startTime
      };
    }
    
    const features = this.extractFeaturesV4(timestamp);

    if (!features) {
      return {
        probability: 0,
        prediction: false,
        confidence: 0,
        timestamp,
        predictionTimeMs: performance.now() - startTime,
      };
    }

    const featureVector = this.featuresToVector(features);
    const scaledFeatures = this.standardizeFeatures(featureVector);

    const predictionResult = await this.model.predict([scaledFeatures]); // CatBoost需要二维数组
    const rawProbability = predictionResult[0]; // 修正索引，使用[0]而不是[1]
    
    // 强制启用调试信息来诊断问题
    console.log(`🔍 卖出预测器调试:`);
    console.log(`   模型原始输出: ${rawProbability}`);
    console.log(`   输出类型: ${typeof rawProbability}`);
    console.log(`   是否在[0,1]范围内: ${rawProbability >= 0 && rawProbability <= 1}`);
    
    // 临时测试：总是应用sigmoid，看看是否能得到更合理的概率分布
    console.log(`   🔄 始终应用Sigmoid转换（诊断模式）`);
    let probability = this.sigmoid(rawProbability);
    console.log(`   ✅ Sigmoid后概率: ${probability}`);
    
    // 比较直接使用和Sigmoid后的结果
    console.log(`   🔍 比较: 原始=${rawProbability.toFixed(6)}, Sigmoid=${probability.toFixed(6)}`);
    
    // 检查是否有明显改善
    if (Math.abs(rawProbability - probability) > 0.1) {
        console.log(`   📊 Sigmoid对概率产生了显著影响`);
    }
    
    // 额外的异常检测
    if (probability < 0.001 || probability > 0.999) {
        console.log(`   ⚠️ 检测到极端概率值: ${probability}`);
        console.log(`   这可能表明特征计算或模型输出有问题`);
    }
    
    const prediction = probability >= this.threshold;
    const confidence = Math.abs(probability - 0.5) * 2;

    const endTime = performance.now();

    return {
      probability,
      prediction,
      confidence,
      timestamp,
      predictionTimeMs: endTime - startTime,
      features
    };
  }

  private sigmoid(x: number): number {
    return 1 / (1 + Math.exp(-x));
  }

  public canPredict(currentTimestamp?: Date): boolean {
    const timestamp = currentTimestamp || new Date();
    const sixtySecondsAgo = timestamp.getTime() - 65 * 1000;
    const hasRecentTx = this.transactionHistory.some(tx => tx.timestamp.getTime() >= sixtySecondsAgo);
    const hasRecentPrice = this.priceHistory.some(p => p.timestamp.getTime() >= sixtySecondsAgo);
    return hasRecentTx && hasRecentPrice;
  }

  public getQueueStatus(): { currentSize: number; maxSize: number; canPredict: boolean } {
    return {
      currentSize: this.transactionHistory.length,
      maxSize: this.maxHistorySize,
      canPredict: this.canPredict()
    };
  }
} 