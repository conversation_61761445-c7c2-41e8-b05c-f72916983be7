import * as catboost from 'catboost';
import * as fs from 'fs';
import * as path from 'path';
import { performance } from 'perf_hooks';

// Based on BUY_PREDICTOR_V4_COMPLETE_TECHNICAL_GUIDE.md

export interface TransactionDataV4 {
  timestamp: Date;
  transaction_type: 'buy' | 'sell';
  sol_amount: number;
  usd_amount: number; // V4 uses usd_amount directly
}

export interface PriceDataV4 {
  timestamp: Date;
  price: number;
}

export interface BuyFeaturesV4 {
  // All 48 features from the V4 documentation
  [key: string]: number;
}

export interface BuyPredictionResultV4 {
  probability: number;
  prediction: boolean;
  confidence: number;
  timestamp: Date;
  predictionTimeMs: number;
  features?: BuyFeaturesV4;
}

export interface ScalerParamsV4 {
  center_?: number[]; // RobustScaler
  scale_?: number[];
  feature_names: string[];
}

function safeDivide(numerator: number, denominator: number, defaultValue: number = 0.0): number {
  if (Math.abs(denominator) < 1e-9) {
      return defaultValue;
  }
  return numerator / denominator;
}

export class BuyPredictorV4 {
  private model: any;
  private modelPath: string;
  private scalerPath: string;
  private featuresPath: string;
  private scalerParams: ScalerParamsV4 | null = null;
  private featureNames: string[] = [];
  private threshold: number;
  private transactionHistory: TransactionDataV4[] = [];
  private priceHistory: PriceDataV4[] = [];
  private isModelLoaded: boolean = false;
  private maxHistorySize: number = 5000;

  constructor(
    modelPath: string = 'predictors/v4/buy_predictor_v4_relative_features_20250624_143400.cbm',
    scalerPath: string = 'predictors/v4/buy_predictor_v4_relative_features_scaler_20250624_143400.json',
    featuresPath: string = 'predictors/v4/buy_predictor_v4_relative_features_features_20250624_143400.json',
    threshold: number = 0.5
  ) {
    this.modelPath = path.resolve(modelPath);
    this.scalerPath = path.resolve(scalerPath);
    this.featuresPath = path.resolve(featuresPath);
    this.threshold = threshold;

    console.log(`🤖 BuyPredictorV4 initializing: Threshold=${(threshold * 100).toFixed(1)}%`);
    this.initializeModel();
  }

  private initializeModel(): void {
    try {
      if (!fs.existsSync(this.modelPath)) {
        console.warn(`Buy model V4 file not found: ${this.modelPath}`);
        return;
      }
      this.model = new catboost.Model();
      this.model.loadModel(this.modelPath);

      if (fs.existsSync(this.scalerPath)) {
        const scalerData = fs.readFileSync(this.scalerPath, 'utf8');
        this.scalerParams = JSON.parse(scalerData);
      } else {
        console.warn(`Scaler file not found: ${this.scalerPath}`);
      }

      if (fs.existsSync(this.featuresPath)) {
        const featuresData = fs.readFileSync(this.featuresPath, 'utf8');
        this.featureNames = JSON.parse(featuresData);
      } else {
        console.warn(`Features file not found: ${this.featuresPath}`);
      }

      this.isModelLoaded = true;
      console.log(`✅ BuyPredictorV4 model loaded successfully (Features: ${this.featureNames.length})`);
    } catch (error) {
      console.error(`❌ Failed to load BuyPredictorV4 model: ${error}`);
      this.isModelLoaded = false;
    }
  }

  public addTransaction(transaction: TransactionDataV4): void {
    this.transactionHistory.push(transaction);
    if (this.transactionHistory.length > this.maxHistorySize) {
      this.transactionHistory.shift();
    }
  }

  public addTransactionBatch(transactions: TransactionDataV4[]): void {
    this.transactionHistory.push(...transactions);
    if (this.transactionHistory.length > this.maxHistorySize) {
        this.transactionHistory = this.transactionHistory.slice(-this.maxHistorySize);
    }
  }

  public addPriceData(price: PriceDataV4): void {
    this.priceHistory.push(price);
    if (this.priceHistory.length > this.maxHistorySize) {
      this.priceHistory.shift();
    }
  }

  public addPriceDataBatch(prices: PriceDataV4[]): void {
    this.priceHistory.push(...prices);
    if (this.priceHistory.length > this.maxHistorySize) {
        this.priceHistory = this.priceHistory.slice(-this.maxHistorySize);
    }
  }

  private extractFeaturesV4(currentTimestamp: Date): BuyFeaturesV4 | null {
    if (this.transactionHistory.length === 0) {
        return null;
    }

    const sixtySecondsAgo = currentTimestamp.getTime() - 60 * 1000;
    const recentHistory = this.transactionHistory.filter(tx => tx.timestamp.getTime() >= sixtySecondsAgo && tx.timestamp <= currentTimestamp);
    const recentPrices = this.priceHistory.filter(p => p.timestamp.getTime() >= sixtySecondsAgo && p.timestamp <= currentTimestamp);
    
    if (recentHistory.length === 0) {
        return null;
    }

    const features: BuyFeaturesV4 = {};
    const windowSummary: { [key: number]: any } = {};

    const windows = [5, 10, 30, 60];

    for (const windowSize of windows) {
        const windowStart = new Date(currentTimestamp.getTime() - windowSize * 1000);
        const windowData = recentHistory.filter(tx => tx.timestamp >= windowStart);
        const windowPriceData = recentPrices.filter(p => p.timestamp >= windowStart);

        if (windowData.length === 0) {
            windowSummary[windowSize] = { buy_volume: 0, sell_volume: 0, buy_count: 0, total_count: 0, sol_amounts: [], prices: [], total_volume: 0 };
            continue;
        }

        const buyTxs = windowData.filter(tx => tx.transaction_type === 'buy');
        const sellTxs = windowData.filter(tx => tx.transaction_type === 'sell');
        
        const buy_volume = buyTxs.reduce((sum, tx) => sum + tx.sol_amount, 0);
        const sell_volume = sellTxs.reduce((sum, tx) => sum + tx.sol_amount, 0);

        const sol_amounts = windowData.map(tx => tx.sol_amount);
        // 🔥 修复：使用真实价格数据而不是SOL数量
        const prices = windowPriceData.length > 0 ? windowPriceData.map(p => p.price) : [0];

        windowSummary[windowSize] = {
            buy_volume,
            sell_volume,
            buy_count: buyTxs.length,
            total_count: windowData.length,
            sol_amounts,
            prices,
            total_volume: sol_amounts.reduce((sum, v) => sum + v, 0)
        };
    }

    for (const windowSize of windows) {
        const summary = windowSummary[windowSize];
        const { buy_volume, sell_volume, buy_count, total_count, sol_amounts, prices, total_volume } = summary;

        // Category 1: Buy/Sell Ratio
        features[`buy_sell_ratio_${windowSize}s`] = safeDivide(buy_volume, sell_volume, buy_volume > 0 ? 10.0 : 0.0);
        features[`buy_transaction_ratio_${windowSize}s`] = safeDivide(buy_count, total_count);

        // Category 2: Volume Proportions
        const volume_mean = total_count > 0 ? total_volume / total_count : 0;
        if (sol_amounts.length > 1) {
            const volume_std = Math.sqrt(sol_amounts.map(x => Math.pow(x - volume_mean, 2)).reduce((a, b) => a + b) / sol_amounts.length);
            features[`volume_std_ratio_${windowSize}s`] = safeDivide(volume_std, volume_mean);
        } else {
            features[`volume_std_ratio_${windowSize}s`] = 0.0;
        }
        features[`volume_max_ratio_${windowSize}s`] = safeDivide(Math.max(...sol_amounts, 0), volume_mean, 1.0);
        const sorted_volumes = [...sol_amounts].sort((a,b) => a-b);
        const volume_median = sorted_volumes.length > 0 ? sorted_volumes[Math.floor(sorted_volumes.length / 2)] : 0;
        features[`volume_median_ratio_${windowSize}s`] = safeDivide(volume_median, volume_mean, 1.0);

        // Category 3: Relative Price Changes
        if (prices.length > 1) {
            const first_price = prices[0];
            const last_price = prices[prices.length - 1];
            features[`price_change_ratio_${windowSize}s`] = safeDivide(last_price - first_price, first_price);
            
            const price_mean = prices.reduce((a,b) => a+b, 0) / prices.length;
            const price_std = Math.sqrt(prices.map(x => Math.pow(x - price_mean, 2)).reduce((a,b) => a+b) / prices.length);
            features[`price_volatility_ratio_${windowSize}s`] = safeDivide(price_std, price_mean);
            
            const price_max = Math.max(...prices);
            const price_min = Math.min(...prices);
            features[`price_range_ratio_${windowSize}s`] = safeDivide(price_max - price_min, price_mean);
        } else {
            features[`price_change_ratio_${windowSize}s`] = 0.0;
            features[`price_volatility_ratio_${windowSize}s`] = 0.0;
            features[`price_range_ratio_${windowSize}s`] = 0.0;
        }

        // Category 4: Transaction Frequency
        const windowStartTime = Date.now() - (windowSize * 1000);
        const windowDataForFreq = this.transactionHistory.filter(tx => tx.timestamp.getTime() >= windowStartTime);
        if (windowDataForFreq.length > 1) {
            const time_diffs = [];
            for (let i = 1; i < windowDataForFreq.length; i++) {
                time_diffs.push(windowDataForFreq[i].timestamp.getTime() - windowDataForFreq[i-1].timestamp.getTime());
            }
            const avg_time_diff = time_diffs.reduce((a,b) => a+b, 0) / time_diffs.length;
            features[`transaction_frequency_${windowSize}s`] = safeDivide(1000, avg_time_diff); // transactions per second
        } else {
            features[`transaction_frequency_${windowSize}s`] = 0.0;
        }

        // Category 5: Volume Momentum
        if (windowDataForFreq.length >= 2) {
            const half_point = Math.floor(windowDataForFreq.length / 2);
            const first_half_volume = windowDataForFreq.slice(0, half_point).reduce((sum, tx) => sum + tx.sol_amount, 0);
            const second_half_volume = windowDataForFreq.slice(half_point).reduce((sum, tx) => sum + tx.sol_amount, 0);
            features[`volume_momentum_${windowSize}s`] = safeDivide(second_half_volume - first_half_volume, first_half_volume + second_half_volume);
        } else {
            features[`volume_momentum_${windowSize}s`] = 0.0;
        }
    }

    // 添加缺失的跨时间窗口比较特征 (16个)
    const crossWindowSummary: any = {};
    for (const windowSize of windows) {
        const windowStartTime = currentTimestamp.getTime() - (windowSize * 1000);
        const windowData = this.transactionHistory.filter(tx => tx.timestamp.getTime() >= windowStartTime && tx.timestamp <= currentTimestamp);
        
        const totalVolume = windowData.reduce((sum, tx) => sum + tx.sol_amount, 0);
        const buyVolume = windowData.filter(tx => tx.transaction_type === 'buy').reduce((sum, tx) => sum + tx.sol_amount, 0);
        const buyCount = windowData.filter(tx => tx.transaction_type === 'buy').length;
        const totalCount = windowData.length;
        
        crossWindowSummary[windowSize] = {
            totalVolume,
            buyVolume,
            buyRatio: totalCount > 0 ? buyCount / totalCount : 0,
            buyVolumeRatio: totalVolume > 0 ? buyVolume / totalVolume : 0
        };
    }
    
    // 特征34-36: 交易量跨窗口比较
    features['volume_5s_vs_10s_ratio'] = safeDivide(crossWindowSummary[5].totalVolume, crossWindowSummary[10].totalVolume);
    features['volume_10s_vs_30s_ratio'] = safeDivide(crossWindowSummary[10].totalVolume, crossWindowSummary[30].totalVolume);
    features['volume_30s_vs_60s_ratio'] = safeDivide(crossWindowSummary[30].totalVolume, crossWindowSummary[60].totalVolume);
    
    // 特征37: 买入比例跨窗口比较
    features['buy_ratio_5s_vs_60s'] = safeDivide(crossWindowSummary[5].buyRatio, crossWindowSummary[60].buyRatio);
    
    // 特征38: 价格趋势跨窗口比较
    features['price_trend_5s_vs_30s'] = safeDivide(features['price_change_ratio_5s'] || 0, features['price_change_ratio_30s'] || 0);
    
    // 特征39: 波动率跨窗口比较
    features['volatility_5s_vs_60s'] = safeDivide(features['price_volatility_ratio_5s'] || 0, features['price_volatility_ratio_60s'] || 0);
    
    // 特征40: 交易量加速度比例
    const shortAccel = safeDivide(crossWindowSummary[5].totalVolume - crossWindowSummary[10].totalVolume, crossWindowSummary[10].totalVolume);
    const longAccel = safeDivide(crossWindowSummary[30].totalVolume - crossWindowSummary[60].totalVolume, crossWindowSummary[60].totalVolume);
    features['volume_acceleration_ratio'] = safeDivide(shortAccel, longAccel);
    
    // 特征41: 动量比例
    const shortMomentum = (features['price_change_ratio_5s'] || 0) * crossWindowSummary[5].totalVolume;
    const longMomentum = (features['price_change_ratio_60s'] || 0) * crossWindowSummary[60].totalVolume;
    features['momentum_ratio'] = safeDivide(shortMomentum, longMomentum);
    
    // 特征42: 买入压力比例
    const recentBuyVolume = crossWindowSummary[5].totalVolume * crossWindowSummary[5].buyRatio;
    const historicalBuyVolume = crossWindowSummary[60].totalVolume * crossWindowSummary[60].buyRatio;
    features['buy_pressure_ratio'] = safeDivide(recentBuyVolume, historicalBuyVolume);
    
    // 特征43: 交易量突发比例
    features['volume_burst_ratio'] = safeDivide(features['volume_max_ratio_5s'] || 0, features['volume_max_ratio_60s'] || 0);
    
    // 特征44: 价格支撑比例
    if ((features['price_change_ratio_5s'] || 0) < 0) {
        features['price_support_ratio'] = safeDivide(crossWindowSummary[5].buyRatio, crossWindowSummary[60].buyRatio);
    } else {
        features['price_support_ratio'] = 1.0;
    }
    
    // 特征45: 累积比例
    features['accumulation_ratio'] = safeDivide(crossWindowSummary[30].buyRatio, crossWindowSummary[5].buyRatio);
    
    // 特征46: 买入强度比例
    features['buy_intensity_ratio'] = safeDivide(features['buy_sell_ratio_5s'] || 0, features['buy_sell_ratio_60s'] || 0);
    
    // 特征47: 市场深度比例
    features['market_depth_ratio'] = safeDivide(features['volume_std_ratio_5s'] || 0, features['volume_std_ratio_60s'] || 0);
    
    // 特征48: 入场机会比例
    features['entry_opportunity_ratio'] = (features['price_volatility_ratio_5s'] || 0) * (features['buy_transaction_ratio_5s'] || 0);
    
    // 特征49: 风险收益比
    const reward = Math.abs(features['price_change_ratio_5s'] || 0);
    const risk = features['price_volatility_ratio_5s'] || 0;
    features['risk_reward_ratio'] = safeDivide(reward, risk);

    return features;
  }

  private featuresToVector(features: BuyFeaturesV4): number[] {
    return this.featureNames.map(name => features[name] || 0.0);
  }

  private standardizeFeatures(features: number[]): number[] {
    if (!this.scalerParams || !this.scalerParams.center_ || !this.scalerParams.scale_) {
        return features;
    }

    const standardized = features.map((value, index) => {
        const center = this.scalerParams!.center_![index] || 0;
        const scale = this.scalerParams!.scale_![index] || 1;
        
        if (Math.abs(scale) < 1e-9) {
            return 0.0;
        }
        
        return (value - center) / scale;
    });

    return standardized;
  }

  private sigmoid(x: number): number {
    return 1 / (1 + Math.exp(-x));
  }

  public async predictBuy(currentTimestamp?: Date): Promise<BuyPredictionResultV4> {
    const startTime = performance.now();
    const timestamp = currentTimestamp || new Date();

    if (!this.isModelLoaded) {
        return {
            probability: 0.0,
            prediction: false,
            confidence: 0.0,
            timestamp,
            predictionTimeMs: performance.now() - startTime
        };
    }

    const features = this.extractFeaturesV4(timestamp);
    if (!features) {
        return {
            probability: 0.0,
            prediction: false,
            confidence: 0.0,
            timestamp,
            predictionTimeMs: performance.now() - startTime
        };
    }

    try {
        const featureVector = this.featuresToVector(features);
        const standardizedFeatures = this.standardizeFeatures(featureVector);
        
        // 调试特征向量
        console.log(`📈 特征向量调试:`);
        console.log(`   原始特征数量: ${featureVector.length}`);
        console.log(`   标准化特征数量: ${standardizedFeatures.length}`);
        
        // 检查是否有异常值
        const nanCount = standardizedFeatures.filter(f => isNaN(f) || !isFinite(f)).length;
        const zeroCount = standardizedFeatures.filter(f => f === 0).length;
        const extremeCount = standardizedFeatures.filter(f => Math.abs(f) > 10).length;
        
        console.log(`   NaN/Infinite特征数: ${nanCount}`);
        console.log(`   零值特征数: ${zeroCount}`);
        console.log(`   极端值特征数 (>10): ${extremeCount}`);
        
        if (nanCount > 0) {
            console.log(`   ⚠️ 检测到无效特征值，可能影响预测精度`);
        }
        
        // 显示前5个特征值作为样本
        console.log(`   前5个标准化特征: [${standardizedFeatures.slice(0, 5).map(f => f.toFixed(3)).join(', ')}]`);
        
        const predictions = this.model.predict([standardizedFeatures]);
        const rawProbability = predictions[0];
        
        // 强制启用调试信息来诊断问题
        console.log(`🔍 买入预测器调试:`);
        console.log(`   模型原始输出: ${rawProbability}`);
        console.log(`   输出类型: ${typeof rawProbability}`);
        console.log(`   是否在[0,1]范围内: ${rawProbability >= 0 && rawProbability <= 1}`);
        
        // 临时测试：总是应用sigmoid，看看是否能得到更合理的概率分布
        console.log(`   🔄 始终应用Sigmoid转换（诊断模式）`);
        let probability = this.sigmoid(rawProbability);
        console.log(`   ✅ Sigmoid后概率: ${probability}`);
        
        // 比较直接使用和Sigmoid后的结果
        console.log(`   🔍 比较: 原始=${rawProbability.toFixed(6)}, Sigmoid=${probability.toFixed(6)}`);
        
        // 检查是否有明显改善
        if (Math.abs(rawProbability - probability) > 0.1) {
            console.log(`   📊 Sigmoid对概率产生了显著影响`);
        }
        
        // 额外的异常检测
        if (probability < 0.001 || probability > 0.999) {
            console.log(`   ⚠️ 检测到极端概率值: ${probability}`);
            console.log(`   这可能表明特征计算或模型输出有问题`);
        }
        
        const prediction = probability >= this.threshold;
        const confidence = Math.abs(probability - 0.5) * 2;

        return {
            probability,
            prediction,
            confidence,
            timestamp,
            predictionTimeMs: performance.now() - startTime,
            features
        };
    } catch (error) {
        console.error('Error in BuyPredictorV4 prediction:', error);
        return {
            probability: 0.0,
            prediction: false,
            confidence: 0.0,
            timestamp,
            predictionTimeMs: performance.now() - startTime
        };
    }
  }

  public canPredict(currentTimestamp?: Date): boolean {
    const timestamp = currentTimestamp || new Date();
    const sixtySecondsAgo = timestamp.getTime() - 60 * 1000;
    const recentTx = this.transactionHistory.find(tx => tx.timestamp.getTime() >= sixtySecondsAgo);
    const recentPrice = this.priceHistory.find(p => p.timestamp.getTime() >= sixtySecondsAgo);
    return !!recentTx && !!recentPrice;
  }

  public getQueueStatus(): { currentSize: number; maxSize: number; canPredict: boolean } {
    return {
      currentSize: this.transactionHistory.length,
      maxSize: this.maxHistorySize,
      canPredict: this.canPredict()
    };
  }
} 