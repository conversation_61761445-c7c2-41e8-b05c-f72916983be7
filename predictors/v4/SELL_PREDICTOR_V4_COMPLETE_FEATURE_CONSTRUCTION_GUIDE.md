# Sell Predictor V4 - 完整特征构建指南

## 文档目标读者
本文档面向：
- **模型集成开发者** - 需要在其他系统中重现特征计算
- **算法工程师** - 需要理解和优化特征工程
- **数据科学家** - 需要分析和改进特征设计
- **系统架构师** - 需要评估计算复杂度和性能要求

## 数据输入格式

### 必需的DataFrame结构
```python
# 交易数据DataFrame
transaction_df = pd.DataFrame({
    'timestamp': pd.datetime,      # 交易时间戳，必须已排序
    'transaction_type': str,       # 'buy' 或 'sell'
    'sol_amount': float,          # SOL金额，必须 > 0
    'wallet_address': str         # 钱包地址
})

# 价格数据DataFrame  
price_df = pd.DataFrame({
    'timestamp': pd.datetime,      # 价格时间戳，必须已排序
    'price': float                # 代币价格，必须 > 0
})
```

### 数据预处理要求
```python
# 1. 时间戳排序（必须）
transaction_df = transaction_df.sort_values('timestamp').reset_index(drop=True)
price_df = price_df.sort_values('timestamp').reset_index(drop=True)

# 2. 数据清洗
transaction_df = transaction_df[transaction_df['sol_amount'] > 0]
price_df = price_df[price_df['price'] > 0]
transaction_df = transaction_df.dropna()
price_df = price_df.dropna()

# 3. 最小数据量要求
# 至少需要60秒的历史数据才能计算所有特征
min_timestamp = target_timestamp - timedelta(seconds=60)
transaction_df = transaction_df[transaction_df['timestamp'] >= min_timestamp]
price_df = price_df[price_df['timestamp'] >= min_timestamp]
```

## 时间窗口定义

所有特征基于4个固定时间窗口：
- **5秒窗口**: 超短期信号，捕获瞬时市场变化
- **10秒窗口**: 短期趋势，平滑瞬时噪音
- **30秒窗口**: 中期模式，识别持续性行为
- **60秒窗口**: 长期背景，提供基准对比

### 时间窗口数据筛选
```python
def get_window_data(df, timestamp, window_seconds, buffer_seconds=5):
    """获取指定时间窗口内的数据，包含缓冲区"""
    end_time = pd.to_datetime(timestamp) - timedelta(seconds=buffer_seconds)
    start_time = end_time - timedelta(seconds=window_seconds)
    
    window_df = df[
        (df['timestamp'] >= start_time) & 
        (df['timestamp'] <= end_time)
    ].copy()
    
    return window_df
```

## 完整特征计算实现 (56个相对特征)

### 第一类：买卖比例特征 (8个)

#### 1.1 买入交易比例 (4个特征)
```python
def calculate_buy_ratio(transaction_df, timestamp, window_seconds):
    """
    计算买入交易笔数占总交易笔数的比例
    
    公式: buy_ratio = buy_count / total_count
    """
    window_data = get_window_data(transaction_df, timestamp, window_seconds)
    
    if len(window_data) == 0:
        return 0.0
    
    buy_count = len(window_data[window_data['transaction_type'] == 'buy'])
    total_count = len(window_data)
    
    return buy_count / total_count

# 计算所有时间窗口的买入比例
for window in [5, 10, 30, 60]:
    features[f'buy_ratio_{window}s'] = calculate_buy_ratio(transaction_df, timestamp, window)
```

#### 1.2 卖出交易比例 (4个特征)
```python
def calculate_sell_ratio(transaction_df, timestamp, window_seconds):
    """
    计算卖出交易笔数占总交易笔数的比例
    
    公式: sell_ratio = sell_count / total_count
    """
    window_data = get_window_data(transaction_df, timestamp, window_seconds)
    
    if len(window_data) == 0:
        return 0.0
    
    sell_count = len(window_data[window_data['transaction_type'] == 'sell'])
    total_count = len(window_data)
    
    return sell_count / total_count

# 计算所有时间窗口的卖出比例
for window in [5, 10, 30, 60]:
    features[f'sell_ratio_{window}s'] = calculate_sell_ratio(transaction_df, timestamp, window)
```

### 第二类：交易量比例特征 (12个)

#### 2.1 买入量比例 (4个特征)
```python
def calculate_buy_volume_ratio(transaction_df, timestamp, window_seconds):
    """
    计算买入交易量占总交易量的比例
    
    公式: buy_volume_ratio = buy_volume / total_volume
    """
    window_data = get_window_data(transaction_df, timestamp, window_seconds)
    
    if len(window_data) == 0:
        return 0.0
    
    buy_volume = window_data[window_data['transaction_type'] == 'buy']['sol_amount'].sum()
    total_volume = window_data['sol_amount'].sum()
    
    if total_volume == 0:
        return 0.0
    
    return buy_volume / total_volume

# 计算所有时间窗口的买入量比例
for window in [5, 10, 30, 60]:
    features[f'buy_volume_ratio_{window}s'] = calculate_buy_volume_ratio(transaction_df, timestamp, window)
```

#### 2.2 卖出量比例 (4个特征)
```python
def calculate_sell_volume_ratio(transaction_df, timestamp, window_seconds):
    """
    计算卖出交易量占总交易量的比例
    
    公式: sell_volume_ratio = sell_volume / total_volume
    """
    window_data = get_window_data(transaction_df, timestamp, window_seconds)
    
    if len(window_data) == 0:
        return 0.0
    
    sell_volume = window_data[window_data['transaction_type'] == 'sell']['sol_amount'].sum()
    total_volume = window_data['sol_amount'].sum()
    
    if total_volume == 0:
        return 0.0
    
    return sell_volume / total_volume

# 计算所有时间窗口的卖出量比例
for window in [5, 10, 30, 60]:
    features[f'sell_volume_ratio_{window}s'] = calculate_sell_volume_ratio(transaction_df, timestamp, window)
```

#### 2.3 买卖量比值 (4个特征)
```python
def calculate_buy_sell_volume_ratio(transaction_df, timestamp, window_seconds):
    """
    计算买入量与卖出量的比值
    
    公式: buy_sell_volume_ratio = buy_volume / sell_volume
    特殊处理: 如果sell_volume为0，返回10.0
    """
    window_data = get_window_data(transaction_df, timestamp, window_seconds)
    
    if len(window_data) == 0:
        return 0.0
    
    buy_volume = window_data[window_data['transaction_type'] == 'buy']['sol_amount'].sum()
    sell_volume = window_data[window_data['transaction_type'] == 'sell']['sol_amount'].sum()
    
    if sell_volume == 0:
        return 10.0 if buy_volume > 0 else 0.0
    
    return buy_volume / sell_volume

# 计算所有时间窗口的买卖量比值
for window in [5, 10, 30, 60]:
    features[f'buy_sell_volume_ratio_{window}s'] = calculate_buy_sell_volume_ratio(transaction_df, timestamp, window)
```

### 第三类：价格相对变化特征 (12个)

#### 3.1 价格变化率 (4个特征)
```python
def calculate_price_change_ratio(price_df, timestamp, window_seconds):
    """
    计算价格相对变化率
    
    公式: price_change_ratio = (price_end - price_start) / price_start
    """
    window_data = get_window_data(price_df, timestamp, window_seconds)
    
    if len(window_data) < 2:
        return 0.0
    
    price_start = window_data['price'].iloc[0]
    price_end = window_data['price'].iloc[-1]
    
    if price_start == 0:
        return 0.0
    
    return (price_end - price_start) / price_start

# 计算所有时间窗口的价格变化率
for window in [5, 10, 30, 60]:
    features[f'price_change_ratio_{window}s'] = calculate_price_change_ratio(price_df, timestamp, window)
```

#### 3.2 价格最大值比例 (4个特征)
```python
def calculate_price_max_ratio(price_df, timestamp, window_seconds):
    """
    计算价格最大值与平均值的比例
    
    公式: price_max_ratio = price_max / price_mean
    """
    window_data = get_window_data(price_df, timestamp, window_seconds)
    
    if len(window_data) == 0:
        return 0.0
    
    price_max = window_data['price'].max()
    price_mean = window_data['price'].mean()
    
    if price_mean == 0:
        return 0.0
    
    return price_max / price_mean

# 计算所有时间窗口的价格最大值比例
for window in [5, 10, 30, 60]:
    features[f'price_max_ratio_{window}s'] = calculate_price_max_ratio(price_df, timestamp, window)
```

#### 3.3 价格最小值比例 (4个特征)
```python
def calculate_price_min_ratio(price_df, timestamp, window_seconds):
    """
    计算价格最小值与平均值的比例
    
    公式: price_min_ratio = price_min / price_mean
    """
    window_data = get_window_data(price_df, timestamp, window_seconds)
    
    if len(window_data) == 0:
        return 0.0
    
    price_min = window_data['price'].min()
    price_mean = window_data['price'].mean()
    
    if price_mean == 0:
        return 0.0
    
    return price_min / price_mean

# 计算所有时间窗口的价格最小值比例
for window in [5, 10, 30, 60]:
    features[f'price_min_ratio_{window}s'] = calculate_price_min_ratio(price_df, timestamp, window)
```

### 第四类：波动率特征 (8个)

#### 4.1 交易量波动率 (4个特征)
```python
def calculate_volume_volatility(transaction_df, timestamp, window_seconds):
    """
    计算交易量波动率（变异系数）
    
    公式: volume_volatility = volume_std / volume_mean
    """
    window_data = get_window_data(transaction_df, timestamp, window_seconds)
    
    if len(window_data) < 2:
        return 0.0
    
    volumes = window_data['sol_amount']
    volume_std = volumes.std()
    volume_mean = volumes.mean()
    
    if volume_mean == 0:
        return 0.0
    
    return volume_std / volume_mean

# 计算所有时间窗口的交易量波动率
for window in [5, 10, 30, 60]:
    features[f'volume_volatility_{window}s'] = calculate_volume_volatility(transaction_df, timestamp, window)
```

#### 4.2 价格波动率 (4个特征)
```python
def calculate_price_volatility(price_df, timestamp, window_seconds):
    """
    计算价格波动率（变异系数）
    
    公式: price_volatility = price_std / price_mean
    """
    window_data = get_window_data(price_df, timestamp, window_seconds)
    
    if len(window_data) < 2:
        return 0.0
    
    prices = window_data['price']
    price_std = prices.std()
    price_mean = prices.mean()
    
    if price_mean == 0:
        return 0.0
    
    return price_std / price_mean

# 计算所有时间窗口的价格波动率
for window in [5, 10, 30, 60]:
    features[f'price_volatility_{window}s'] = calculate_price_volatility(price_df, timestamp, window)
```

### 第五类：趋势特征 (8个)

#### 5.1 交易量趋势 (4个特征)
```python
def calculate_volume_trend(transaction_df, timestamp, window_seconds):
    """
    计算交易量趋势（线性回归斜率归一化）
    
    公式: volume_trend = slope / volume_range
    """
    window_data = get_window_data(transaction_df, timestamp, window_seconds)
    
    if len(window_data) < 3:
        return 0.0
    
    # 按时间排序并获取交易量
    volumes = window_data['sol_amount'].values
    x = np.arange(len(volumes))
    
    # 计算线性回归斜率
    if len(x) > 1:
        slope = np.polyfit(x, volumes, 1)[0]
        volume_range = volumes.max() - volumes.min()
        
        if volume_range == 0:
            return 0.0
        
        return slope / volume_range
    
    return 0.0

# 计算所有时间窗口的交易量趋势
for window in [5, 10, 30, 60]:
    features[f'volume_trend_{window}s'] = calculate_volume_trend(transaction_df, timestamp, window)
```

#### 5.2 价格趋势 (4个特征)
```python
def calculate_price_trend(price_df, timestamp, window_seconds):
    """
    计算价格趋势（线性回归斜率归一化）
    
    公式: price_trend = slope / price_range
    """
    window_data = get_window_data(price_df, timestamp, window_seconds)
    
    if len(window_data) < 3:
        return 0.0
    
    # 按时间排序并获取价格
    prices = window_data['price'].values
    x = np.arange(len(prices))
    
    # 计算线性回归斜率
    if len(x) > 1:
        slope = np.polyfit(x, prices, 1)[0]
        price_range = prices.max() - prices.min()
        
        if price_range == 0:
            return 0.0
        
        return slope / price_range
    
    return 0.0

# 计算所有时间窗口的价格趋势
for window in [5, 10, 30, 60]:
    features[f'price_trend_{window}s'] = calculate_price_trend(price_df, timestamp, window)
```

### 第六类：加速度特征 (8个)

#### 6.1 交易量变化率 (4个特征)
```python
def calculate_volume_change_ratio(transaction_df, timestamp, window_seconds):
    """
    计算交易量变化率（后半段vs前半段）
    
    公式: volume_change_ratio = (second_half_avg - first_half_avg) / first_half_avg
    """
    window_data = get_window_data(transaction_df, timestamp, window_seconds)
    
    if len(window_data) < 4:
        return 0.0
    
    # 分割为前后两半
    mid_point = len(window_data) // 2
    first_half = window_data.iloc[:mid_point]['sol_amount']
    second_half = window_data.iloc[mid_point:]['sol_amount']
    
    first_half_avg = first_half.mean()
    second_half_avg = second_half.mean()
    
    if first_half_avg == 0:
        return 1.0 if second_half_avg > 0 else 0.0
    
    return (second_half_avg - first_half_avg) / first_half_avg

# 计算所有时间窗口的交易量变化率
for window in [5, 10, 30, 60]:
    features[f'volume_change_ratio_{window}s'] = calculate_volume_change_ratio(transaction_df, timestamp, window)
```

#### 6.2 价格加速度 (4个特征)
```python
def calculate_price_acceleration(price_df, timestamp, window_seconds):
    """
    计算价格加速度（二阶导数归一化）
    
    公式: price_acceleration = second_derivative / price_mean
    """
    window_data = get_window_data(price_df, timestamp, window_seconds)
    
    if len(window_data) < 5:
        return 0.0
    
    prices = window_data['price'].values
    
    # 计算一阶差分（速度）
    velocity = np.diff(prices)
    
    # 计算二阶差分（加速度）
    if len(velocity) > 1:
        acceleration = np.diff(velocity)
        avg_acceleration = np.mean(acceleration)
        price_mean = np.mean(prices)
        
        if price_mean == 0:
            return 0.0
        
        return avg_acceleration / price_mean
    
    return 0.0

# 计算所有时间窗口的价格加速度
for window in [5, 10, 30, 60]:
    features[f'price_acceleration_{window}s'] = calculate_price_acceleration(price_df, timestamp, window)
```

### 第七类：跨时间窗口比较特征 (4个)

#### 7.1 短期vs长期比较
```python
def calculate_cross_window_features(features):
    """
    计算跨时间窗口比较特征
    """
    cross_features = {}
    
    # 短期vs长期交易量比较
    if features.get('buy_volume_ratio_5s', 0) > 0 and features.get('buy_volume_ratio_60s', 0) > 0:
        cross_features['buy_volume_5s_vs_60s'] = features['buy_volume_ratio_5s'] / features['buy_volume_ratio_60s']
    else:
        cross_features['buy_volume_5s_vs_60s'] = 0.0
    
    # 短期vs中期波动率比较
    if features.get('volume_volatility_5s', 0) > 0 and features.get('volume_volatility_30s', 0) > 0:
        cross_features['volatility_5s_vs_30s'] = features['volume_volatility_5s'] / features['volume_volatility_30s']
    else:
        cross_features['volatility_5s_vs_30s'] = 0.0
    
    # 价格趋势一致性
    price_trends = [features.get(f'price_trend_{w}s', 0) for w in [5, 10, 30, 60]]
    if len(price_trends) > 1:
        signs = [1 if x > 0 else -1 if x < 0 else 0 for x in price_trends]
        cross_features['price_trend_consistency'] = abs(sum(signs)) / len(signs)
    else:
        cross_features['price_trend_consistency'] = 0.0
    
    # 市场活跃度加速
    volume_ratios = [features.get(f'buy_volume_ratio_{w}s', 0) for w in [5, 10, 30, 60]]
    if len(volume_ratios) >= 2:
        cross_features['market_acceleration'] = (volume_ratios[0] + volume_ratios[1]) / 2 - (volume_ratios[2] + volume_ratios[3]) / 2
    else:
        cross_features['market_acceleration'] = 0.0
    
    return cross_features
```

## 完整特征提取类实现

```python
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any
import logging

class SellPredictorV4FeatureConstructor:
    """
    Sell Predictor V4 特征构建器
    实现56个相对特征的完整计算逻辑
    """
    
    def __init__(self):
        self.window_sizes = [5, 10, 30, 60]  # 时间窗口（秒）
        self.buffer_seconds = 5  # 缓冲时间（秒）
        self.feature_names = self._get_expected_feature_names()
        
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _get_expected_feature_names(self) -> List[str]:
        """获取所有期望的特征名称"""
        features = []
        
        # 第一类：买卖比例特征 (8个)
        for window in self.window_sizes:
            features.extend([
                f'buy_ratio_{window}s',
                f'sell_ratio_{window}s'
            ])
        
        # 第二类：交易量比例特征 (12个)
        for window in self.window_sizes:
            features.extend([
                f'buy_volume_ratio_{window}s',
                f'sell_volume_ratio_{window}s',
                f'buy_sell_volume_ratio_{window}s'
            ])
        
        # 第三类：价格相对变化特征 (12个)
        for window in self.window_sizes:
            features.extend([
                f'price_change_ratio_{window}s',
                f'price_max_ratio_{window}s',
                f'price_min_ratio_{window}s'
            ])
        
        # 第四类：波动率特征 (8个)
        for window in self.window_sizes:
            features.extend([
                f'volume_volatility_{window}s',
                f'price_volatility_{window}s'
            ])
        
        # 第五类：趋势特征 (8个)
        for window in self.window_sizes:
            features.extend([
                f'volume_trend_{window}s',
                f'price_trend_{window}s'
            ])
        
        # 第六类：加速度特征 (8个)
        for window in self.window_sizes:
            features.extend([
                f'volume_change_ratio_{window}s',
                f'price_acceleration_{window}s'
            ])
        
        # 第七类：跨时间窗口比较特征 (4个)
        features.extend([
            'buy_volume_5s_vs_60s',
            'volatility_5s_vs_30s',
            'price_trend_consistency',
            'market_acceleration'
        ])
        
        return features
    
    def extract_all_relative_features(self, transaction_df: pd.DataFrame, 
                                    price_df: pd.DataFrame, 
                                    timestamp: datetime) -> Dict[str, float]:
        """
        提取所有56个相对特征
        
        Args:
            transaction_df: 交易数据DataFrame
            price_df: 价格数据DataFrame  
            timestamp: 目标时间戳
        
        Returns:
            Dict[str, float]: 包含56个特征的字典
        """
        try:
            # 验证输入数据
            self._validate_input_data(transaction_df, price_df)
            
            # 预处理数据
            processed_transaction_df = self._preprocess_data(transaction_df, timestamp)
            processed_price_df = self._preprocess_data(price_df, timestamp)
            
            features = {}
            
            # 计算各类特征
            for window in self.window_sizes:
                # 获取窗口数据
                window_transaction_data = self._get_window_data(processed_transaction_df, timestamp, window)
                window_price_data = self._get_window_data(processed_price_df, timestamp, window)
                
                # 第一类：买卖比例特征
                features.update(self._calculate_buy_sell_ratios(window_transaction_data, window))
                
                # 第二类：交易量比例特征
                features.update(self._calculate_volume_ratios(window_transaction_data, window))
                
                # 第三类：价格相对变化特征
                features.update(self._calculate_price_changes(window_price_data, window))
                
                # 第四类：波动率特征
                features.update(self._calculate_volatilities(window_transaction_data, window_price_data, window))
                
                # 第五类：趋势特征
                features.update(self._calculate_trends(window_transaction_data, window_price_data, window))
                
                # 第六类：加速度特征
                features.update(self._calculate_accelerations(window_transaction_data, window_price_data, window))
            
            # 第七类：跨时间窗口比较特征
            features.update(self._calculate_cross_window_features(features))
            
            # 验证特征完整性
            self._validate_features(features)
            
            return features
            
        except Exception as e:
            self.logger.error(f"特征提取失败: {str(e)}")
            # 返回默认特征值
            return {name: 0.0 for name in self.feature_names}
    
    def _validate_input_data(self, transaction_df: pd.DataFrame, price_df: pd.DataFrame):
        """验证输入数据格式"""
        # 验证交易数据
        required_transaction_columns = ['timestamp', 'transaction_type', 'sol_amount']
        for col in required_transaction_columns:
            if col not in transaction_df.columns:
                raise ValueError(f"交易数据缺少必需列: {col}")
        
        # 验证价格数据
        required_price_columns = ['timestamp', 'price']
        for col in required_price_columns:
            if col not in price_df.columns:
                raise ValueError(f"价格数据缺少必需列: {col}")
        
        # 验证数据类型
        if not pd.api.types.is_datetime64_any_dtype(transaction_df['timestamp']):
            raise ValueError("交易数据timestamp列必须是datetime类型")
        
        if not pd.api.types.is_datetime64_any_dtype(price_df['timestamp']):
            raise ValueError("价格数据timestamp列必须是datetime类型")
    
    def _preprocess_data(self, df: pd.DataFrame, timestamp: datetime) -> pd.DataFrame:
        """预处理数据"""
        # 确保时间戳排序
        df = df.sort_values('timestamp').reset_index(drop=True)
        
        # 过滤时间范围（保留足够的历史数据）
        min_timestamp = pd.to_datetime(timestamp) - timedelta(seconds=max(self.window_sizes) + self.buffer_seconds)
        df = df[df['timestamp'] >= min_timestamp].copy()
        
        return df
    
    def _get_window_data(self, df: pd.DataFrame, timestamp: datetime, window_seconds: int) -> pd.DataFrame:
        """获取指定时间窗口内的数据"""
        end_time = pd.to_datetime(timestamp) - timedelta(seconds=self.buffer_seconds)
        start_time = end_time - timedelta(seconds=window_seconds)
        
        return df[(df['timestamp'] >= start_time) & (df['timestamp'] <= end_time)].copy()
    
    def _validate_features(self, features: Dict[str, float]):
        """验证特征完整性"""
        missing_features = set(self.feature_names) - set(features.keys())
        if missing_features:
            self.logger.warning(f"缺少特征: {missing_features}")
            # 补充缺失特征
            for feature in missing_features:
                features[feature] = 0.0
        
        # 检查特征值是否有效
        for name, value in features.items():
            if not isinstance(value, (int, float)) or np.isnan(value) or np.isinf(value):
                self.logger.warning(f"特征值异常: {name} = {value}, 设置为0.0")
                features[name] = 0.0

    # 实现各类特征计算方法...
    def _calculate_buy_sell_ratios(self, window_data: pd.DataFrame, window: int) -> Dict[str, float]:
        """计算买卖比例特征"""
        features = {}
        
        if len(window_data) == 0:
            features[f'buy_ratio_{window}s'] = 0.0
            features[f'sell_ratio_{window}s'] = 0.0
            return features
        
        buy_count = len(window_data[window_data['transaction_type'] == 'buy'])
        sell_count = len(window_data[window_data['transaction_type'] == 'sell'])
        total_count = len(window_data)
        
        features[f'buy_ratio_{window}s'] = buy_count / total_count
        features[f'sell_ratio_{window}s'] = sell_count / total_count
        
        return features
    
    # ... 其他特征计算方法的实现 ...

# 使用示例
def main():
    # 创建特征构建器
    constructor = SellPredictorV4FeatureConstructor()
    
    # 准备测试数据
    transaction_df = pd.DataFrame({
        'timestamp': pd.date_range('2024-01-01 10:00:00', periods=100, freq='1S'),
        'transaction_type': np.random.choice(['buy', 'sell'], 100),
        'sol_amount': np.random.uniform(0.1, 10.0, 100)
    })
    
    price_df = pd.DataFrame({
        'timestamp': pd.date_range('2024-01-01 10:00:00', periods=100, freq='1S'),
        'price': np.random.uniform(0.001, 0.01, 100)
    })
    
    # 提取特征
    target_timestamp = pd.to_datetime('2024-01-01 10:01:30')
    features = constructor.extract_all_relative_features(transaction_df, price_df, target_timestamp)
    
    print(f"成功提取 {len(features)} 个特征:")
    for name, value in features.items():
        print(f"  {name}: {value:.6f}")

if __name__ == "__main__":
    main()
```

## 特征重要性分析

根据sell predictor V4的训练结果，以下是Top 10重要特征：

1. **volume_volatility_5s** (6.01) - 5秒交易量波动率
2. **price_max_ratio_5s** (5.66) - 5秒价格最大值比例  
3. **price_volatility_5s** (4.01) - 5秒价格波动率
4. **price_trend_60s** (3.83) - 60秒价格趋势
5. **price_volatility_10s** (3.79) - 10秒价格波动率
6. **volume_change_ratio_60s** (3.65) - 60秒交易量变化率
7. **volume_volatility_60s** (3.43) - 60秒交易量波动率
8. **volume_change_ratio_30s** (3.30) - 30秒交易量变化率
9. **volume_volatility_10s** (3.20) - 10秒交易量波动率
10. **price_volatility_30s** (2.84) - 30秒价格波动率

## 性能优化建议

### 1. 计算优化
- 使用向量化操作替代循环
- 缓存中间计算结果
- 并行计算多个时间窗口

### 2. 内存优化
- 只保留必要的历史数据
- 使用数据类型优化（float32 vs float64）
- 及时释放不用的DataFrame

### 3. 实时计算
- 使用滑动窗口算法
- 增量更新特征值
- 预计算基准统计量

## 部署注意事项

1. **数据质量**: 确保交易类型识别准确
2. **时间同步**: 交易数据和价格数据时间戳对齐
3. **异常处理**: 处理数据缺失和异常值
4. **性能监控**: 监控特征计算时间和准确性
5. **模型更新**: 定期重新训练以适应市场变化

---

**文档版本**: 1.0  
**最后更新**: 2025-06-24  
**模型版本**: Sell Predictor V4 - Unified Relative Features 