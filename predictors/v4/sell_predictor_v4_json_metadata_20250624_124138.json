{"model_version": "V4_JSON_ONLY", "version_name": "Sell Predictor V4 - Pure JSON Format", "description": "Advanced sell prediction model using 56 relative features, all parameters in JSON format", "save_timestamp": "20250624_124138", "model_type": "CatBoost", "model_format": "CBM", "scaler_format": "JSON", "features_format": "JSON", "feature_engineering": "relative_values_only", "training_approach": "unified_multi_dataset", "feature_count": 56, "feature_type": "all_relative_values", "prediction_target": "sell", "datasets_combined": true, "train_test_split": "80/20", "performance_metrics": {"validation_auc": 0.9482, "validation_f1": 0.8462, "validation_precision": 0.8919, "validation_recall": 0.8049, "training_auc": 1.0, "training_f1": 1.0}, "deployment_ready": true, "production_notes": "Requires accurate transaction type identification", "compatibility": {"typescript_compatible": true, "python_compatible": true, "json_only": true, "no_pickle_dependencies": true}, "file_formats": {"model": "CBM (CatBoost Binary)", "scaler": "JSON (parameters only)", "features": "JSON (feature names list)", "metadata": "JSON (model information)"}, "scaler_info": {"scaler_type": "RobustScaler", "center_": [0.5446570972886762, 0.4553429027113237, 0.4963206674113614, 0.5036793325886386, 0.9853905035309269, 5.290848012940315e-05, 0.01059639917205742, -0.009868379855111978, 0.010838595283450833, 0.004688284348973261, 1.3491972622432562, 0.8895479112423734, 0.0, 0.0, 0.5420197740112994, 0.4579802259887006, 0.500471537314586, 0.49952846268541395, 1.0018879525948368, 0.0005074998339886551, 0.014291403236971367, -0.018327533128802975, 0.014397193458893842, 0.3544308007764361, 1.5395843044647828, 0.8455081142940886, 0.0006127641078536962, -0.0006529786463768167, 0.5444912559618442, 0.4555087440381558, 0.49782106819883776, 0.5021789318011622, 0.9913223414714305, 0.0018503739265211313, 0.03416993915309398, -0.03846995801326476, 0.021937395269180503, 0.08669254928072784, 1.7967752847177958, 0.8538658771093461, -0.00017965885232464393, -0.0003278953768370855, 0.5356767676767678, 0.46432323232323236, 0.4951270337761367, 0.5048729662238632, 0.9806962814493572, -0.005651426147760506, 0.044113130769296696, -0.056811487561010655, 0.02961803792511399, 0.20025201426236344, 1.8689693016684472, 0.8346860279614128, -0.00020446439762745718, -0.0002700045624472994], "scale_": [0.2549019607843137, 0.25490196078431376, 0.28156931048910205, 0.28156931048910183, 1.1556711716461558, 0.027647846934767177, 0.04228062522627324, 0.0323014640218134, 0.022048834772938057, 5.599243565191234, 0.708751527124982, 0.9649105876290809, 0.054120387541988925, 0.1821652851070972, 0.1963514231096007, 0.19635142310960074, 0.2005137949844208, 0.20051379498442085, 0.7945515916030248, 0.03988796118705047, 0.0521137189465415, 0.04574176084009346, 0.02345167214669983, 6.436158380587957, 0.6743239738440561, 0.857267051096519, 0.031544291882259386, 0.11788379382353233, 0.14072470027566986, 0.14072470027566986, 0.12366222197579702, 0.1236622219757969, 0.5065206128536656, 0.06371414044044732, 0.06725034996736043, 0.07073196149333348, 0.03352591513053392, 4.828115092153652, 0.6149686505115819, 0.6420505445228867, 0.010482462850977911, 0.03882755258609876, 0.09852389604864853, 0.09852389604864847, 0.09638372089092961, 0.09638372089092961, 0.3838674497000574, 0.09273271805006159, 0.10113413081528128, 0.09954476978451812, 0.04720310187109138, 6.139634480711029, 0.5899787709100139, 0.48786286130390444, 0.006118368139441112, 0.01481046446819791], "n_features_in_": 56, "n_samples_seen_": 566}, "recommended_thresholds": {"conservative": 0.7, "balanced": 0.5, "aggressive": 0.3}, "time_windows": [5, 10, 30, 60], "feature_categories": {"buy_sell_ratios": 8, "volume_ratios": 12, "price_changes": 12, "volatilities": 8, "trends": 4, "accelerations": 4, "other_ratios": 8}}