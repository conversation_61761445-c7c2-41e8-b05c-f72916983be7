# Buy Predictor V4 - 完整技术实现指南

## 文档目标

本文档为**模型集成开发者**、**算法工程师**、**数据科学家**和**系统架构师**提供买点预测V4模型的完整技术实现细节，确保任何开发团队都能准确重现特征计算过程。

## 模型概述

- **模型名称**: Buy Predictor V4 Relative Features
- **特征数量**: 48个相对特征
- **时间窗口**: 5秒、10秒、30秒、60秒
- **适用场景**: 多代币环境下的买点预测
- **数据要求**: 最少60秒历史交易数据

## 数据输入格式规范

### 必需的DataFrame结构
```python
df = pd.DataFrame({
    'timestamp': pd.datetime,      # 交易时间戳，必须按时间排序
    'transaction_type': str,       # 交易类型：'buy' 或 'sell'
    'sol_amount': float,          # SOL金额，必须 > 0
    'usd_amount': float           # USD金额，必须 > 0
})
```

### 数据质量要求
1. **时间戳**: 必须是pandas datetime格式，按时间升序排列
2. **交易类型**: 只能是'buy'或'sell'，大小写敏感
3. **金额**: 所有金额必须大于0，不能有NaN值
4. **数据量**: 目标时间戳前至少60秒的交易数据

### 数据预处理步骤
```python
def preprocess_data(df, target_timestamp):
    """数据预处理标准流程"""
    # 1. 时间戳排序（必须）
    df = df.sort_values('timestamp').reset_index(drop=True)
    
    # 2. 数据清洗
    df = df[df['sol_amount'] > 0]      # 移除零金额交易
    df = df[df['usd_amount'] > 0]      # 移除零金额交易
    df = df.dropna()                   # 移除缺失值
    
    # 3. 时间窗口筛选 - 保留60秒内的数据
    min_timestamp = target_timestamp - timedelta(seconds=60)
    df = df[
        (df['timestamp'] >= min_timestamp) & 
        (df['timestamp'] <= target_timestamp)
    ]
    
    # 4. 数据类型优化
    df['sol_amount'] = df['sol_amount'].astype('float32')
    df['usd_amount'] = df['usd_amount'].astype('float32')
    
    return df
```

## 时间窗口管理

### 窗口定义
- **5秒窗口**: 捕获瞬时市场变化，识别突发交易
- **10秒窗口**: 短期趋势分析，平滑瞬时噪音
- **30秒窗口**: 中期模式识别，观察持续性行为
- **60秒窗口**: 长期背景分析，提供基准对比

### 窗口数据提取
```python
def get_window_data(df, timestamp, window_seconds):
    """提取指定时间窗口内的数据"""
    end_time = pd.to_datetime(timestamp)
    start_time = end_time - timedelta(seconds=window_seconds)
    
    window_df = df[
        (df['timestamp'] >= start_time) & 
        (df['timestamp'] <= end_time)
    ].copy()
    
    return window_df
```

## 完整特征计算实现

### 第一类：买卖比例特征 (8个特征)

#### 特征1-4: 买入量与卖出量比例
**计算公式**:
```
买入量 = Σ(买入交易的SOL金额)
卖出量 = Σ(卖出交易的SOL金额)
比例 = 买入量 / 卖出量
```

**实现代码**:
```python
def calculate_buy_sell_ratio(window_df, window_seconds):
    buy_df = window_df[window_df['transaction_type'] == 'buy']
    sell_df = window_df[window_df['transaction_type'] == 'sell']
    
    buy_volume = buy_df['sol_amount'].sum() if len(buy_df) > 0 else 0
    sell_volume = sell_df['sol_amount'].sum() if len(sell_df) > 0 else 0
    
    # 特殊情况处理
    if sell_volume > 0:
        ratio = buy_volume / sell_volume
    else:
        ratio = 10.0 if buy_volume > 0 else 0.0  # 只有买入时设为10.0
    
    return {f'buy_sell_ratio_{window_seconds}s': ratio}
```

**边界情况处理**:
- 卖出量为0但买入量>0: 返回10.0
- 买入量和卖出量都为0: 返回0.0
- 正常情况: 返回实际比例

#### 特征5-8: 买入交易笔数比例
**计算公式**:
```
买入交易数 = count(交易类型 == 'buy')
总交易数 = count(所有交易)
比例 = 买入交易数 / 总交易数
```

**实现代码**:
```python
def calculate_buy_transaction_ratio(window_df, window_seconds):
    if len(window_df) == 0:
        return {f'buy_transaction_ratio_{window_seconds}s': 0.0}
    
    buy_count = len(window_df[window_df['transaction_type'] == 'buy'])
    total_count = len(window_df)
    
    ratio = buy_count / total_count
    
    return {f'buy_transaction_ratio_{window_seconds}s': ratio}
```

### 第二类：交易量比例特征 (12个特征)

#### 特征9-12: 最大交易量与平均交易量比例
**计算公式**:
```
最大交易量 = max(所有交易的SOL金额)
平均交易量 = mean(所有交易的SOL金额)
比例 = 最大交易量 / 平均交易量
```

**实现代码**:
```python
def calculate_volume_max_ratio(window_df, window_seconds):
    if len(window_df) <= 1:
        return {f'volume_max_ratio_{window_seconds}s': 1.0}
    
    volume_mean = window_df['sol_amount'].mean()
    volume_max = window_df['sol_amount'].max()
    
    ratio = volume_max / (volume_mean + 1e-8)  # 避免除零
    
    return {f'volume_max_ratio_{window_seconds}s': ratio}
```

#### 特征13-16: 交易量标准差与均值比例（变异系数）
**计算公式**:
```
交易量标准差 = std(所有交易的SOL金额)
交易量均值 = mean(所有交易的SOL金额)
比例 = 标准差 / 均值
```

**实现代码**:
```python
def calculate_volume_std_ratio(window_df, window_seconds):
    if len(window_df) <= 1:
        return {f'volume_std_ratio_{window_seconds}s': 0.0}
    
    volume_mean = window_df['sol_amount'].mean()
    volume_std = window_df['sol_amount'].std()
    
    ratio = volume_std / (volume_mean + 1e-8)
    
    return {f'volume_std_ratio_{window_seconds}s': ratio}
```

#### 特征17-20: 中位数交易量与平均交易量比例
**计算公式**:
```
中位数交易量 = median(所有交易的SOL金额)
平均交易量 = mean(所有交易的SOL金额)
比例 = 中位数 / 均值
```

**实现代码**:
```python
def calculate_volume_median_ratio(window_df, window_seconds):
    if len(window_df) <= 1:
        return {f'volume_median_ratio_{window_seconds}s': 1.0}
    
    volume_mean = window_df['sol_amount'].mean()
    volume_median = window_df['sol_amount'].median()
    
    ratio = volume_median / (volume_mean + 1e-8)
    
    return {f'volume_median_ratio_{window_seconds}s': ratio}
```

### 第三类：价格相对变化特征 (12个特征)

#### 价格计算基础
**价格计算公式**:
```
价格 = USD金额 / SOL金额
```

**实现代码**:
```python
def calculate_prices(window_df):
    prices = window_df['usd_amount'] / (window_df['sol_amount'] + 1e-8)
    return prices
```

#### 特征21-24: 价格变化比例
**计算公式**:
```
首个价格 = 时间窗口内第一笔交易的价格
最后价格 = 时间窗口内最后一笔交易的价格
变化比例 = (最后价格 - 首个价格) / 首个价格
```

**实现代码**:
```python
def calculate_price_change_ratio(window_df, window_seconds):
    if len(window_df) <= 1:
        return {f'price_change_ratio_{window_seconds}s': 0.0}
    
    prices = calculate_prices(window_df)
    
    first_price = prices.iloc[0]
    last_price = prices.iloc[-1]
    
    if first_price > 0:
        change_ratio = (last_price - first_price) / first_price
    else:
        change_ratio = 0.0
    
    return {f'price_change_ratio_{window_seconds}s': change_ratio}
```

#### 特征25-28: 价格波动率
**计算公式**:
```
价格序列 = [价格1, 价格2, ..., 价格n]
价格均值 = mean(价格序列)
价格标准差 = std(价格序列)
波动率 = 标准差 / 均值
```

**实现代码**:
```python
def calculate_price_volatility_ratio(window_df, window_seconds):
    if len(window_df) <= 1:
        return {f'price_volatility_ratio_{window_seconds}s': 0.0}
    
    prices = calculate_prices(window_df)
    
    price_mean = prices.mean()
    price_std = prices.std()
    
    volatility_ratio = price_std / (price_mean + 1e-8)
    
    return {f'price_volatility_ratio_{window_seconds}s': volatility_ratio}
```

#### 特征29-32: 价格范围比例
**计算公式**:
```
价格序列 = [价格1, 价格2, ..., 价格n]
最高价 = max(价格序列)
最低价 = min(价格序列)
均价 = mean(价格序列)
范围比例 = (最高价 - 最低价) / 均价
```

**实现代码**:
```python
def calculate_price_range_ratio(window_df, window_seconds):
    if len(window_df) <= 1:
        return {f'price_range_ratio_{window_seconds}s': 0.0}
    
    prices = calculate_prices(window_df)
    
    price_mean = prices.mean()
    price_min = prices.min()
    price_max = prices.max()
    
    range_ratio = (price_max - price_min) / (price_mean + 1e-8)
    
    return {f'price_range_ratio_{window_seconds}s': range_ratio}
```

### 第四类：跨时间窗口比较特征 (8个特征)

#### 数据准备
```python
def prepare_window_summary_data(df, timestamp):
    window_data = {}
    
    for window in [5, 10, 30, 60]:
        window_df = get_window_data(df, timestamp, window)
        
        # 计算基础指标
        total_volume = window_df['sol_amount'].sum() if len(window_df) > 0 else 0
        buy_count = len(window_df[window_df['transaction_type'] == 'buy'])
        total_count = len(window_df)
        buy_ratio = buy_count / total_count if total_count > 0 else 0
        
        # 计算价格波动率
        if len(window_df) > 1:
            prices = calculate_prices(window_df)
            volatility = prices.std()
        else:
            volatility = 0
        
        window_data[window] = {
            'volume': total_volume,
            'buy_ratio': buy_ratio,
            'volatility': volatility
        }
    
    return window_data
```

#### 特征33-35: 交易量跨窗口比较
**计算公式**:
```
volume_5s_vs_10s_ratio = 5秒交易量 / 10秒交易量
volume_10s_vs_30s_ratio = 10秒交易量 / 30秒交易量
volume_30s_vs_60s_ratio = 30秒交易量 / 60秒交易量
```

**实现代码**:
```python
def calculate_volume_cross_window_ratios(window_data):
    features = {}
    
    features['volume_5s_vs_10s_ratio'] = safe_divide(
        window_data[5]['volume'], 
        window_data[10]['volume']
    )
    
    features['volume_10s_vs_30s_ratio'] = safe_divide(
        window_data[10]['volume'], 
        window_data[30]['volume']
    )
    
    features['volume_30s_vs_60s_ratio'] = safe_divide(
        window_data[30]['volume'], 
        window_data[60]['volume']
    )
    
    return features
```

#### 特征36: 买入比例跨窗口比较
**计算公式**:
```
buy_ratio_5s_vs_60s = 5秒买入比例 / 60秒买入比例
```

#### 特征37: 价格趋势跨窗口比较
**计算公式**:
```
price_trend_5s_vs_30s = 5秒价格变化 / 30秒价格变化
```

#### 特征38: 波动率跨窗口比较
**计算公式**:
```
volatility_5s_vs_60s = 5秒波动率 / 60秒波动率
```

#### 特征39: 交易量加速度比例
**计算公式**:
```
短期加速度 = (5秒交易量 - 10秒交易量) / 10秒交易量
长期加速度 = (30秒交易量 - 60秒交易量) / 60秒交易量
加速度比例 = 短期加速度 / 长期加速度
```

#### 特征40: 动量比例
**计算公式**:
```
短期动量 = 5秒价格变化 × 5秒交易量
长期动量 = 60秒价格变化 × 60秒交易量
动量比例 = 短期动量 / 长期动量
```

### 第五类：买点特有特征 (8个特征)

#### 特征41: 买入压力比例
**计算公式**:
```
近期买入量 = 5秒交易量 × 5秒买入比例
历史买入量 = 60秒交易量 × 60秒买入比例
买入压力比例 = 近期买入量 / 历史买入量
```

#### 特征42: 交易量突发比例
**计算公式**:
```
volume_burst_ratio = 5秒最大交易量比例 / 60秒最大交易量比例
```

#### 特征43: 价格支撑比例
**计算逻辑**:
```python
if 5秒价格变化 < 0:  # 价格下跌
    price_support_ratio = 5秒买入比例 / 60秒买入比例
else:  # 价格上涨或持平
    price_support_ratio = 1.0
```

#### 特征44: 累积比例
**计算公式**:
```
accumulation_ratio = 30秒买入比例 / 5秒买入比例
```

#### 特征45: 买入强度比例
**计算公式**:
```
buy_intensity_ratio = 5秒买卖比例 / 60秒买卖比例
```

#### 特征46: 市场深度比例
**计算公式**:
```
market_depth_ratio = 5秒交易量分散度 / 60秒交易量分散度
```

#### 特征47: 入场机会比例
**计算公式**:
```
entry_opportunity_ratio = 5秒价格波动率 × 5秒买入交易比例
```

#### 特征48: 风险收益比
**计算公式**:
```
收益 = |5秒价格变化|
风险 = 5秒价格波动率
risk_reward_ratio = 收益 / 风险
```

## 安全除法函数

所有除法运算都使用安全除法函数，避免除零错误：

```python
def safe_divide(numerator, denominator, default=0.0):
    """安全除法，处理除零情况"""
    if abs(denominator) < 1e-8:
        return default
    return numerator / denominator
```

## 完整特征提取流程

```python
def extract_all_relative_features(df, timestamp):
    """提取所有48个相对特征的完整流程"""
    
    # 1. 数据验证和预处理
    validate_input_data(df)
    processed_df = preprocess_data(df, timestamp)
    
    if len(processed_df) == 0:
        return {name: 0.0 for name in feature_names}
    
    features = {}
    
    # 2. 计算所有时间窗口的基础特征
    for window in [5, 10, 30, 60]:
        window_df = get_window_data(processed_df, timestamp, window)
        
        # 第一类：买卖比例特征
        features.update(calculate_buy_sell_ratio(window_df, window))
        features.update(calculate_buy_transaction_ratio(window_df, window))
        
        # 第二类：交易量比例特征
        features.update(calculate_volume_max_ratio(window_df, window))
        features.update(calculate_volume_std_ratio(window_df, window))
        features.update(calculate_volume_median_ratio(window_df, window))
        
        # 第三类：价格相对变化特征
        features.update(calculate_price_change_ratio(window_df, window))
        features.update(calculate_price_volatility_ratio(window_df, window))
        features.update(calculate_price_range_ratio(window_df, window))
    
    # 3. 准备跨窗口数据
    window_data = prepare_window_summary_data(processed_df, timestamp)
    
    # 4. 计算跨窗口比较特征
    features.update(calculate_volume_cross_window_ratios(window_data))
    features.update(calculate_buy_ratio_cross_window(window_data))
    features.update(calculate_price_trend_cross_window(features))
    features.update(calculate_volatility_cross_window(window_data))
    features.update(calculate_volume_acceleration_ratio(window_data))
    features.update(calculate_momentum_ratio(features, window_data))
    
    # 5. 计算买点特有特征
    features.update(calculate_buy_pressure_ratio(window_data))
    features.update(calculate_volume_burst_ratio(features))
    features.update(calculate_price_support_ratio(features, window_data))
    features.update(calculate_accumulation_ratio(window_data))
    features.update(calculate_buy_intensity_ratio(features))
    features.update(calculate_market_depth_ratio(features))
    features.update(calculate_entry_opportunity_ratio(features))
    features.update(calculate_risk_reward_ratio(features))
    
    # 6. 确保所有特征都存在
    for feature_name in expected_feature_names:
        if feature_name not in features:
            features[feature_name] = 0.0
    
    # 7. 验证特征值有效性
    for name, value in features.items():
        if np.isnan(value) or np.isinf(value):
            features[name] = 0.0
    
    return features
```

## 数据标准化

模型使用RobustScaler进行数据标准化：

```python
def robust_scale_features(features, scaler_params):
    """使用RobustScaler标准化特征"""
    scaled_features = []
    
    for i, feature_name in enumerate(scaler_params['feature_names']):
        original_value = features[feature_name]
        center = scaler_params['center_'][i]  # 中位数
        scale = scaler_params['scale_'][i]    # IQR
        
        # RobustScaler公式: (x - median) / IQR
        scaled_value = (original_value - center) / scale
        scaled_features.append(scaled_value)
    
    return np.array(scaled_features)
```

## 性能优化建议

### 1. 计算优化
```python
# 使用向量化操作
prices = df['usd_amount'].values / (df['sol_amount'].values + 1e-8)

# 缓存重复计算
@lru_cache(maxsize=128)
def cached_window_data(timestamp_str, window):
    return get_window_data(df, timestamp_str, window)
```

### 2. 内存优化
```python
# 只保留需要的列
df = df[['timestamp', 'transaction_type', 'sol_amount', 'usd_amount']].copy()

# 使用适当的数据类型
df['sol_amount'] = df['sol_amount'].astype('float32')
df['usd_amount'] = df['usd_amount'].astype('float32')
```

### 3. 并行计算
```python
from concurrent.futures import ThreadPoolExecutor

def parallel_feature_calculation(df, timestamps):
    with ThreadPoolExecutor(max_workers=4) as executor:
        futures = [
            executor.submit(extract_all_relative_features, df, ts) 
            for ts in timestamps
        ]
        results = [future.result() for future in futures]
    return results
```

## 错误处理和边界情况

### 常见边界情况
1. **数据为空**: 返回全零特征向量
2. **单笔交易**: 大部分比例特征返回默认值
3. **除零错误**: 使用安全除法函数处理
4. **异常值**: 检查NaN和Inf，替换为0.0

### 数据验证
```python
def validate_input_data(df):
    required_columns = ['timestamp', 'transaction_type', 'sol_amount', 'usd_amount']
    
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"缺少必需列: {col}")
    
    if len(df) == 0:
        raise ValueError("输入数据为空")
    
    if df['sol_amount'].min() <= 0:
        raise ValueError("SOL金额必须大于0")
    
    return True
```

## 测试验证

### 单元测试
```python
def test_feature_calculation():
    # 创建测试数据
    test_df = pd.DataFrame({
        'timestamp': pd.date_range('2025-01-01', periods=100, freq='1s'),
        'transaction_type': ['buy', 'sell'] * 50,
        'sol_amount': [1.0, 2.0] * 50,
        'usd_amount': [130.0, 260.0] * 50
    })
    
    # 计算特征
    features = extract_all_relative_features(test_df, test_df['timestamp'].iloc[-1])
    
    # 验证特征数量
    assert len(features) == 48, f"期望48个特征，实际{len(features)}个"
    
    # 验证特征值范围
    for name, value in features.items():
        assert not np.isnan(value), f"特征{name}为NaN"
        assert not np.isinf(value), f"特征{name}为无穷大"
    
    print("✅ 所有特征计算测试通过")
```

## 部署集成指南

### 模型文件清单
1. `buy_predictor_v4_relative_features_20250624_143400.cbm` - CatBoost模型
2. `buy_predictor_v4_relative_features_scaler_20250624_143400.json` - 标准化参数
3. `buy_predictor_v4_relative_features_features_20250624_143400.json` - 特征名称
4. `buy_predictor_v4_relative_features_metadata_20250624_143400.json` - 模型元数据

### 集成步骤
1. 加载模型和标准化参数
2. 实现特征提取函数
3. 实现数据预处理流程
4. 集成预测接口
5. 添加错误处理和日志

### 预测阈值建议
- **保守策略**: 阈值 0.7，高精度低召回
- **平衡策略**: 阈值 0.5，精度召回平衡
- **激进策略**: 阈值 0.3，高召回低精度

---

**本文档提供了买点预测V4模型的完整技术实现细节。开发者可以根据此指南在任何系统中准确重现模型的特征计算和预测功能。** 