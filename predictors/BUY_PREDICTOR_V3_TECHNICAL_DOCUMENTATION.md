# Enhanced Activity Price Buy Predictor V3 技术文档

## 模型概述

Enhanced Activity Price Buy Predictor V3 是一个基于Top 20特征的CatBoost买入预测模型，专门用于预测pump.fun代币的目标钱包买入行为。该模型优化了特征工程，在保持高预测精度的同时显著提升了推理效率。

### 模型基本信息
- **模型版本**: V3_BUY
- **算法类型**: CatBoost分类器
- **特征数量**: 20个核心特征
- **预测目标**: 目标钱包买入事件
- **模型格式**: .cbm (TypeScript兼容)
- **标准化器**: JSON格式参数

## 模型文件清单

### 核心模型文件
| 文件名 | 格式 | 用途 | 兼容性 |
|--------|------|------|--------|
| `catboost_buy_predictor_v3_20250620_101944.cbm` | CBM | CatBoost模型文件 | TypeScript/JavaScript |
| `catboost_buy_predictor_v3_scaler_20250620_101944.json` | JSON | 标准化器参数 | 全平台兼容 |
| `catboost_buy_predictor_v3_features_20250620_101944.json` | JSON | 特征名称列表 | 全平台兼容 |
| `catboost_buy_predictor_v3_metadata_20250620_101944.json` | JSON | 模型元数据 | 全平台兼容 |

### 模型参数配置
```json
{
  "iterations": 200,
  "learning_rate": 0.1,
  "depth": 6,
  "random_seed": 42,
  "verbose": false,
  "early_stopping_rounds": 20
}
```

## 特征定义

### 输入特征列表 (按重要性排序)

| 序号 | 特征名称 | 数据类型 | 时间窗口 | 含义 | 计算方式 |
|------|---------|----------|----------|------|----------|
| 1 | `buy_volume_sol_5s` | float | 5秒 | 5秒内SOL买入量 | 过去5秒内所有买入交易的SOL总量 |
| 2 | `volume_sol_max_5s` | float | 5秒 | 5秒内SOL交易量最大值 | 过去5秒内单笔交易的最大SOL金额 |
| 3 | `volume_sol_mean_5s` | float | 5秒 | 5秒内SOL交易量均值 | 过去5秒内所有交易的SOL金额平均值 |
| 4 | `buy_volume_sol_10s` | float | 10秒 | 10秒内SOL买入量 | 过去10秒内所有买入交易的SOL总量 |
| 5 | `buy_price_mean_60s` | float | 60秒 | 60秒内买入价格均值 | 过去60秒内买入交易的平均价格 |
| 6 | `buy_ratio_60s` | float | 60秒 | 60秒内买入交易比例 | 买入交易数量 / 总交易数量 |
| 7 | `volume_sol_mean_30s` | float | 30秒 | 30秒内SOL交易量均值 | 过去30秒内所有交易的SOL金额平均值 |
| 8 | `buy_price_mean_5s` | float | 5秒 | 5秒内买入价格均值 | 过去5秒内买入交易的平均价格 |
| 9 | `buy_price_mean_30s` | float | 30秒 | 30秒内买入价格均值 | 过去30秒内买入交易的平均价格 |
| 10 | `buy_volume_sol_60s` | float | 60秒 | 60秒内SOL买入量 | 过去60秒内所有买入交易的SOL总量 |
| 11 | `sell_volume_sol_5s` | float | 5秒 | 5秒内SOL卖出量 | 过去5秒内所有卖出交易的SOL总量 |
| 12 | `volume_sol_mean_10s` | float | 10秒 | 10秒内SOL交易量均值 | 过去10秒内所有交易的SOL金额平均值 |
| 13 | `volume_sol_max_10s` | float | 10秒 | 10秒内SOL交易量最大值 | 过去10秒内单笔交易的最大SOL金额 |
| 14 | `buy_price_mean_10s` | float | 10秒 | 10秒内买入价格均值 | 过去10秒内买入交易的平均价格 |
| 15 | `sell_volume_sol_10s` | float | 10秒 | 10秒内SOL卖出量 | 过去10秒内所有卖出交易的SOL总量 |
| 16 | `buy_volume_sol_30s` | float | 30秒 | 30秒内SOL买入量 | 过去30秒内所有买入交易的SOL总量 |
| 17 | `volume_sol_total_5s` | float | 5秒 | 5秒内SOL交易量总和 | 过去5秒内所有交易的SOL总金额 |
| 18 | `volume_sol_total_10s` | float | 10秒 | 10秒内SOL交易量总和 | 过去10秒内所有交易的SOL总金额 |
| 19 | `buy_sell_volume_ratio_60s` | float | 60秒 | 60秒内买卖量比例 | 买入SOL总量 / 卖出SOL总量 |
| 20 | `volume_sol_max_30s` | float | 30秒 | 30秒内SOL交易量最大值 | 过去30秒内单笔交易的最大SOL金额 |

### 特征向量格式
模型期望输入一个包含20个特征的数组，必须严格按照以下顺序：
```javascript
[
  sell_volume_sol_5s,        // 索引 0
  volume_sol_mean_5s,        // 索引 1
  volume_sol_mean_10s,       // 索引 2
  sell_volume_sol_10s,       // 索引 3
  buy_volume_sol_60s,        // 索引 4
  buy_price_mean_60s,        // 索引 5
  volume_sol_max_5s,         // 索引 6
  volume_sol_mean_30s,       // 索引 7
  buy_price_mean_10s,        // 索引 8
  buy_volume_sol_30s,        // 索引 9
  volume_sol_total_5s,       // 索引 10
  buy_volume_sol_5s,         // 索引 11
  buy_price_mean_30s,        // 索引 12
  volume_sol_total_10s,      // 索引 13
  buy_price_mean_5s,         // 索引 14
  buy_volume_sol_10s,        // 索引 15
  buy_sell_volume_ratio_60s, // 索引 16
  buy_ratio_60s,             // 索引 17
  volume_sol_max_10s,        // 索引 18
  volume_sol_max_30s         // 索引 19
]
```

## 实时数据处理

### 原始交易流数据结构

#### 1. 交易行为数据
```javascript
{
  "timestamp": "2024-06-20T08:00:00.000Z",   // ISO 8601时间戳
  "transaction_type": "buy",                  // "buy" 或 "sell"
  "sol_amount": 1.5,                         // SOL交易金额
  "is_target_wallet": false,                 // 是否为目标钱包 (不用作特征)
  "wallet_address": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"
}
```

#### 2. 价格数据 (Swap数据)
```javascript
{
  "blockTimestamp": "2024-06-20T08:00:00.000Z",
  "transactionType": "buy",
  "walletAddress": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
  "bought": {
    "symbol": "TOKEN",
    "amount": "1000000",
    "address": "4xYrnBTdACYetkJEAP4gj4bLFfKw9YYv1nvSWyS1pump"
  },
  "sold": {
    "symbol": "SOL",
    "amount": "1.5",
    "address": "So11111111111111111111111111111111111111112"
  },
  "totalValueSol": 1.5    // 以SOL计价的交易总价值
}
```

### 实时特征计算流程

#### 1. 数据缓存管理
- **维护滑动窗口**: 保留最近60秒的交易数据
- **数据结构**: 按时间戳排序的交易记录列表
- **内存管理**: 定期清理超过60秒的历史数据

#### 2. 特征计算时机
- **触发条件**: 每收到一笔新交易时计算特征
- **计算频率**: 建议每1-2秒计算一次当前特征
- **异步处理**: 特征计算不应阻塞交易数据接收

#### 3. 时间窗口特征计算算法

```javascript
// 伪代码示例
function calculateFeatures(currentTimestamp, transactionHistory) {
  const features = new Array(20).fill(0);
  const windows = [5, 10, 30, 60]; // 秒
  
  for (let windowSize of windows) {
    const windowStart = currentTimestamp - windowSize * 1000; // 毫秒
    const windowData = transactionHistory.filter(tx => 
      tx.timestamp >= windowStart && tx.timestamp <= currentTimestamp
    );
    
    // 计算各种统计特征
    const buyTxs = windowData.filter(tx => tx.transaction_type === 'buy');
    const sellTxs = windowData.filter(tx => tx.transaction_type === 'sell');
    
    // 买入量特征
    const buyVolume = buyTxs.reduce((sum, tx) => sum + tx.sol_amount, 0);
    
    // 卖出量特征  
    const sellVolume = sellTxs.reduce((sum, tx) => sum + tx.sol_amount, 0);
    
    // 价格特征 (需要从price数据计算)
    const buyPrices = buyTxs.filter(tx => tx.price > 0).map(tx => tx.price);
    const avgBuyPrice = buyPrices.length > 0 ? 
      buyPrices.reduce((sum, p) => sum + p, 0) / buyPrices.length : 0;
    
    // 交易量统计特征
    const allVolumes = windowData.map(tx => tx.sol_amount);
    const totalVolume = allVolumes.reduce((sum, v) => sum + v, 0);
    const avgVolume = allVolumes.length > 0 ? totalVolume / allVolumes.length : 0;
    const maxVolume = allVolumes.length > 0 ? Math.max(...allVolumes) : 0;
    
    // 比例特征
    const buyRatio = windowData.length > 0 ? buyTxs.length / windowData.length : 0.5;
    const buysellVolumeRatio = sellVolume > 0 ? buyVolume / sellVolume : 0;
    
    // 根据窗口大小设置对应的特征值
    // (具体索引对应关系见特征向量格式)
  }
  
  return features;
}
```

#### 4. 价格特征计算
```javascript
function calculatePriceFromSwap(swapData) {
  const solAmount = parseFloat(swapData.sold.amount || swapData.bought.amount);
  const tokenAmount = parseFloat(swapData.bought.amount || swapData.sold.amount);
  
  if (tokenAmount > 0) {
    return solAmount / tokenAmount; // SOL per Token
  }
  return 0;
}
```

### 数据标准化

使用保存的标准化器参数对特征进行标准化：

```javascript
function standardizeFeatures(features, scalerParams) {
  const standardized = new Array(20);
  
  for (let i = 0; i < 20; i++) {
    // Z-score标准化: (x - mean) / scale
    standardized[i] = (features[i] - scalerParams.mean_[i]) / scalerParams.scale_[i];
  }
  
  return standardized;
}
```

## 模型推理

### 输入要求
- **特征向量**: 长度为20的浮点数数组
- **数据顺序**: 必须严格按照特征向量格式
- **数据范围**: 已标准化的特征值 (通常在-3到+3之间)
- **缺失值处理**: 用0填充缺失的特征值

### 输出格式
- **预测类型**: 概率值
- **数值范围**: 0.0 - 1.0 (浮点数)
- **语义含义**: 目标钱包在当前时刻买入的概率
- **决策阈值**: 0.5 (>= 0.5 预测为买入，< 0.5 预测为不买入)

### 性能指标
- **AUC**: 0.9809
- **Precision**: 0.8667
- **Recall**: 0.7647  
- **F1 Score**: 0.8125
- **推荐阈值**: 0.5

## 数据质量要求

### 最小数据要求
- **时间窗口**: 需要最近60秒的交易历史
- **最少交易数**: 建议60秒内至少有3笔有效交易
- **价格有效性**: 所有价格数据必须 > 0
- **时间精度**: 毫秒级时间戳精度

### 异常情况处理
- **新代币冷启动**: 缺少历史数据时，特征值设为0
- **交易稀少**: 在时间窗口内无交易时，对应特征设为0
- **价格异常**: 价格为0或负数时，排除该交易的价格计算
- **比例计算**: 分母为0时，比例特征设为0或默认值

## TypeScript集成指南

### CatBoost模型加载
```typescript
// 使用CatBoost的JavaScript绑定加载.cbm模型
import { CatBoostClassifier } from 'catboost';

const model = new CatBoostClassifier();
await model.loadModel('./catboost_buy_predictor_v3_20250620_101944.cbm');
```

### 标准化器参数加载
```typescript
import scalerParams from './catboost_buy_predictor_v3_scaler_20250620_101944.json';

interface ScalerParams {
  mean_: number[];
  scale_: number[];
  feature_names: string[];
  n_features_in_: number;
}
```

### 预测流程
```typescript
async function predictBuyProbability(
  transactions: Transaction[], 
  currentTime: Date
): Promise<number> {
  // 1. 计算特征
  const features = calculateFeatures(transactions, currentTime);
  
  // 2. 标准化
  const standardizedFeatures = standardizeFeatures(features, scalerParams);
  
  // 3. 模型推理
  const probability = await model.predict([standardizedFeatures]);
  
  return probability[0]; // 返回买入概率
}
```

## 监控与维护

### 关键监控指标
- **特征分布**: 定期检查特征值分布是否正常
- **预测延迟**: 监控特征计算和模型推理的耗时
- **数据质量**: 监控缺失数据和异常值的比例
- **预测准确性**: 对比预测结果与实际买入事件

### 性能优化建议
- **缓存策略**: 合理使用内存缓存减少重复计算
- **并行计算**: 多个时间窗口的特征可并行计算
- **批量处理**: 积累多个时间点的特征批量推理
- **预计算**: 对于一些复杂统计量可以增量更新

## 版本信息

- **模型版本**: V3_BUY
- **训练时间**: 2025-06-20 10:19:44
- **CatBoost版本**: 支持.cbm格式
- **特征工程版本**: Top 20 优化特征集
- **兼容性**: TypeScript/JavaScript 生产环境 