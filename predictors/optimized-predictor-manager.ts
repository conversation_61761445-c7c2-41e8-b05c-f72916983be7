import { BuyPredictor } from './buy-predictor';
import { SellPredictor } from './sell-predictor';
import path from 'path';

/**
 * 优化的预测器管理器 - 混合模式：共享模型 + 独立历史
 * 这种设计结合了单例和多例的优点，避免了各自的缺点
 */
export class OptimizedPredictorManager {
  private static sharedBuyModel: any = null;
  private static sharedSellModel: any = null;
  private static isModelsLoaded: boolean = false;
  private static loadingPromise: Promise<void> | null = null;

  private tokenBuyPredictors: Map<string, BuyPredictor> = new Map();
  private tokenSellPredictors: Map<string, SellPredictor> = new Map();

  /**
   * 静态方法：预加载共享模型（只执行一次）
   */
  public static async preloadModels(
    buyModelPath?: string,
    sellModelPath?: string
  ): Promise<void> {
    if (this.isModelsLoaded) {
      console.log('📊 共享模型已加载，跳过重复加载');
      return;
    }

    if (this.loadingPromise) {
      console.log('📊 模型正在加载中，等待完成...');
      return this.loadingPromise;
    }

    this.loadingPromise = this.doLoadModels(buyModelPath, sellModelPath);
    return this.loadingPromise;
  }

  private static async doLoadModels(
    buyModelPath?: string,
    sellModelPath?: string
  ): Promise<void> {
    console.log('🚀 开始加载共享AI模型...');
    
    try {
      // 使用绝对路径
      const finalBuyPath = buyModelPath || path.resolve(__dirname, '../catboost_focal_blocktol_2025.cbm');
      const finalSellPath = sellModelPath || path.resolve(__dirname, '../best_catboost_sell_model.cbm');

      console.log(`📊 加载共享买入模型: ${finalBuyPath}`);
      console.log(`📊 加载共享卖出模型: ${finalSellPath}`);

      // 这里需要实际的模型加载逻辑
      // 暂时模拟加载过程
      const catboost = require('catboost');
      
      this.sharedBuyModel = new catboost.Model();
      this.sharedBuyModel.loadModel(finalBuyPath);
      
      this.sharedSellModel = new catboost.Model();
      this.sharedSellModel.loadModel(finalSellPath);

      this.isModelsLoaded = true;
      this.loadingPromise = null;
      
      console.log('✅ 共享AI模型加载完成');
      console.log(`   🤖 买入模型: 已加载`);
      console.log(`   🤖 卖出模型: 已加载`);
      
    } catch (error) {
      console.error('❌ 共享模型加载失败:', error);
      this.loadingPromise = null;
      throw error;
    }
  }

  /**
   * 为特定token创建独立的预测器实例（共享模型）
   */
  public async createPredictorsForToken(
    tokenAddress: string,
    buyThreshold: number = 0.5,
    sellThreshold: number = 0.3,
    windowSize: number = 20
  ): Promise<{buyPredictor: BuyPredictor, sellPredictor: SellPredictor}> {
    
    // 确保共享模型已加载
    if (!OptimizedPredictorManager.isModelsLoaded) {
      await OptimizedPredictorManager.preloadModels();
    }

    // 检查是否已为该token创建过预测器
    const existingBuy = this.tokenBuyPredictors.get(tokenAddress);
    const existingSell = this.tokenSellPredictors.get(tokenAddress);
    
    if (existingBuy && existingSell) {
      console.log(`📊 Token ${tokenAddress.slice(0, 8)}... 预测器已存在，直接返回`);
      return {
        buyPredictor: existingBuy,
        sellPredictor: existingSell
      };
    }

    console.log(`🆕 为Token ${tokenAddress.slice(0, 8)}... 创建独立预测器实例`);
    
    // 创建带有共享模型的预测器实例
    const buyPredictor = new BuyPredictor(
      '', // 空路径，因为使用共享模型
      windowSize,
      buyThreshold
    );
    
    const sellPredictor = new SellPredictor(
      '', // 空路径，因为使用共享模型
      windowSize,
      sellThreshold
    );

    // 注入共享模型（需要修改预测器类来支持这种模式）
    // buyPredictor.setSharedModel(OptimizedPredictorManager.sharedBuyModel);
    // sellPredictor.setSharedModel(OptimizedPredictorManager.sharedSellModel);

    // 存储到映射中
    this.tokenBuyPredictors.set(tokenAddress, buyPredictor);
    this.tokenSellPredictors.set(tokenAddress, sellPredictor);

    console.log(`✅ Token ${tokenAddress.slice(0, 8)}... 预测器创建完成`);
    console.log(`   🎯 买入阈值: ${(buyThreshold * 100).toFixed(1)}%`);
    console.log(`   🎯 卖出阈值: ${(sellThreshold * 100).toFixed(1)}%`);
    console.log(`   📊 窗口大小: ${windowSize}`);

    return {
      buyPredictor,
      sellPredictor
    };
  }

  /**
   * 获取token的预测器
   */
  public getPredictorsForToken(tokenAddress: string): {
    buyPredictor: BuyPredictor | null,
    sellPredictor: SellPredictor | null
  } {
    return {
      buyPredictor: this.tokenBuyPredictors.get(tokenAddress) || null,
      sellPredictor: this.tokenSellPredictors.get(tokenAddress) || null
    };
  }

  /**
   * 移除token的预测器（清理资源）
   */
  public removePredictorsForToken(tokenAddress: string): void {
    const buyPredictor = this.tokenBuyPredictors.get(tokenAddress);
    const sellPredictor = this.tokenSellPredictors.get(tokenAddress);

    if (buyPredictor) {
      buyPredictor.clearHistory();
      this.tokenBuyPredictors.delete(tokenAddress);
    }

    if (sellPredictor) {
      sellPredictor.clearHistory();
      this.tokenSellPredictors.delete(tokenAddress);
    }

    console.log(`🗑️ 已清理Token ${tokenAddress.slice(0, 8)}... 的预测器资源`);
  }

  /**
   * 获取所有活跃token列表
   */
  public getActiveTokens(): string[] {
    return Array.from(this.tokenBuyPredictors.keys());
  }

  /**
   * 获取预测器统计信息
   */
  public getStats(): {
    sharedModelsLoaded: boolean;
    activeTokenCount: number;
    memoryEstimate: string;
  } {
    const activeCount = this.tokenBuyPredictors.size;
    // 估算内存使用：共享模型 + 每个token的历史数据
    const estimatedMemoryMB = 100 + (activeCount * 5); // 共享模型100MB + 每token 5MB历史数据
    
    return {
      sharedModelsLoaded: OptimizedPredictorManager.isModelsLoaded,
      activeTokenCount: activeCount,
      memoryEstimate: `~${estimatedMemoryMB}MB`
    };
  }

  /**
   * 批量清理不活跃的token预测器
   */
  public cleanupInactiveTokens(activeTokenAddresses: string[]): void {
    const currentTokens = new Set(this.tokenBuyPredictors.keys());
    const activeTokens = new Set(activeTokenAddresses);
    
    let removedCount = 0;
    for (const tokenAddress of currentTokens) {
      if (!activeTokens.has(tokenAddress)) {
        this.removePredictorsForToken(tokenAddress);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      console.log(`🧹 批量清理完成: 移除${removedCount}个不活跃token的预测器`);
    }
  }

  /**
   * 完全清理所有资源
   */
  public cleanup(): void {
    console.log('🧹 清理所有预测器资源...');
    
    // 清理所有token预测器
    for (const tokenAddress of this.tokenBuyPredictors.keys()) {
      this.removePredictorsForToken(tokenAddress);
    }
    
    console.log('✅ 预测器管理器清理完成');
  }

  /**
   * 重置共享模型（用于测试或重启）
   */
  public static resetSharedModels(): void {
    OptimizedPredictorManager.sharedBuyModel = null;
    OptimizedPredictorManager.sharedSellModel = null;
    OptimizedPredictorManager.isModelsLoaded = false;
    OptimizedPredictorManager.loadingPromise = null;
    console.log('�� 共享模型已重置');
  }
} 