# Enhanced Activity Price Sell Predictor V3 模型文档

## 模型概述

Enhanced Activity Price Sell Predictor V3 是一个基于Top 20特征的优化卖出预测模型，专门用于预测pump.fun代币的目标钱包卖出行为。该模型基于V2模型优化而来，通过特征选择将特征数量从149个减少到20个，在保持预测性能的同时显著提升了模型效率（7.5倍提升）。

### 模型性能概览
- **特征数量**: 20个核心特征
- **模型类型**: CatBoostClassifier （默认，也支持RandomForest）
- **效率提升**: 相比V2模型提升7.5倍
- **预期性能损失**: AUC约2.2%，F1约1.7%
- **推荐阈值**: 0.5
- **TypeScript兼容**: 支持.cbm格式，可直接在TypeScript环境中使用

## 核心特征列表

### 1. 短期交易量特征 (5秒窗口)
| 特征名称 | 数据类型 | 含义 | 计算方式 |
|---------|----------|------|----------|
| `sell_volume_sol_5s` | float | 5秒内SOL卖出量 | 过去5秒内所有卖出交易的SOL总量 |
| `volume_sol_mean_5s` | float | 5秒内SOL交易量均值 | 过去5秒内所有交易的SOL金额平均值 |
| `volume_sol_max_5s` | float | 5秒内SOL交易量最大值 | 过去5秒内单笔交易的最大SOL金额 |
| `volume_sol_total_5s` | float | 5秒内SOL交易量总和 | 过去5秒内所有交易的SOL总金额 |
| `buy_volume_sol_5s` | float | 5秒内SOL买入量 | 过去5秒内所有买入交易的SOL总量 |
| `buy_price_mean_5s` | float | 5秒内买入价格均值 | 过去5秒内买入交易的平均价格 |

### 2. 中短期交易量特征 (10秒窗口)
| 特征名称 | 数据类型 | 含义 | 计算方式 |
|---------|----------|------|----------|
| `volume_sol_mean_10s` | float | 10秒内SOL交易量均值 | 过去10秒内所有交易的SOL金额平均值 |
| `sell_volume_sol_10s` | float | 10秒内SOL卖出量 | 过去10秒内所有卖出交易的SOL总量 |
| `buy_price_mean_10s` | float | 10秒内买入价格均值 | 过去10秒内买入交易的平均价格 |
| `volume_sol_total_10s` | float | 10秒内SOL交易量总和 | 过去10秒内所有交易的SOL总金额 |
| `buy_volume_sol_10s` | float | 10秒内SOL买入量 | 过去10秒内所有买入交易的SOL总量 |
| `volume_sol_max_10s` | float | 10秒内SOL交易量最大值 | 过去10秒内单笔交易的最大SOL金额 |

### 3. 中期交易量特征 (30秒窗口)
| 特征名称 | 数据类型 | 含义 | 计算方式 |
|---------|----------|------|----------|
| `volume_sol_mean_30s` | float | 30秒内SOL交易量均值 | 过去30秒内所有交易的SOL金额平均值 |
| `buy_volume_sol_30s` | float | 30秒内SOL买入量 | 过去30秒内所有买入交易的SOL总量 |
| `buy_price_mean_30s` | float | 30秒内买入价格均值 | 过去30秒内买入交易的平均价格 |
| `volume_sol_max_30s` | float | 30秒内SOL交易量最大值 | 过去30秒内单笔交易的最大SOL金额 |

### 4. 长期交易量特征 (60秒窗口)
| 特征名称 | 数据类型 | 含义 | 计算方式 |
|---------|----------|------|----------|
| `buy_volume_sol_60s` | float | 60秒内SOL买入量 | 过去60秒内所有买入交易的SOL总量 |
| `buy_price_mean_60s` | float | 60秒内买入价格均值 | 过去60秒内买入交易的平均价格 |
| `buy_sell_volume_ratio_60s` | float | 60秒内买卖量比例 | 买入SOL总量 / 卖出SOL总量 |
| `buy_ratio_60s` | float | 60秒内买入交易比例 | 买入交易数量 / 总交易数量 |

## 数据输入格式

### 原始交易流数据结构
模型需要接收两种类型的实时数据流：

#### 1. 交易行为数据 (CSV格式交易记录)
```
timestamp,transaction_type,sol_amount,is_target_wallet,wallet_address,...
2024-06-20T08:00:00.000Z,buy,1.5,false,9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM,...
2024-06-20T08:00:05.000Z,sell,2.3,true,7uMCBkzb9KBEF3JzhMkxf5VjDDfgNJzLjQQwmfP4pump,...
```

**关键字段说明:**
- `timestamp`: ISO 8601格式的时间戳
- `transaction_type`: 交易类型 ("buy" 或 "sell")
- `sol_amount`: SOL交易金额（用于计算交易量特征）
- `is_target_wallet`: 是否为目标钱包（仅用于标签生成，不作为特征）
- `wallet_address`: 钱包地址

#### 2. 价格数据 (NPZ格式的swaps数据)
```javascript
{
  "blockTimestamp": "2024-06-20T08:00:00.000Z",
  "transactionType": "buy",  // "buy" 或 "sell"
  "walletAddress": "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
  "bought": {
    "symbol": "TOKEN",
    "amount": "1000000",
    "address": "4xYrnBTdACYetkJEAP4gj4bLFfKw9YYv1nvSWyS1pump"
  },
  "sold": {
    "symbol": "SOL",
    "amount": "1.5",
    "address": "So11111111111111111111111111111111111111112"
  },
  "totalValueSol": 1.5  // 以SOL计价的交易总价值
}
```

## 实时特征计算流程

### 1. 数据预处理
- 接收到新的交易记录时，更新内存中的交易历史缓存
- 维护一个滑动窗口，保留最近60秒的交易数据
- 按时间戳排序所有交易记录

### 2. 特征计算时机
- 每收到一笔新交易时触发特征计算
- 或者按固定时间间隔（如每1秒）计算当前时刻的特征

### 3. 时间窗口特征计算

#### 价格特征计算
对于每个时间窗口(5s, 10s, 30s, 60s)：
```
当前时间 = current_timestamp
窗口开始时间 = current_timestamp - 时间窗口
筛选条件 = (timestamp >= 窗口开始时间) AND (timestamp <= 当前时间) AND (price > 0)

价格计算:
- 从swap数据中提取价格: price = sol_amount / token_amount
- 买入价格均值 = 买入交易的平均价格
- 买入量计算 = 买入交易的SOL总量
- 卖出量计算 = 卖出交易的SOL总量
```

#### 交易量特征计算
```
交易量特征计算:
- volume_sol_mean = 所有交易SOL金额的平均值
- volume_sol_total = 所有交易SOL金额的总和
- volume_sol_max = 单笔交易的最大SOL金额
- buy_volume_sol = 买入交易的SOL总量
- sell_volume_sol = 卖出交易的SOL总量
```

#### 比例特征计算
```
比例特征计算:
- buy_sell_volume_ratio = buy_volume_sol / sell_volume_sol (sell_volume_sol > 0)
- buy_ratio = 买入交易数量 / 总交易数量
```

### 4. 缺失值处理
当某个时间窗口内没有交易数据时：
- 所有价格和交易量特征设为 0
- 比例特征设为 0 或默认值 (如buy_ratio设为0.5)

## 模型输入向量

### 特征向量格式
模型接收一个包含20个特征的向量，按以下顺序排列：
```
[
  sell_volume_sol_5s,
  volume_sol_mean_5s,
  volume_sol_mean_10s,
  sell_volume_sol_10s,
  buy_volume_sol_60s,
  buy_price_mean_60s,
  volume_sol_max_5s,
  volume_sol_mean_30s,
  buy_price_mean_10s,
  buy_volume_sol_30s,
  volume_sol_total_5s,
  buy_volume_sol_5s,
  buy_price_mean_30s,
  volume_sol_total_10s,
  buy_price_mean_5s,
  buy_volume_sol_10s,
  buy_sell_volume_ratio_60s,
  buy_ratio_60s,
  volume_sol_max_10s,
  volume_sol_max_30s
]
```

### 数据标准化
- 所有特征在输入模型前需要进行StandardScaler标准化
- 使用训练时保存的scaler参数进行一致的标准化处理
- **CatBoost版本**: 标准化器参数保存为JSON格式，便于TypeScript读取

## 模型输出

### 预测结果
- **输出类型**: 概率值 (0-1之间的浮点数)
- **预测含义**: 目标钱包在当前时刻卖出的概率
- **推荐阈值**: 0.5 (概率 >= 0.5 预测为卖出，< 0.5 预测为不卖出)

### 实时预测流程
1. 收集最近60秒的交易数据
2. 计算20个核心特征
3. 使用保存的scaler进行标准化
4. 输入CatBoost模型 (.cbm格式)
5. 获得卖出概率预测
6. 根据阈值进行二分类决策

### TypeScript集成流程
1. 加载.cbm格式的CatBoost模型
2. 读取JSON格式的标准化器参数
3. 实时计算20个特征
4. 使用标准化器参数进行数据标准化
5. 调用CatBoost模型进行预测

## 数据质量要求

### 最小数据量
- **最少交易记录**: 建议最近60秒内至少有3笔交易
- **数据完整性**: timestamp和transaction_type字段必须完整
- **价格数据**: 需要有效的价格信息 (price > 0)

### 数据时效性
- **实时性要求**: 交易数据延迟不超过1秒
- **数据窗口**: 需要维护最近60秒的完整交易历史
- **缓存策略**: 建议使用LRU缓存管理历史数据

## 注意事项

### 特征工程要点
1. **时间同步**: 确保所有数据源的时间戳同步
2. **异常处理**: 处理价格为0或异常值的情况
3. **边界情况**: 新上线代币可能缺少足够的历史数据
4. **性能优化**: 合理使用缓存避免重复计算

### 模型局限性
1. **冷启动问题**: 新代币缺少历史数据时预测准确性下降
2. **市场环境**: 模型基于历史数据训练，极端市场条件下可能失效
3. **数据依赖**: 严重依赖实时数据质量和完整性

### 监控建议
1. **特征监控**: 定期检查各特征的分布是否与训练时一致
2. **预测质量**: 监控预测概率的分布和准确性
3. **数据延迟**: 监控数据接收的延迟情况
4. **异常检测**: 及时发现和处理数据异常

## CatBoost模型文件说明

### 文件清单
生成的CatBoost V3卖出预测器包含以下文件：

1. **enhanced_sell_predictor_v3_YYYYMMDD_HHMMSS.cbm**
   - CatBoost模型文件（TypeScript兼容）
   - 可直接在Node.js/TypeScript环境中加载

2. **enhanced_sell_predictor_v3_scaler_YYYYMMDD_HHMMSS.json**
   - 标准化器参数（JSON格式）
   - 包含mean_, scale_, var_等标准化参数

3. **enhanced_sell_predictor_v3_features_YYYYMMDD_HHMMSS.json**
   - 特征列表文件
   - 包含Top 20特征的名称和顺序

4. **enhanced_sell_predictor_v3_metadata_YYYYMMDD_HHMMSS.json**
   - 模型元数据
   - 包含模型版本、性能指标、兼容性信息

### TypeScript集成示例

#### 1. 加载CatBoost模型
```typescript
import { CatBoost } from 'catboost';
import * as fs from 'fs';

// 加载模型
const model = new CatBoost();
model.load_model('enhanced_sell_predictor_v3_20250620_103700.cbm');

// 加载标准化器参数
const scalerParams = JSON.parse(
  fs.readFileSync('enhanced_sell_predictor_v3_scaler_20250620_103700.json', 'utf8')
);
```

#### 2. 特征标准化
```typescript
function standardizeFeatures(features: number[], scalerParams: any): number[] {
  return features.map((feature, index) => {
    return (feature - scalerParams.mean_[index]) / scalerParams.scale_[index];
  });
}
```

#### 3. 预测流程
```typescript
function predictSellProbability(features: number[]): number {
  // 标准化特征
  const standardizedFeatures = standardizeFeatures(features, scalerParams);
  
  // 预测
  const prediction = model.predict([standardizedFeatures]);
  
  return prediction[0]; // 返回卖出概率
}
```

### 模型性能基准
基于2kDRXU测试数据集的性能表现：
- **AUC**: 0.9486
- **Precision**: 0.8077
- **Recall**: 0.7500
- **F1 Score**: 0.7778

### 兼容性说明
- **CatBoost版本**: 推荐使用1.2+
- **Node.js版本**: 推荐使用16+
- **TypeScript版本**: 推荐使用4.0+
- **模型格式**: .cbm (CatBoost Binary Model)
- **标准化器**: JSON格式，跨平台兼容 