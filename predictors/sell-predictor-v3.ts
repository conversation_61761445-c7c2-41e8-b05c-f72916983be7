import * as catboost from 'catboost';
import * as fs from 'fs';
import * as path from 'path';

export interface SellFeaturesV3 {
  // 5秒窗口特征
  sell_volume_sol_5s: number;
  volume_sol_mean_5s: number;
  buy_volume_sol_5s: number;
  volume_sol_max_5s: number;
  volume_sol_total_5s: number;
  buy_price_mean_5s: number;
  
  // 10秒窗口特征
  volume_sol_mean_10s: number;
  sell_volume_sol_10s: number;
  buy_volume_sol_10s: number;
  volume_sol_max_10s: number;
  volume_sol_total_10s: number;
  buy_price_mean_10s: number;
  
  // 30秒窗口特征
  volume_sol_mean_30s: number;
  buy_volume_sol_30s: number;
  buy_price_mean_30s: number;
  volume_sol_max_30s: number;
  
  // 60秒窗口特征
  buy_volume_sol_60s: number;
  buy_price_mean_60s: number;
  buy_sell_volume_ratio_60s: number;
  buy_ratio_60s: number;
}

export interface SellPredictionResultV3 {
  probability: number;
  prediction: boolean;
  confidence: number;
  timestamp: Date;
  predictionTimeMs: number;
  predictionTimeMicros: number;
  features?: SellFeaturesV3;
}

export interface TransactionDataV3 {
  timestamp: Date;
  transaction_type: 'buy' | 'sell';
  sol_amount: number;
  price?: number;
  wallet_address?: string;
  is_target_wallet?: boolean;
}

export interface ScalerParams {
  mean_?: number[];  // StandardScaler
  scale_: number[];
  var_?: number[];
  center_?: number[]; // RobustScaler
  scaler_type?: string;
  n_features_in_: number;
  n_samples_seen_: number;
  feature_names: string[];
}

export class SellPredictorV3 {
  private model: any;
  private modelPath: string;
  private scalerPath: string;
  private featuresPath: string;
  private scalerParams: ScalerParams | null = null;
  private featureNames: string[] = [];
  private threshold: number;
  private transactionHistory: TransactionDataV3[] = [];
  private isModelLoaded: boolean = false;
  private maxHistorySize: number = 3000; // 保留足够的历史数据用于60秒窗口

  // 特征缓存机制
  private cachedFeatures: SellFeaturesV3 | null = null;
  private lastFeatureCalculationHash: string = '';
  private featureCacheEnabled: boolean = true;

  constructor(
    modelPath: string = 'predictors/enhanced_sell_predictor_v3_normalized_20250620_150028.cbm',
    scalerPath: string = 'predictors/enhanced_sell_predictor_v3_normalized_scaler_20250620_150028.json',
    featuresPath: string = 'predictors/enhanced_sell_predictor_v3_normalized_features_20250620_150028.json',
    threshold: number = 0.5
  ) {
    this.modelPath = path.resolve(modelPath);
    this.scalerPath = path.resolve(scalerPath);
    this.featuresPath = path.resolve(featuresPath);
    this.threshold = threshold;
    
    console.log(`🤖 SellPredictorV3初始化: 阈值=${(threshold*100).toFixed(1)}%`);
    this.initializeModel();
  }

  private initializeModel(): void {
    try {
      // 加载模型
      if (!fs.existsSync(this.modelPath)) {
        console.warn(`Sell model V3 file not found: ${this.modelPath}`);
        return;
      }

      this.model = new catboost.Model();
      this.model.loadModel(this.modelPath);
      
      // 加载标准化器参数
      if (fs.existsSync(this.scalerPath)) {
        const scalerData = fs.readFileSync(this.scalerPath, 'utf8');
        this.scalerParams = JSON.parse(scalerData);
      } else {
        console.warn(`Scaler file not found: ${this.scalerPath}`);
      }
      
      // 加载特征名称
      if (fs.existsSync(this.featuresPath)) {
        const featuresData = fs.readFileSync(this.featuresPath, 'utf8');
        this.featureNames = JSON.parse(featuresData);
      } else {
        console.warn(`Features file not found: ${this.featuresPath}`);
      }
      
      this.isModelLoaded = true;
      console.log(`✅ SellPredictorV3 模型加载成功 (特征数: ${this.featureNames.length})`);
    } catch (error) {
      console.error(`❌ Failed to load SellPredictorV3 model: ${error}`);
      this.isModelLoaded = false;
    }
  }

  /**
   * 计算数据哈希值用于缓存判断
   */
  private calculateDataHash(transactions: TransactionDataV3[]): string {
    if (transactions.length === 0) return '';
    
    const lastFew = transactions.slice(-5);
    const hashData = lastFew.map(t => 
      `${t.timestamp.getTime()}-${t.transaction_type}-${t.sol_amount.toFixed(6)}`
    ).join('|');
    
    return hashData + `|len:${transactions.length}`;
  }

  /**
   * 添加新的交易数据
   */
  public addTransaction(transaction: TransactionDataV3): void {
    this.transactionHistory.push(transaction);
    this.invalidateFeatureCache();
    
    // 保持历史数据在合理范围内
    if (this.transactionHistory.length > this.maxHistorySize) {
      this.transactionHistory = this.transactionHistory.slice(-this.maxHistorySize);
    }
    
    // 🔍 调试信息：显示添加的交易数据
    if (process.env.DEBUG_V3_PREDICTOR_DETAILS === 'true') {
      console.log(`   🔍 SellPredictorV3添加交易: ${transaction.transaction_type} ${transaction.sol_amount}SOL (总数据: ${this.transactionHistory.length}笔)`);
    }
  }

  /**
   * 批量添加交易数据
   */
  public addTransactionBatch(transactions: TransactionDataV3[]): void {
    this.transactionHistory.push(...transactions);
    this.invalidateFeatureCache();
    
    if (this.transactionHistory.length > this.maxHistorySize) {
      this.transactionHistory = this.transactionHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * 使缓存失效
   */
  private invalidateFeatureCache(): void {
    this.cachedFeatures = null;
    this.lastFeatureCalculationHash = '';
  }

  /**
   * 从实时交易流数据中提取V3特征 (带缓存优化)
   */
  private extractFeaturesV3(currentTimestamp: Date): SellFeaturesV3 | null {
    if (this.transactionHistory.length === 0) {
      return null;
    }

    // 检查缓存
    if (this.featureCacheEnabled) {
      const currentHash = this.calculateDataHash(this.transactionHistory);
      if (currentHash === this.lastFeatureCalculationHash && this.cachedFeatures) {
        if (process.env.DEBUG_PREDICTION_PERFORMANCE === 'true') {
          console.log(`🚀 使用缓存的卖出特征V3 (哈希: ${currentHash.slice(0, 20)}...)`);
        }
        return this.cachedFeatures;
      }
      this.lastFeatureCalculationHash = currentHash;
    }

    const features: SellFeaturesV3 = {
      // 5秒窗口
      sell_volume_sol_5s: 0,
      volume_sol_mean_5s: 0,
      buy_volume_sol_5s: 0,
      volume_sol_max_5s: 0,
      volume_sol_total_5s: 0,
      buy_price_mean_5s: 0,
      
      // 10秒窗口
      volume_sol_mean_10s: 0,
      sell_volume_sol_10s: 0,
      buy_volume_sol_10s: 0,
      volume_sol_max_10s: 0,
      volume_sol_total_10s: 0,
      buy_price_mean_10s: 0,
      
      // 30秒窗口
      volume_sol_mean_30s: 0,
      buy_volume_sol_30s: 0,
      buy_price_mean_30s: 0,
      volume_sol_max_30s: 0,
      
      // 60秒窗口
      buy_volume_sol_60s: 0,
      buy_price_mean_60s: 0,
      buy_sell_volume_ratio_60s: 0,
      buy_ratio_60s: 0.5 // 默认比例
    };

    const windows = [5, 10, 30, 60];
    
    for (const windowSize of windows) {
      const windowStart = new Date(currentTimestamp.getTime() - windowSize * 1000);
      const windowData = this.transactionHistory.filter(tx => 
        tx.timestamp >= windowStart && tx.timestamp <= currentTimestamp
      );
      
      if (windowData.length === 0) continue;
      
      // 按交易类型分组
      const buyTxs = windowData.filter(tx => tx.transaction_type === 'buy');
      const sellTxs = windowData.filter(tx => tx.transaction_type === 'sell');
      
      // 计算基础统计量
      const allVolumes = windowData.map(tx => tx.sol_amount);
      const buyVolumes = buyTxs.map(tx => tx.sol_amount);
      const sellVolumes = sellTxs.map(tx => tx.sol_amount);
      const buyPrices = buyTxs.filter(tx => tx.price && tx.price > 0).map(tx => tx.price!);
      
      const totalVolume = allVolumes.reduce((sum, v) => sum + v, 0);
      const buyVolume = buyVolumes.reduce((sum, v) => sum + v, 0);
      const sellVolume = sellVolumes.reduce((sum, v) => sum + v, 0);
      const avgVolume = allVolumes.length > 0 ? totalVolume / allVolumes.length : 0;
      const maxVolume = allVolumes.length > 0 ? Math.max(...allVolumes) : 0;
      const avgBuyPrice = buyPrices.length > 0 ? buyPrices.reduce((sum, p) => sum + p, 0) / buyPrices.length : 0;
      
      // 计算比例特征
      const buyRatio = windowData.length > 0 ? buyTxs.length / windowData.length : 0.5;
      const buySellVolumeRatio = sellVolume > 0 ? buyVolume / sellVolume : (buyVolume > 0 ? 1000 : 0);
      
      // 根据窗口大小设置对应的特征值
      if (windowSize === 5) {
        features.sell_volume_sol_5s = sellVolume;
        features.volume_sol_mean_5s = avgVolume;
        features.buy_volume_sol_5s = buyVolume;
        features.volume_sol_max_5s = maxVolume;
        features.volume_sol_total_5s = totalVolume;
        features.buy_price_mean_5s = avgBuyPrice;
      } else if (windowSize === 10) {
        features.volume_sol_mean_10s = avgVolume;
        features.sell_volume_sol_10s = sellVolume;
        features.buy_volume_sol_10s = buyVolume;
        features.volume_sol_max_10s = maxVolume;
        features.volume_sol_total_10s = totalVolume;
        features.buy_price_mean_10s = avgBuyPrice;
      } else if (windowSize === 30) {
        features.volume_sol_mean_30s = avgVolume;
        features.buy_volume_sol_30s = buyVolume;
        features.buy_price_mean_30s = avgBuyPrice;
        features.volume_sol_max_30s = maxVolume;
      } else if (windowSize === 60) {
        features.buy_volume_sol_60s = buyVolume;
        features.buy_price_mean_60s = avgBuyPrice;
        features.buy_sell_volume_ratio_60s = buySellVolumeRatio;
        features.buy_ratio_60s = buyRatio;
      }
    }

    // 缓存特征
    if (this.featureCacheEnabled) {
      this.cachedFeatures = features;
    }

    return features;
  }

  /**
   * 将特征转换为模型输入向量 (按照训练时的顺序)
   */
  private featuresToVector(features: SellFeaturesV3): number[] {
    // 按照 enhanced_sell_predictor_v3_features_20250620_103700.json 中的顺序
    return [
      features.sell_volume_sol_5s,      // 0
      features.volume_sol_mean_5s,      // 1
      features.volume_sol_mean_10s,     // 2
      features.sell_volume_sol_10s,     // 3
      features.buy_volume_sol_60s,      // 4
      features.buy_price_mean_60s,      // 5
      features.volume_sol_max_5s,       // 6
      features.volume_sol_mean_30s,     // 7
      features.buy_price_mean_10s,      // 8
      features.buy_volume_sol_30s,      // 9
      features.volume_sol_total_5s,     // 10
      features.buy_volume_sol_5s,       // 11
      features.buy_price_mean_30s,      // 12
      features.volume_sol_total_10s,    // 13
      features.buy_price_mean_5s,       // 14
      features.buy_volume_sol_10s,      // 15
      features.buy_sell_volume_ratio_60s, // 16
      features.buy_ratio_60s,           // 17
      features.volume_sol_max_10s,      // 18
      features.volume_sol_max_30s       // 19
    ];
  }

  /**
   * Sigmoid激活函数 - 将logits转换为概率
   */
  private sigmoid(x: number): number {
    // 避免数值溢出
    if (x > 500) return 1.0;
    if (x < -500) return 0.0;
    return 1 / (1 + Math.exp(-x));
  }

  /**
   * 使用标准化器参数对特征进行标准化
   */
  private standardizeFeatures(features: number[]): number[] {
    if (!this.scalerParams || !this.scalerParams.scale_) {
      console.warn('⚠️ 标准化器参数未加载，使用原始特征');
      return features;
    }

    const standardized = new Array(20);
    
    // 检查是否为RobustScaler
    if (this.scalerParams.scaler_type === 'RobustScaler' && this.scalerParams.center_) {
      for (let i = 0; i < 20; i++) {
        // RobustScaler标准化: (x - center) / scale
        standardized[i] = (features[i] - this.scalerParams.center_[i]) / this.scalerParams.scale_[i];
      }
      if (process.env.DEBUG_V3_PREDICTOR_DETAILS === 'true') {
        console.log(`🔧 使用RobustScaler标准化特征`);
      }
    } else if (this.scalerParams.mean_) {
      for (let i = 0; i < 20; i++) {
        // StandardScaler标准化: (x - mean) / scale
        standardized[i] = (features[i] - this.scalerParams.mean_[i]) / this.scalerParams.scale_[i];
      }
      if (process.env.DEBUG_V3_PREDICTOR_DETAILS === 'true') {
        console.log(`🔧 使用StandardScaler标准化特征`);
      }
    } else {
      console.warn('⚠️ 无法识别标准化器类型，使用原始特征');
      return features;
    }

    return standardized;
  }

  /**
   * 执行卖出预测
   */
  public async predictSell(currentTimestamp?: Date): Promise<SellPredictionResultV3> {
    const predictionStart = performance.now();
    const timestamp = currentTimestamp || new Date();

    console.log(`🔍 V3卖出预测 - 开始预测`);
    console.log(`  - 模型加载状态: ${this.isModelLoaded ? '✅' : '❌'}`);
    console.log(`  - 模型对象存在: ${this.model ? '✅' : '❌'}`);
    console.log(`  - 历史交易数: ${this.transactionHistory.length}笔`);

    if (!this.isModelLoaded || !this.model) {
      console.log(`❌ V3卖出预测 - 模型未加载，返回默认值`);
      return {
        probability: 0.0,
        prediction: false,
        confidence: 0.0,
        timestamp,
        predictionTimeMs: 0,
        predictionTimeMicros: 0
      };
    }

    try {
      // 提取特征
      console.log(`🔍 V3卖出预测 - 开始提取特征`);
      const features = this.extractFeaturesV3(timestamp);
      if (!features) {
        console.log(`❌ V3卖出预测 - 特征提取失败，返回默认值`);
        return {
          probability: 0.0,
          prediction: false,
          confidence: 0.0,
          timestamp,
          predictionTimeMs: 0,
          predictionTimeMicros: 0
        };
      }
      console.log(`✅ V3卖出预测 - 特征提取成功`);

      // 转换为向量
      const featureVector = this.featuresToVector(features);
      
      // 标准化
      const standardizedFeatures = this.standardizeFeatures(featureVector);
      
      // 🔥 调试：分析交易数据分布
      const now = timestamp;
      const recent60s = this.transactionHistory.filter(tx => 
        tx.timestamp >= new Date(now.getTime() - 60 * 1000) && tx.timestamp <= now
      );
      const recent10s = this.transactionHistory.filter(tx => 
        tx.timestamp >= new Date(now.getTime() - 10 * 1000) && tx.timestamp <= now
      );
      const buyTxs60s = recent60s.filter(tx => tx.transaction_type === 'buy');
      const sellTxs60s = recent60s.filter(tx => tx.transaction_type === 'sell');
      const buyTxs10s = recent10s.filter(tx => tx.transaction_type === 'buy');
      
      console.log(`🔍 V3卖出预测 - 交易数据分析:`);
      console.log(`  - 60s内总交易: ${recent60s.length}笔 (买入: ${buyTxs60s.length}, 卖出: ${sellTxs60s.length})`);
      console.log(`  - 10s内总交易: ${recent10s.length}笔 (买入: ${buyTxs10s.length})`);
      
      // 🔥 修复：限制打印的价格数量，去重并只显示前20个
      const buyPrices = buyTxs60s
        .map(tx => tx.price?.toFixed(10) || 'N/A')  // 使用10位精度以区分小价格差异
        .filter(price => price !== 'N/A');
      
      if (buyPrices.length > 0) {
        // 去重并排序
        const uniquePrices = [...new Set(buyPrices)].sort();
        const displayPrices = uniquePrices.slice(0, 20); // 最多显示20个
        const moreCount = uniquePrices.length - displayPrices.length;
        
        console.log(`  - 60s内买入价格: ${displayPrices.join(', ')}${moreCount > 0 ? ` (还有${moreCount}个不同价格...)` : ''}`);
      } else {
        console.log(`  - 60s内买入价格: 无有效价格数据`);
      }
      
      // 🔥 调试：输出原始特征值
      console.log(`🔍 V3卖出预测 - 原始特征样本:`);
      console.log(`  - Volume特征: 5s=${featureVector[0].toFixed(6)}, mean_5s=${featureVector[1].toFixed(6)}`);
      console.log(`  - Price特征: buy_price_60s=${featureVector[5].toFixed(10)}, buy_price_10s=${featureVector[8].toFixed(10)}`);
      console.log(`  - Ratio特征: buy_ratio=${featureVector[17].toFixed(4)}, volume_ratio=${featureVector[16].toFixed(2)}`);
      
      // 🔥 调试：输出标准化后的特征值
      console.log(`🔍 V3卖出预测 - 标准化后样本:`);
      if (process.env.DEBUG_V3_PREDICTOR_DETAILS === 'true') {
      console.log(`  - 标准化特征前5个: [${standardizedFeatures.slice(0, 5).map(f => f.toFixed(3)).join(', ')}]`);
    }
      console.log(`  - 价格相关标准化: price_60s=${standardizedFeatures[5].toFixed(3)}, price_10s=${standardizedFeatures[8].toFixed(3)}`);
      
      // 模型预测
      const prediction = this.model.predict([standardizedFeatures]);
      const rawLogit = Array.isArray(prediction) ? prediction[0] : prediction;
      
      // 🔥 调试：输出模型预测的原始值
      console.log(`🔍 V3卖出预测 - 模型输出:`);
      console.log(`  - 原始logit: ${rawLogit.toFixed(6)}`);
      console.log(`  - Sigmoid转换前: ${rawLogit}`);
      console.log(`  - Sigmoid转换后: ${this.sigmoid(rawLogit).toFixed(6)}`);
      console.log(`  - 预测阈值: ${this.threshold}`);
      console.log(`  - 预测结果: ${this.sigmoid(rawLogit) >= this.threshold ? '卖出' : '持有'}`);
      
      // 🔥 检查是否有特征异常值
      const featureAnomalies = standardizedFeatures.map((val, idx) => {
        if (Math.abs(val) > 5) {
          return `特征${idx}(${this.featureNames[idx] || 'unknown'}): ${val.toFixed(3)}`;
        }
        return null;
      }).filter(x => x !== null);
      
      if (featureAnomalies.length > 0) {
        console.log(`⚠️ 检测到异常特征值: ${featureAnomalies.join(', ')}`);
        
        // 🔥 新增：异常特征分析和风险评估
        const highAnomalyCount = featureAnomalies.length;
        const maxAnomalyValue = Math.max(...standardizedFeatures.map(Math.abs));
        
        console.log(`   📊 异常特征统计:`);
        console.log(`   - 异常特征数量: ${highAnomalyCount}/${standardizedFeatures.length}`);
        console.log(`   - 最大异常值: ${maxAnomalyValue.toFixed(3)}`);
        
        // 风险评估
        let riskLevel = 'LOW';
        if (highAnomalyCount >= 5 || maxAnomalyValue > 20) {
          riskLevel = 'CRITICAL';
          console.log(`   🚨 风险评估: ${riskLevel} - 建议暂停交易`);
        } else if (highAnomalyCount >= 3 || maxAnomalyValue > 10) {
          riskLevel = 'HIGH';
          console.log(`   ⚠️ 风险评估: ${riskLevel} - 建议降低交易额度`);
        } else {
          riskLevel = 'MEDIUM';
          console.log(`   ⚡ 风险评估: ${riskLevel} - 可以谨慎交易`);
        }
        
        // 异常特征解释
        console.log(`   🔍 异常特征解释:`);
        featureAnomalies.forEach(anomaly => {
          if (anomaly.includes('sell_volume')) {
            console.log(`   - ${anomaly}: 卖出压力异常，可能有大户抛售`);
          } else if (anomaly.includes('buy_volume')) {
            console.log(`   - ${anomaly}: 买入量异常，可能有大户买入`);
          } else if (anomaly.includes('volume_sol_total')) {
            console.log(`   - ${anomaly}: 交易量异常活跃，市场波动加剧`);
          } else if (anomaly.includes('price')) {
            console.log(`   - ${anomaly}: 价格异常，可能存在价格操纵`);
          }
        });
      }
      
      // 🔥 CatBoost二分类模型输出logits，需要应用sigmoid函数转换为概率
      const probability = this.sigmoid(rawLogit);
      
      const predictionTime = performance.now() - predictionStart;
      
      return {
        probability: probability,
        prediction: probability >= this.threshold,
        confidence: Math.abs(probability - 0.5) * 2, // 置信度计算
        timestamp,
        predictionTimeMs: predictionTime,
        predictionTimeMicros: predictionTime * 1000,
        features
      };
      
    } catch (error) {
      console.error(`❌ SellPredictorV3 预测失败: ${error}`);
      return {
        probability: 0.0,
        prediction: false,
        confidence: 0.0,
        timestamp,
        predictionTimeMs: 0,
        predictionTimeMicros: 0
      };
    }
  }

  /**
   * 检查是否有足够的数据进行预测
   * V3模型需要60秒的完整数据窗口才能计算准确特征
   */
  public canPredict(currentTimestamp?: Date): boolean {
    const timestamp = currentTimestamp || new Date();
    
    if (this.transactionHistory.length === 0) {
      return false;
    }
    
    // 🔥 关键修复：确保有60秒的完整数据窗口
    const oldestTransaction = this.transactionHistory[0];
    const dataSpan = timestamp.getTime() - oldestTransaction.timestamp.getTime();
    const hasFullWindow = dataSpan >= 60 * 1000; // 至少60秒的数据跨度
    
    // 检查60秒窗口内的交易数量
    const minWindow = new Date(timestamp.getTime() - 60 * 1000);
    const recentTransactions = this.transactionHistory.filter(tx => 
      tx.timestamp >= minWindow && tx.timestamp <= timestamp
    );
    
    const hasMinTransactions = recentTransactions.length >= 5; // 提高最小交易要求
    
    // 🔥 只有同时满足数据跨度和交易数量要求才能预测
    const canPredict = hasFullWindow && hasMinTransactions;
    
    if (!canPredict) {
      console.log(`🔍 V3卖出预测器数据检查:`);
      console.log(`  - 数据跨度: ${(dataSpan / 1000).toFixed(1)}s (需要≥60s)`);
      console.log(`  - 60s内交易数: ${recentTransactions.length} (需要≥5笔)`);
      console.log(`  - 总历史数据: ${this.transactionHistory.length}笔`);
      console.log(`  - 可以预测: ${canPredict ? '✅' : '❌'}`);
    }
    
    return canPredict;
  }

  /**
   * 获取队列状态
   */
  public getQueueStatus(): {
    currentSize: number;
    maxSize: number;
    utilizationRate: number;
    canPredict: boolean;
    neededForPrediction: number;
    dataSpanSeconds?: number;
    recentTransactions?: number;
  } {
    const timestamp = new Date();
    let dataSpanSeconds = 0;
    let recentTransactions = 0;
    
    if (this.transactionHistory.length > 0) {
      const oldestTransaction = this.transactionHistory[0];
      dataSpanSeconds = (timestamp.getTime() - oldestTransaction.timestamp.getTime()) / 1000;
      
      const minWindow = new Date(timestamp.getTime() - 60 * 1000);
      recentTransactions = this.transactionHistory.filter(tx => 
        tx.timestamp >= minWindow && tx.timestamp <= timestamp
      ).length;
    }
    
    return {
      currentSize: this.transactionHistory.length,
      maxSize: this.maxHistorySize,
      utilizationRate: this.transactionHistory.length / this.maxHistorySize,
      canPredict: this.canPredict(),
      neededForPrediction: 5, // 更新为新的要求
      dataSpanSeconds,
      recentTransactions
    };
  }

  /**
   * 清空历史数据
   */
  public clearHistory(): void {
    this.transactionHistory = [];
    this.invalidateFeatureCache();
  }

  /**
   * 获取模型信息
   */
  public getModelInfo(): {
    modelPath: string;
    isLoaded: boolean;
    threshold: number;
    historyLength: number;
    featureCount: number;
  } {
    return {
      modelPath: this.modelPath,
      isLoaded: this.isModelLoaded,
      threshold: this.threshold,
      historyLength: this.transactionHistory.length,
      featureCount: this.featureNames.length
    };
  }

  /**
   * 设置预测阈值
   */
  public setThreshold(threshold: number): void {
    this.threshold = threshold;
  }

  /**
   * 启用/禁用特征缓存
   */
  public enableFeatureCache(enabled: boolean = true): void {
    this.featureCacheEnabled = enabled;
    if (!enabled) {
      this.invalidateFeatureCache();
    }
  }

  /**
   * 获取缓存状态
   */
  public getCacheStatus(): {
    enabled: boolean;
    hasCachedFeatures: boolean;
    lastHash: string;
  } {
    return {
      enabled: this.featureCacheEnabled,
      hasCachedFeatures: this.cachedFeatures !== null,
      lastHash: this.lastFeatureCalculationHash
    };
  }

  /**
   * 重新加载模型
   */
  public reloadModel(): boolean {
    try {
      this.initializeModel();
      return this.isModelLoaded;
    } catch (error) {
      console.error(`❌ 重新加载模型失败: ${error}`);
      return false;
    }
  }

  /**
   * 获取最新特征（调试用）
   */
  public getLatestFeatures(): SellFeaturesV3 | null {
    return this.cachedFeatures;
  }
} 