"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuyPredictor = void 0;
const catboost = require("catboost");
const fs = require("fs");
const path = require("path");
class BuyPredictor {
    constructor(modelPath = '../catboost_focal_blocktol_2025.cbm', windowSize = 10, threshold = 0.5, maxHistorySize = 100 // 默认保持最近100笔交易
    ) {
        this.transactionHistory = [];
        this.isModelLoaded = false;
        // 如果是相对路径，则相对于项目根目录解析
        if (modelPath.startsWith('./') || modelPath.startsWith('../')) {
            this.modelPath = path.resolve(__dirname, '../', modelPath.replace('./', ''));
        }
        else {
            this.modelPath = path.resolve(modelPath);
        }
        this.windowSize = windowSize;
        this.threshold = threshold;
        this.maxHistorySize = maxHistorySize;
        this.minDataForPrediction = 10; // 🔥 修改：统一设置为最少10笔数据
        console.log(`🤖 BuyPredictor初始化: 窗口=${windowSize}, 阈值=${(threshold * 100).toFixed(1)}%, 最小数据=${this.minDataForPrediction}笔`);
        this.initializeModel();
    }
    initializeModel() {
        try {
            if (!fs.existsSync(this.modelPath)) {
                console.warn(`Model file not found: ${this.modelPath}. Predictions will return default values.`);
                return;
            }
            this.model = new catboost.Model();
            this.model.loadModel(this.modelPath);
            this.isModelLoaded = true;
            console.log(`Buy model loaded successfully from: ${this.modelPath}`);
        }
        catch (error) {
            console.error(`Failed to load buy model: ${error}`);
            this.isModelLoaded = false;
        }
    }
    /**
     * 提取买点预测特征 - 修复版，使用最近的windowSize笔数据
     */
    extractBuyFeatures(transactions) {
        if (transactions.length < this.minDataForPrediction) {
            console.log(`⚠️ 数据不足: ${transactions.length} < ${this.minDataForPrediction}，无法提取特征`);
            return null;
        }
        // 🔥 修复：使用最近的windowSize笔数据，与卖出预测器保持一致
        // 这样确保新数据能够影响预测结果（通过滑动窗口）
        let windowData = transactions.slice(-this.windowSize);
        // 如果实际数据少于窗口大小，用默认值填充前面的部分
        while (windowData.length < this.windowSize) {
            const defaultTransaction = {
                timestamp: new Date(Date.now() - (this.windowSize - windowData.length) * 60000),
                action: 2, // hold
                sol_amount: 0.001, // 小额默认值
                usd_amount: 0.15,
                is_target_wallet: false,
                wallet: '',
                block_number: 0
            };
            windowData.unshift(defaultTransaction);
        }
        console.log(`📊 特征提取: 原始数据${transactions.length}笔, 使用数据${this.windowSize}笔, 填充${this.windowSize - Math.min(transactions.length, this.windowSize)}笔`);
        // 提取特征
        const actions = windowData.map(t => t.action);
        const solAmounts = windowData.map(t => t.sol_amount);
        const usdAmounts = windowData.map(t => t.usd_amount);
        const lastTimestamp = windowData[windowData.length - 1].timestamp;
        // 计算统计特征
        const solAmountMean = solAmounts.reduce((a, b) => a + b, 0) / solAmounts.length;
        const usdAmountMean = usdAmounts.reduce((a, b) => a + b, 0) / usdAmounts.length;
        const solAmountStd = Math.sqrt(solAmounts.reduce((sum, val) => sum + Math.pow(val - solAmountMean, 2), 0) / solAmounts.length);
        const usdAmountStd = Math.sqrt(usdAmounts.reduce((sum, val) => sum + Math.pow(val - usdAmountMean, 2), 0) / usdAmounts.length);
        return {
            sol_amount_mean: solAmountMean,
            sol_amount_std: solAmountStd,
            usd_amount_mean: usdAmountMean,
            usd_amount_std: usdAmountStd,
            buy_count: actions.filter(a => a === 1).length,
            sell_count: actions.filter(a => a === 0).length,
            hold_count: actions.filter(a => a === 2).length,
            hour_last: lastTimestamp.getHours(),
            weekday_last: lastTimestamp.getDay()
        };
    }
    /**
     * 添加新的交易数据
     */
    addTransaction(transaction) {
        this.transactionHistory.push(transaction);
        // 保持历史数据在合理范围内（最多保存1000条记录）
        if (this.transactionHistory.length > 1000) {
            this.transactionHistory = this.transactionHistory.slice(-1000);
        }
    }
    /**
     * 根据当前交易历史预测买点 - 优化版队列管理
     */
    async predictBuy() {
        // 检查队列状态
        const queueStatus = this.getQueueStatus();
        if (process.env.DEBUG_AI_PREDICTION === 'true') {
            console.log(`🔮 BuyPredictor预测开始:`);
            console.log(`   📋 队列状态: ${queueStatus.currentSize}/${queueStatus.maxSize} (${(queueStatus.utilizationRate * 100).toFixed(1)}%)`);
            console.log(`   ✅ 可预测: ${queueStatus.canPredict} (需要${this.windowSize}笔，实际${queueStatus.currentSize}笔)`);
        }
        if (!queueStatus.canPredict) {
            if (process.env.DEBUG_AI_PREDICTION === 'true') {
                console.log(`❌ 数据不足，无法预测: 当前${queueStatus.currentSize}笔，需要至少${this.windowSize}笔`);
            }
            return {
                probability: 0.0,
                prediction: false,
                confidence: 0.0,
                timestamp: new Date(),
                predictionTimeMs: 0,
                predictionTimeMicros: 0
            };
        }
        if (!this.isModelLoaded || !this.model) {
            if (process.env.DEBUG_AI_PREDICTION === 'true') {
                console.log(`❌ 模型未加载，返回默认预测`);
                console.log(`   📊 模型状态: isLoaded=${this.isModelLoaded}, model=${!!this.model}`);
            }
            return {
                probability: 0.0,
                prediction: false,
                confidence: 0.0,
                timestamp: new Date(),
                predictionTimeMs: 0,
                predictionTimeMicros: 0
            };
        }
        try {
            if (process.env.DEBUG_AI_PREDICTION === 'true') {
                console.log(`🔍 提取特征 (从${this.transactionHistory.length}笔交易中)...`);
            }
            // 提取特征（使用队列中的数据）
            const features = this.extractBuyFeatures(this.transactionHistory);
            if (!features) {
                if (process.env.DEBUG_AI_PREDICTION === 'true') {
                    console.log(`❌ 特征提取失败`);
                }
                return {
                    probability: 0.0,
                    prediction: false,
                    confidence: 0.0,
                    timestamp: new Date(),
                    predictionTimeMs: 0,
                    predictionTimeMicros: 0
                };
            }
            if (process.env.DEBUG_AI_PREDICTION === 'true') {
                console.log(`✅ 特征提取成功:`);
                console.log(`   📊 特征详情:`);
                console.log(`      💰 SOL金额: 均值=${features.sol_amount_mean.toFixed(6)}, 标准差=${features.sol_amount_std.toFixed(6)}`);
                console.log(`      💵 USD金额: 均值=${features.usd_amount_mean.toFixed(2)}, 标准差=${features.usd_amount_std.toFixed(2)}`);
                console.log(`      📈 交易统计: 买入=${features.buy_count}, 卖出=${features.sell_count}, 持有=${features.hold_count}`);
                console.log(`      🕐 时间特征: 小时=${features.hour_last}, 星期=${features.weekday_last}`);
            }
            // 准备特征数组（只包含数值特征，买点预测不需要分类特征）
            const numericalFeatures = [
                [
                    features.sol_amount_mean,
                    features.sol_amount_std,
                    features.usd_amount_mean,
                    features.usd_amount_std,
                    features.buy_count,
                    features.sell_count,
                    features.hold_count,
                    features.hour_last,
                    features.weekday_last
                ]
            ];
            if (process.env.DEBUG_AI_PREDICTION === 'true') {
                console.log(`🤖 准备模型输入:`);
                console.log(`   📊 特征向量: [${numericalFeatures[0].map(f => f.toFixed(4)).join(', ')}]`);
            }
            // 开始计时（高精度）
            const startTime = process.hrtime.bigint();
            // 进行预测 - 正确的API调用方式
            const categoricalFeatures = numericalFeatures.map(() => []); // 每个样本对应一个空的分类特征数组
            if (process.env.DEBUG_AI_PREDICTION === 'true') {
                console.log(`🔮 调用CatBoost模型预测...`);
            }
            const prediction = this.model.predict(numericalFeatures, categoricalFeatures);
            // 结束计时
            const endTime = process.hrtime.bigint();
            const predictionTimeNanos = Number(endTime - startTime);
            const predictionTimeMicros = predictionTimeNanos / 1000;
            const predictionTimeMs = predictionTimeMicros / 1000;
            if (process.env.DEBUG_AI_PREDICTION === 'true') {
                console.log(`✅ 模型预测完成 (耗时: ${predictionTimeMicros.toFixed(2)}μs)`);
                console.log(`   📊 原始预测输出: ${JSON.stringify(prediction)}`);
            }
            // 获取概率（CatBoost回归模型输出logit值，需要转换为概率）
            let probability = 0.0;
            if (Array.isArray(prediction) && prediction.length > 0) {
                let rawOutput = 0.0;
                if (Array.isArray(prediction[0])) {
                    // 多类或二分类概率输出
                    rawOutput = prediction[0][1] || prediction[0][0] || 0.0;
                    if (process.env.DEBUG_AI_PREDICTION === 'true') {
                        console.log(`   📊 概率解析: 多类输出 -> 取索引1: ${rawOutput}`);
                    }
                }
                else {
                    // 单一概率输出（logit值）
                    rawOutput = prediction[0];
                    if (process.env.DEBUG_AI_PREDICTION === 'true') {
                        console.log(`   📊 概率解析: 单值输出 (logit) -> ${rawOutput}`);
                    }
                }
                if (process.env.DEBUG_AI_PREDICTION === 'true') {
                    console.log(`   🔢 rawOutput值: ${rawOutput} (类型: ${typeof rawOutput})`);
                }
                // 应用sigmoid函数转换logit值为概率
                // sigmoid(x) = 1 / (1 + exp(-x))
                const sigmoidResult = 1 / (1 + Math.exp(-rawOutput));
                probability = sigmoidResult;
                if (process.env.DEBUG_AI_PREDICTION === 'true') {
                    console.log(`   🔄 Sigmoid转换详细过程:`);
                    console.log(`      输入logit: ${rawOutput.toFixed(6)}`);
                    console.log(`      Math.exp(-logit): ${Math.exp(-rawOutput).toFixed(6)}`);
                    console.log(`      1 + Math.exp(-logit): ${(1 + Math.exp(-rawOutput)).toFixed(6)}`);
                    console.log(`      sigmoid结果: ${sigmoidResult.toFixed(6)}`);
                    console.log(`      赋值后probability: ${probability.toFixed(6)}`);
                }
            }
            // 确保概率在[0,1]范围内（sigmoid函数输出自然在此范围内，但为了安全起见）
            const originalProbability = probability;
            probability = Math.max(0, Math.min(1, probability));
            if (Math.abs(originalProbability - probability) > 0.0001 && process.env.DEBUG_AI_PREDICTION === 'true') {
                console.log(`   ⚠️ 概率边界修正: ${originalProbability.toFixed(6)} -> ${probability.toFixed(6)}`);
            }
            const isPrediction = probability > this.threshold;
            const confidence = Math.abs(probability - 0.5) * 2; // 将概率转换为置信度
            if (process.env.DEBUG_AI_PREDICTION === 'true') {
                console.log(`🎯 买入预测最终结果:`);
                console.log(`   📈 概率: ${(probability * 100).toFixed(3)}%`);
                console.log(`   🎯 阈值: ${(this.threshold * 100).toFixed(1)}%`);
                console.log(`   ✅ 预测: ${isPrediction ? '买入' : '不买入'} (${probability > this.threshold ? '超过阈值' : '低于阈值'})`);
                console.log(`   📊 置信度: ${(confidence * 100).toFixed(1)}%`);
            }
            return {
                probability,
                prediction: isPrediction,
                confidence,
                timestamp: new Date(),
                predictionTimeMs,
                predictionTimeMicros
            };
        }
        catch (error) {
            console.error('❌ 买入预测错误:', error);
            console.error('   错误堆栈:', error.stack);
            return {
                probability: 0.0,
                prediction: false,
                confidence: 0.0,
                timestamp: new Date(),
                predictionTimeMs: 0,
                predictionTimeMicros: 0
            };
        }
    }
    /**
     * 批量预测（用于批处理场景）
     */
    async predictBuyBatch(transactions) {
        if (!this.isModelLoaded || !this.model) {
            console.warn('Model not loaded, returning default predictions');
            return transactions.map(() => ({
                probability: 0.0,
                prediction: false,
                confidence: 0.0,
                timestamp: new Date(),
                predictionTimeMs: 0,
                predictionTimeMicros: 0
            }));
        }
        const results = [];
        try {
            // 准备所有特征
            const allFeatures = [];
            const validIndices = [];
            for (let i = 0; i < transactions.length; i++) {
                const features = this.extractBuyFeatures(transactions[i]);
                if (features) {
                    allFeatures.push([
                        features.sol_amount_mean,
                        features.sol_amount_std,
                        features.usd_amount_mean,
                        features.usd_amount_std,
                        features.buy_count,
                        features.sell_count,
                        features.hold_count,
                        features.hour_last,
                        features.weekday_last
                    ]);
                    validIndices.push(i);
                }
            }
            if (allFeatures.length === 0) {
                return transactions.map(() => ({
                    probability: 0.0,
                    prediction: false,
                    confidence: 0.0,
                    timestamp: new Date(),
                    predictionTimeMs: 0,
                    predictionTimeMicros: 0
                }));
            }
            // 开始计时
            const startTime = process.hrtime.bigint();
            // 批量预测
            const categoricalFeatures = allFeatures.map(() => []); // 每个样本对应一个空的分类特征数组
            const predictions = this.model.predict(allFeatures, categoricalFeatures);
            // 结束计时
            const endTime = process.hrtime.bigint();
            const totalTimeNanos = Number(endTime - startTime);
            const avgTimeMicros = (totalTimeNanos / 1000) / allFeatures.length;
            const avgTimeMs = avgTimeMicros / 1000;
            // 处理预测结果
            let predictionIndex = 0;
            for (let i = 0; i < transactions.length; i++) {
                if (validIndices.includes(i)) {
                    let probability = 0.0;
                    if (Array.isArray(predictions) && predictions.length > predictionIndex) {
                        let rawOutput = 0.0;
                        if (Array.isArray(predictions[predictionIndex])) {
                            rawOutput = predictions[predictionIndex][1] || predictions[predictionIndex][0] || 0.0;
                        }
                        else {
                            rawOutput = predictions[predictionIndex];
                        }
                        // 应用sigmoid函数转换logit值为概率
                        probability = 1 / (1 + Math.exp(-rawOutput));
                    }
                    probability = Math.max(0, Math.min(1, probability));
                    const isPrediction = probability > this.threshold;
                    const confidence = Math.abs(probability - 0.5) * 2;
                    results.push({
                        probability,
                        prediction: isPrediction,
                        confidence,
                        timestamp: new Date(),
                        predictionTimeMs: avgTimeMs,
                        predictionTimeMicros: avgTimeMicros
                    });
                    predictionIndex++;
                }
                else {
                    results.push({
                        probability: 0.0,
                        prediction: false,
                        confidence: 0.0,
                        timestamp: new Date(),
                        predictionTimeMs: 0,
                        predictionTimeMicros: 0
                    });
                }
            }
            console.log(`Buy batch prediction completed: ${allFeatures.length} samples, avg time=${avgTimeMicros.toFixed(2)}μs per prediction`);
        }
        catch (error) {
            console.error('Buy batch prediction error:', error);
            return transactions.map(() => ({
                probability: 0.0,
                prediction: false,
                confidence: 0.0,
                timestamp: new Date(),
                predictionTimeMs: 0,
                predictionTimeMicros: 0
            }));
        }
        return results;
    }
    /**
     * 清除交易历史
     */
    clearHistory() {
        this.transactionHistory = [];
    }
    /**
     * 获取当前交易历史长度
     */
    getHistoryLength() {
        return this.transactionHistory.length;
    }
    /**
     * 设置预测阈值
     */
    setThreshold(threshold) {
        this.threshold = threshold;
    }
    /**
     * 获取最近的特征数据（用于调试）
     */
    getLatestFeatures() {
        if (this.transactionHistory.length < this.windowSize) {
            return null;
        }
        return this.extractBuyFeatures(this.transactionHistory);
    }
    /**
     * 检查模型状态
     */
    isReady() {
        return this.isModelLoaded;
    }
    /**
     * 重新加载模型
     */
    reloadModel() {
        try {
            this.initializeModel();
            return this.isModelLoaded;
        }
        catch (error) {
            console.error('Failed to reload model:', error);
            return false;
        }
    }
    /**
     * 获取模型信息
     */
    getModelInfo() {
        return {
            modelPath: this.modelPath,
            isLoaded: this.isModelLoaded,
            windowSize: this.windowSize,
            threshold: this.threshold,
            historyLength: this.transactionHistory.length
        };
    }
    /**
     * 获取队列状态 - 为队列管理添加支持
     */
    getQueueStatus() {
        const currentSize = this.transactionHistory.length;
        const maxSize = 1000; // 默认最大队列大小
        const minForPrediction = this.minDataForPrediction;
        return {
            currentSize,
            maxSize,
            utilizationRate: currentSize / maxSize,
            canPredict: currentSize >= minForPrediction,
            neededForPrediction: Math.max(0, minForPrediction - currentSize)
        };
    }
    /**
     * 批量添加交易数据 - 为队列管理添加支持
     */
    addTransactionBatch(transactions) {
        if (transactions.length === 0)
            return;
        // 批量添加
        this.transactionHistory.push(...transactions);
        // 队列管理 - 保持在最大长度内
        if (this.transactionHistory.length > 1000) {
            const excess = this.transactionHistory.length - 1000;
            this.transactionHistory = this.transactionHistory.slice(excess);
        }
    }
}
exports.BuyPredictor = BuyPredictor;
