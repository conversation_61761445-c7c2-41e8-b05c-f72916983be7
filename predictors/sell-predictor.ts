import * as catboost from 'catboost';
import * as fs from 'fs';
import * as path from 'path';

export interface SellFeatures {
  sol_amount_mean: number;
  sol_amount_std: number;
  usd_amount_mean: number;
  usd_amount_std: number;
  buy_count: number;
  sell_count: number;
  hold_count: number;
  hour_last: number;
  weekday_last: number;
  last_buy_distance: number;
  buy_first_from_last: number;
  last_buy_sol_amount: number;
  last_buy_usd_amount: number;
  last_buy_time_diff: number;
  buy_sol_sum: number;
  buy_sol_mean: number;
  buy_sol_max: number;
  buy_usd_sum: number;
  buy_usd_mean: number;
  buy_usd_max: number;
  buy_last_from_last: number;
  buy_center_gravity: number;
  sell_first_from_last: number;
  sell_last_from_last: number;
  sell_center_gravity: number;
  sol_amount_slope: number;
  sol_amount_ratio: number;
  usd_amount_slope: number;
  usd_amount_ratio: number;
  sol_amount_volatility: number;
  usd_amount_volatility: number;
  action_alternate_count: number;
  max_buy_streak: number;
  max_sell_streak: number;
  time_interval_mean: number;
  time_interval_std: number;
  buy_ratio: number;
  sell_ratio: number;
  hold_ratio: number;
  sol_amount_entropy: number;
  usd_amount_entropy: number;
  sol_amount_proportion: number;
  usd_amount_proportion: number;
  buy_front_half: number;
  buy_back_half: number;
  sell_front_half: number;
  sell_back_half: number;
  sol_amount_max_rise: number;
  sol_amount_max_drop: number;
  sol_amount_max_drawdown: number;
  usd_amount_max_rise: number;
  usd_amount_max_drop: number;
  usd_amount_max_drawdown: number;
  buy2sell_sol_diff_mean: number;
  buy2sell_usd_diff_mean: number;
  action_last_1: number;
  sol_amount_last_1: number;
  usd_amount_last_1: number;
  action_last_2: number;
  sol_amount_last_2: number;
  usd_amount_last_2: number;
  action_last_3: number;
  sol_amount_last_3: number;
  usd_amount_last_3: number;
}

export interface SellPredictionResult {
  probability: number;
  prediction: boolean;
  confidence: number;
  timestamp: Date;
  predictionTimeMs: number; // 预测耗时（毫秒）
  predictionTimeMicros: number; // 预测耗时（微秒）
}

export interface TransactionData {
  timestamp: Date;
  action: number; // 0: sell, 1: buy, 2: hold
  sol_amount: number;
  usd_amount: number;
  is_target_wallet: boolean;
  wallet: string;
  block_number: number;
}

export class SellPredictor {
  private model: any;
  private modelPath: string;
  private windowSize: number;
  private threshold: number;
  private transactionHistory: TransactionData[] = [];
  private isModelLoaded: boolean = false;

  // 队列管理配置
  private maxHistorySize: number;
  private minDataForPrediction: number;
  
  // 🔥 新增：特征缓存机制
  private cachedFeatures: SellFeatures | null = null;
  private lastFeatureCalculationHash: string = '';
  private featureCacheEnabled: boolean = true;

  constructor(
    modelPath: string = '../best_catboost_sell_model.cbm',
    windowSize: number = 10, // 🔥 通过AI_CONFIG.featureWindowSize控制
    threshold: number = 0.7,
    maxHistorySize: number = 50  // 卖出预测需要更多数据，默认保持最近150笔交易
  ) {
    // 如果是相对路径，则相对于项目根目录解析
    if (modelPath.startsWith('./') || modelPath.startsWith('../')) {
      this.modelPath = path.resolve(__dirname, '../', modelPath.replace('./', ''));
    } else {
      this.modelPath = path.resolve(modelPath);
    }
    this.windowSize = windowSize;
    this.threshold = threshold;
    this.maxHistorySize = maxHistorySize;
    this.minDataForPrediction = 10; // 🔥 修改：统一设置为最少10笔数据
    
    console.log(`🤖 SellPredictor初始化: 窗口=${windowSize}, 阈值=${(threshold*100).toFixed(1)}%, 最小数据=${this.minDataForPrediction}笔`);
    this.initializeModel();
  }

  private initializeModel(): void {
    try {
      if (!fs.existsSync(this.modelPath)) {
        console.warn(`Model file not found: ${this.modelPath}. Predictions will return default values.`);
        return;
      }

      this.model = new catboost.Model();
      this.model.loadModel(this.modelPath);
      this.isModelLoaded = true;
      console.log(`Sell model loaded successfully from: ${this.modelPath}`);
    } catch (error) {
      console.error(`Failed to load sell model: ${error}`);
      this.isModelLoaded = false;
    }
  }

  /**
   * 🔥 计算数据哈希值用于缓存判断
   */
  private calculateDataHash(transactions: TransactionData[]): string {
    if (transactions.length === 0) return '';
    
    // 使用最后几笔交易的关键信息生成哈希
    const lastFew = transactions.slice(-5);
    const hashData = lastFew.map(t => 
      `${t.timestamp.getTime()}-${t.action}-${t.sol_amount.toFixed(6)}-${t.usd_amount.toFixed(2)}`
    ).join('|');
    
    return hashData + `|len:${transactions.length}`;
  }

  /**
   * 提取卖点预测特征（完整的64个特征）- 带缓存优化
   */
  private extractSellFeatures(transactions: TransactionData[]): SellFeatures | null {
    if (transactions.length < this.minDataForPrediction) {
      console.log(`⚠️ 卖出特征提取：数据不足 (需要${this.minDataForPrediction}笔，实际${transactions.length}笔)`);
      return null;
    }

    // 🔥 检查缓存
    if (this.featureCacheEnabled) {
      const currentHash = this.calculateDataHash(transactions);
      if (currentHash === this.lastFeatureCalculationHash && this.cachedFeatures) {
        if (process.env.DEBUG_PREDICTION_PERFORMANCE === 'true') {
          console.log(`🚀 使用缓存的卖出特征 (哈希: ${currentHash.slice(0, 20)}...)`);
        }
        return this.cachedFeatures;
      }
      this.lastFeatureCalculationHash = currentHash;
    }

    // 🔥 修复：使用所有可用数据，而不是只取最近的windowSize笔
    // 这样确保新数据能够影响预测结果，与买入预测器保持一致
    const availableData = transactions; // 使用所有数据
    
    // 🔥 只有在数据真的不足时才填充，而且填充数量要合理
    let windowData = [...availableData];
    
    // 只有在数据少于最小窗口大小时才填充
    const targetSize = Math.max(this.windowSize, availableData.length);
    
    // 如果实际数据少于窗口大小，用默认值填充前面的部分
    while (windowData.length < this.windowSize) {
      const defaultTransaction: TransactionData = {
        timestamp: new Date(Date.now() - (this.windowSize - windowData.length) * 60000),
        action: 2, // hold
        sol_amount: 0.001, // 小额默认值
        usd_amount: 0.15,
        is_target_wallet: false,
        wallet: '',
        block_number: 0
      };
      windowData.unshift(defaultTransaction);
    }
    
    const featureExtractionStart = performance.now();
    console.log(`📊 卖出特征提取: 原始数据${transactions.length}笔, 使用数据${this.windowSize}笔, 填充${this.windowSize - Math.min(transactions.length, this.windowSize)}笔`);
    
    // 提取基础数据
    const actions = windowData.map(t => t.action);
    const solAmounts = windowData.map(t => t.sol_amount);
    const usdAmounts = windowData.map(t => t.usd_amount);
    const timestamps = windowData.map(t => t.timestamp);
    
    // 基础统计特征
    const solAmountMean = solAmounts.reduce((a, b) => a + b, 0) / solAmounts.length;
    const usdAmountMean = usdAmounts.reduce((a, b) => a + b, 0) / usdAmounts.length;
    const solAmountStd = Math.sqrt(
      solAmounts.reduce((sum, val) => sum + Math.pow(val - solAmountMean, 2), 0) / solAmounts.length
    );
    const usdAmountStd = Math.sqrt(
      usdAmounts.reduce((sum, val) => sum + Math.pow(val - usdAmountMean, 2), 0) / usdAmounts.length
    );

    const buyCount = actions.filter(a => a === 1).length;
    const sellCount = actions.filter(a => a === 0).length;
    const holdCount = actions.filter(a => a === 2).length;
    const lastTimestamp = timestamps[timestamps.length - 1];

    // 买卖操作相关特征
    const buyIndices = actions.map((action, index) => action === 1 ? index : -1).filter(i => i !== -1);
    const sellIndices = actions.map((action, index) => action === 0 ? index : -1).filter(i => i !== -1);

    // 买入相关特征
    let buyFeatures: any = {};
    if (buyIndices.length > 0) {
      const lastBuyIdx = buyIndices[buyIndices.length - 1];
      const firstBuyIdx = buyIndices[0];
      buyFeatures = {
        last_buy_distance: this.windowSize - (lastBuyIdx + 1),
        buy_first_from_last: buyIndices.length > 1 ? lastBuyIdx - firstBuyIdx : 0,
        last_buy_sol_amount: solAmounts[lastBuyIdx],
        last_buy_usd_amount: usdAmounts[lastBuyIdx],
        last_buy_time_diff: (lastTimestamp.getTime() - timestamps[lastBuyIdx].getTime()) / 1000,
        buy_sol_sum: buyIndices.reduce((sum, i) => sum + solAmounts[i], 0),
        buy_sol_mean: buyIndices.reduce((sum, i) => sum + solAmounts[i], 0) / buyIndices.length,
        buy_sol_max: Math.max(...buyIndices.map(i => solAmounts[i])),
        buy_usd_sum: buyIndices.reduce((sum, i) => sum + usdAmounts[i], 0),
        buy_usd_mean: buyIndices.reduce((sum, i) => sum + usdAmounts[i], 0) / buyIndices.length,
        buy_usd_max: Math.max(...buyIndices.map(i => usdAmounts[i])),
        buy_last_from_last: 0,
        buy_center_gravity: buyIndices.reduce((sum, i) => sum + i, 0) / buyIndices.length
      };
    } else {
      buyFeatures = {
        last_buy_distance: -1, buy_first_from_last: -1, last_buy_sol_amount: 0.0,
        last_buy_usd_amount: 0.0, last_buy_time_diff: -1.0, buy_sol_sum: 0.0,
        buy_sol_mean: 0.0, buy_sol_max: 0.0, buy_usd_sum: 0.0, buy_usd_mean: 0.0,
        buy_usd_max: 0.0, buy_last_from_last: -1, buy_center_gravity: -1
      };
    }

    // 卖出相关特征
    let sellFeatures: any = {};
    if (sellIndices.length > 0) {
      const firstSellIdx = sellIndices[0];
      const lastSellIdx = sellIndices[sellIndices.length - 1];
      sellFeatures = {
        sell_first_from_last: sellIndices.length > 1 ? lastSellIdx - firstSellIdx : 0,
        sell_last_from_last: 0,
        sell_center_gravity: sellIndices.reduce((sum, i) => sum + i, 0) / sellIndices.length
      };
    } else {
      sellFeatures = {
        sell_first_from_last: -1, sell_last_from_last: -1, sell_center_gravity: -1
      };
    }

    // 🔥 优化：简化趋势特征计算
    let trendFeatures: any = {};
    if (solAmounts.length > 1) {
      // 使用简单的首尾比较代替复杂的线性回归
      const firstSol = solAmounts[0];
      const lastSol = solAmounts[solAmounts.length - 1];
      const firstUsd = usdAmounts[0];
      const lastUsd = usdAmounts[usdAmounts.length - 1];
      
      trendFeatures.sol_amount_slope = lastSol - firstSol; // 简化斜率为差值
      trendFeatures.sol_amount_ratio = firstSol !== 0 ? lastSol / firstSol : 0.0;
      trendFeatures.usd_amount_slope = lastUsd - firstUsd; // 简化斜率为差值
      trendFeatures.usd_amount_ratio = firstUsd !== 0 ? lastUsd / firstUsd : 0.0;
    } else {
      trendFeatures = {sol_amount_slope: 0, sol_amount_ratio: 0, usd_amount_slope: 0, usd_amount_ratio: 0};
    }

    // 波动性特征
    const solAmountVolatility = solAmountMean !== 0 ? solAmountStd / solAmountMean : 0.0;
    const usdAmountVolatility = usdAmountMean !== 0 ? usdAmountStd / usdAmountMean : 0.0;

    // 动作模式特征
    const actionAlternateCount = actions.slice(0, -1).reduce((count, action, i) => 
      action !== actions[i + 1] ? count + 1 : count, 0);

    // 连续性特征
    let buyStreak = 0, sellStreak = 0;
    for (const action of actions) {
      if (action === 1) {
        buyStreak++;
        sellStreak = 0;
      } else if (action === 0) {
        sellStreak++;
        buyStreak = 0;
      }
    }

    // 时间间隔特征
    let timeFeatures: any = {};
    if (timestamps.length > 1) {
      const intervals = timestamps.slice(1).map((ts, i) => (ts.getTime() - timestamps[i].getTime()) / 1000);
      const intervalMean = intervals.reduce((a, b) => a + b, 0) / intervals.length;
      const intervalStd = Math.sqrt(
        intervals.reduce((sum, val) => sum + Math.pow(val - intervalMean, 2), 0) / intervals.length
      );
      timeFeatures = {
        time_interval_mean: intervalMean,
        time_interval_std: intervalStd
      };
    } else {
      timeFeatures = {time_interval_mean: 0.0, time_interval_std: 0.0};
    }

    // 比例特征
    const buyRatio = actions.filter(a => a === 1).length / actions.length;
    const sellRatio = actions.filter(a => a === 0).length / actions.length;
    const holdRatio = actions.filter(a => a === 2).length / actions.length;

    // 🔥 优化：简化熵特征计算
    let entropyFeatures: any = {};
    const solSum = solAmounts.reduce((a, b) => a + Math.abs(b), 0);
    const usdSum = usdAmounts.reduce((a, b) => a + Math.abs(b), 0);
    
    // 使用简化的方差代替复杂的熵计算
    entropyFeatures.sol_amount_entropy = solSum !== 0 ? solAmountStd / solSum : 0.0;
    entropyFeatures.usd_amount_entropy = usdSum !== 0 ? usdAmountStd / usdSum : 0.0;

    // 比例特征
    const solAbsSum = solAmounts.reduce((a, b) => a + Math.abs(b), 0);
    const usdAbsSum = usdAmounts.reduce((a, b) => a + Math.abs(b), 0);
    const solAmountProportion = solAbsSum !== 0 ? solAmounts.reduce((a, b) => a + b, 0) / solAbsSum : 0.0;
    const usdAmountProportion = usdAbsSum !== 0 ? usdAmounts.reduce((a, b) => a + b, 0) / usdAbsSum : 0.0;

    // 前后半段特征
    const halfPoint = Math.floor(this.windowSize / 2);
    const buyFrontHalf = actions.slice(0, halfPoint).filter(a => a === 1).length;
    const buyBackHalf = actions.slice(halfPoint).filter(a => a === 1).length;
    const sellFrontHalf = actions.slice(0, halfPoint).filter(a => a === 0).length;
    const sellBackHalf = actions.slice(halfPoint).filter(a => a === 0).length;

    // 🔥 优化：简化最大上升/下降/回撤特征计算
    const solMax = Math.max(...solAmounts);
    const solMin = Math.min(...solAmounts);
    const usdMax = Math.max(...usdAmounts);
    const usdMin = Math.min(...usdAmounts);
    
    const solAmountMaxRise = solMax - solAmounts[0];
    const solAmountMaxDrop = solAmounts[0] - solMin;
    const solAmountMaxDrawdown = solMax - solMin; // 简化回撤计算
    
    const usdAmountMaxRise = usdMax - usdAmounts[0];
    const usdAmountMaxDrop = usdAmounts[0] - usdMin;
    const usdAmountMaxDrawdown = usdMax - usdMin; // 简化回撤计算

    // 买卖差异特征
    let buyToSellFeatures: any = {};
    if (buyIndices.length > 0 && sellIndices.length > 0) {
      const buyMeanSol = buyIndices.reduce((sum, i) => sum + solAmounts[i], 0) / buyIndices.length;
      const sellMeanSol = sellIndices.reduce((sum, i) => sum + solAmounts[i], 0) / sellIndices.length;
      const buyMeanUsd = buyIndices.reduce((sum, i) => sum + usdAmounts[i], 0) / buyIndices.length;
      const sellMeanUsd = sellIndices.reduce((sum, i) => sum + usdAmounts[i], 0) / sellIndices.length;
      
      buyToSellFeatures = {
        buy2sell_sol_diff_mean: buyMeanSol - sellMeanSol,
        buy2sell_usd_diff_mean: buyMeanUsd - sellMeanUsd
      };
    } else {
      buyToSellFeatures = {buy2sell_sol_diff_mean: 0.0, buy2sell_usd_diff_mean: 0.0};
    }

    // 最近N个交易特征
    let lastNFeatures: any = {};
    if (actions.length >= 3) {
      lastNFeatures = {
        action_last_1: actions[actions.length - 1],
        sol_amount_last_1: solAmounts[solAmounts.length - 1],
        usd_amount_last_1: usdAmounts[usdAmounts.length - 1],
        action_last_2: actions[actions.length - 2],
        sol_amount_last_2: solAmounts[solAmounts.length - 2],
        usd_amount_last_2: usdAmounts[usdAmounts.length - 2],
        action_last_3: actions[actions.length - 3],
        sol_amount_last_3: solAmounts[solAmounts.length - 3],
        usd_amount_last_3: usdAmounts[usdAmounts.length - 3]
      };
    } else {
      lastNFeatures = {
        action_last_1: 0, sol_amount_last_1: 0, usd_amount_last_1: 0,
        action_last_2: 0, sol_amount_last_2: 0, usd_amount_last_2: 0,
        action_last_3: 0, sol_amount_last_3: 0, usd_amount_last_3: 0
      };
    }

    // 🔥 构建特征对象
    const features: SellFeatures = {
      sol_amount_mean: solAmountMean,
      sol_amount_std: solAmountStd,
      usd_amount_mean: usdAmountMean,
      usd_amount_std: usdAmountStd,
      buy_count: buyCount,
      sell_count: sellCount,
      hold_count: holdCount,
      hour_last: lastTimestamp.getHours(),
      weekday_last: lastTimestamp.getDay(),
      ...buyFeatures,
      ...sellFeatures,
      ...trendFeatures,
      sol_amount_volatility: solAmountVolatility,
      usd_amount_volatility: usdAmountVolatility,
      action_alternate_count: actionAlternateCount,
      max_buy_streak: buyStreak,
      max_sell_streak: sellStreak,
      ...timeFeatures,
      buy_ratio: buyRatio,
      sell_ratio: sellRatio,
      hold_ratio: holdRatio,
      ...entropyFeatures,
      sol_amount_proportion: solAmountProportion,
      usd_amount_proportion: usdAmountProportion,
      buy_front_half: buyFrontHalf,
      buy_back_half: buyBackHalf,
      sell_front_half: sellFrontHalf,
      sell_back_half: sellBackHalf,
      sol_amount_max_rise: solAmountMaxRise,
      sol_amount_max_drop: solAmountMaxDrop,
      sol_amount_max_drawdown: solAmountMaxDrawdown,
      usd_amount_max_rise: usdAmountMaxRise,
      usd_amount_max_drop: usdAmountMaxDrop,
      usd_amount_max_drawdown: usdAmountMaxDrawdown,
      ...buyToSellFeatures,
      ...lastNFeatures
    };

    // 🔥 保存到缓存
    if (this.featureCacheEnabled) {
      this.cachedFeatures = features;
      if (process.env.DEBUG_PREDICTION_PERFORMANCE === 'true') {
        console.log(`💾 卖出特征已缓存 (哈希: ${this.lastFeatureCalculationHash.slice(0, 20)}...)`);
      }
    }

    const featureExtractionTime = performance.now() - featureExtractionStart;
    if (process.env.DEBUG_PREDICTION_PERFORMANCE === 'true') {
      console.log(`⚡ 特征提取耗时: ${featureExtractionTime.toFixed(3)}ms`);
    }

    return features;
  }

  /**
   * 添加新的交易数据 - 优化版队列管理
   */
  public addTransaction(transaction: TransactionData): void {
    // 添加新交易到队列尾部
    this.transactionHistory.push(transaction);
    
    // 🔥 清除特征缓存（因为数据已变化）
    this.invalidateFeatureCache();
    
    // 队列超出最大长度时，移除最老的数据（FIFO）
    if (this.transactionHistory.length > this.maxHistorySize) {
      // 使用shift()移除第一个元素（最老的数据）
      const removedCount = this.transactionHistory.length - this.maxHistorySize;
      this.transactionHistory.splice(0, removedCount);
      
      // 队列清理完成（静默处理）
    }
  }

  /**
   * 🔥 清除特征缓存
   */
  private invalidateFeatureCache(): void {
    this.cachedFeatures = null;
    this.lastFeatureCalculationHash = '';
  }

  /**
   * 批量添加交易数据 - 优化版
   */
  public addTransactionBatch(transactions: TransactionData[]): void {
    if (transactions.length === 0) return;
    
    // 批量添加
    this.transactionHistory.push(...transactions);
    
    // 🔥 清除特征缓存（因为数据已变化）
    this.invalidateFeatureCache();
    
    // 队列管理
    if (this.transactionHistory.length > this.maxHistorySize) {
      const removedCount = this.transactionHistory.length - this.maxHistorySize;
      this.transactionHistory.splice(0, removedCount);
      
      // 批量队列清理完成（静默处理）
    }
  }

  /**
   * 获取队列状态信息
   */
  public getQueueStatus(): {
    currentSize: number;
    maxSize: number;
    utilizationRate: number;
    canPredict: boolean;
    neededForPrediction: number;
  } {
    const currentSize = this.transactionHistory.length;
    const utilizationRate = currentSize / this.maxHistorySize;
    const canPredict = currentSize >= this.minDataForPrediction;
    const neededForPrediction = Math.max(0, this.minDataForPrediction - currentSize);
    
    return {
      currentSize,
      maxSize: this.maxHistorySize,
      utilizationRate,
      canPredict,
      neededForPrediction
    };
  }

  /**
   * 根据当前交易历史预测卖点
   */
  public async predictSell(): Promise<SellPredictionResult> {
    if (process.env.DEBUG_AI_PREDICTION === 'true') {
      console.log(`🔮 SellPredictor预测开始:`);
      console.log(`   📋 队列状态: ${this.transactionHistory.length}/${this.maxHistorySize} (${(this.transactionHistory.length/this.maxHistorySize*100).toFixed(1)}%)`);
      console.log(`   ✅ 可预测: ${this.transactionHistory.length >= this.minDataForPrediction} (需要${this.minDataForPrediction}笔，实际${this.transactionHistory.length}笔)`);
    }

    if (this.transactionHistory.length < this.minDataForPrediction) {
      if (process.env.DEBUG_AI_PREDICTION === 'true') {
        console.log(`⚠️ 数据不足，跳过卖出预测 (需要${this.minDataForPrediction}笔，实际${this.transactionHistory.length}笔)`);
      }
      return {
        probability: 0.0,
        prediction: false,
        confidence: 0.0,
        timestamp: new Date(),
        predictionTimeMs: 0,
        predictionTimeMicros: 0
      };
    }

    if (!this.isModelLoaded || !this.model) {
      if (process.env.DEBUG_AI_PREDICTION === 'true') {
        console.warn('卖出模型未加载，返回默认预测结果');
      }
      return {
        probability: 0.0,
        prediction: false,
        confidence: 0.0,
        timestamp: new Date(),
        predictionTimeMs: 0,
        predictionTimeMicros: 0
      };
    }

    try {
      if (process.env.DEBUG_AI_PREDICTION === 'true') {
        console.log(`🔍 提取卖出特征 (从${this.transactionHistory.length}笔交易中)...`);
      }
      
      // 🔥 使用优化的特征提取
      const features = this.extractOptimizedSellFeatures(this.transactionHistory);
      if (!features) {
        if (process.env.DEBUG_AI_PREDICTION === 'true') {
          console.log(`❌ 特征提取失败`);
        }
        return {
          probability: 0.0,
          prediction: false,
          confidence: 0.0,
          timestamp: new Date(),
          predictionTimeMs: 0,
          predictionTimeMicros: 0
        };
      }

      if (process.env.DEBUG_AI_PREDICTION === 'true') {
        console.log(`✅ 卖出特征提取成功:`);
        console.log(`   📊 特征详情:`);
        console.log(`      💰 SOL金额: 均值=${features.sol_amount_mean.toFixed(6)}, 标准差=${features.sol_amount_std.toFixed(6)}`);
        console.log(`      💵 USD金额: 均值=${features.usd_amount_mean.toFixed(2)}, 标准差=${features.usd_amount_std.toFixed(2)}`);
        console.log(`      📈 交易统计: 买入=${features.buy_count}, 卖出=${features.sell_count}, 持有=${features.hold_count}`);
        console.log(`      🕐 时间特征: 小时=${features.hour_last}, 星期=${features.weekday_last}`);
        console.log(`      🔍 买入距离: 最后买入距离=${features.last_buy_distance}`);
        console.log(`      💲 最后买入: SOL=${features.last_buy_sol_amount.toFixed(4)}, USD=${features.last_buy_usd_amount.toFixed(2)}`);
      }

      // 准备特征数组（64个特征，按顺序排列）
      const numericalFeatures = [
        [
          features.sol_amount_mean,
          features.sol_amount_std,
          features.usd_amount_mean,
          features.usd_amount_std,
          features.buy_count,
          features.sell_count,
          features.hold_count,
          features.hour_last,
          features.weekday_last,
          features.last_buy_distance,
          features.buy_first_from_last,
          features.last_buy_sol_amount,
          features.last_buy_usd_amount,
          features.last_buy_time_diff,
          features.buy_sol_sum,
          features.buy_sol_mean,
          features.buy_sol_max,
          features.buy_usd_sum,
          features.buy_usd_mean,
          features.buy_usd_max,
          features.buy_last_from_last,
          features.buy_center_gravity,
          features.sell_first_from_last,
          features.sell_last_from_last,
          features.sell_center_gravity,
          features.sol_amount_slope,
          features.sol_amount_ratio,
          features.usd_amount_slope,
          features.usd_amount_ratio,
          features.sol_amount_volatility,
          features.usd_amount_volatility,
          features.action_alternate_count,
          features.max_buy_streak,
          features.max_sell_streak,
          features.time_interval_mean,
          features.time_interval_std,
          features.buy_ratio,
          features.sell_ratio,
          features.hold_ratio,
          features.sol_amount_entropy,
          features.usd_amount_entropy,
          features.sol_amount_proportion,
          features.usd_amount_proportion,
          features.buy_front_half,
          features.buy_back_half,
          features.sell_front_half,
          features.sell_back_half,
          features.sol_amount_max_rise,
          features.sol_amount_max_drop,
          features.sol_amount_max_drawdown,
          features.usd_amount_max_rise,
          features.usd_amount_max_drop,
          features.usd_amount_max_drawdown,
          features.buy2sell_sol_diff_mean,
          features.buy2sell_usd_diff_mean,
          features.action_last_1,
          features.sol_amount_last_1,
          features.usd_amount_last_1,
          features.action_last_2,
          features.sol_amount_last_2,
          features.usd_amount_last_2,
          features.action_last_3,
          features.sol_amount_last_3,
          features.usd_amount_last_3
        ]
      ];

      if (process.env.DEBUG_AI_PREDICTION === 'true') {
        console.log(`🤖 准备卖出模型输入:`);
        console.log(`   📊 特征向量长度: ${numericalFeatures[0].length} 个特征`);
        console.log(`   📊 特征向量前10个: [${numericalFeatures[0].slice(0, 10).map(f => f.toFixed(4)).join(', ')}]`);
      }

      // 开始计时（高精度）
      const startTime = process.hrtime.bigint();

      if (process.env.DEBUG_AI_PREDICTION === 'true') {
        console.log(`🔮 调用CatBoost卖出模型预测...`);
      }
      // 进行预测 - 正确的API调用方式
      const categoricalFeatures = numericalFeatures.map(() => []); // 每个样本对应一个空的分类特征数组
      const prediction = this.model.predict(numericalFeatures, categoricalFeatures);

      // 结束计时
      const endTime = process.hrtime.bigint();
      const predictionTimeNanos = Number(endTime - startTime);
      const predictionTimeMicros = predictionTimeNanos / 1000;
      const predictionTimeMs = predictionTimeMicros / 1000;

      if (process.env.DEBUG_AI_PREDICTION === 'true') {
        console.log(`✅ 卖出模型预测完成 (耗时: ${predictionTimeMicros.toFixed(2)}μs)`);
        console.log(`   📊 原始预测输出: ${JSON.stringify(prediction)}`);
      }

      // 获取概率（CatBoost回归模型输出logit值，需要转换为概率）
      let probability = 0.0;
      if (Array.isArray(prediction) && prediction.length > 0) {
        let rawOutput = 0.0;
        if (Array.isArray(prediction[0])) {
          // 多类或二分类概率输出
          rawOutput = prediction[0][1] || prediction[0][0] || 0.0;
          if (process.env.DEBUG_AI_PREDICTION === 'true') {
            console.log(`   📊 概率解析: 多类输出 -> 取索引1: ${rawOutput}`);
          }
        } else {
          // 单一概率输出（logit值）
          rawOutput = prediction[0];
          if (process.env.DEBUG_AI_PREDICTION === 'true') {
            console.log(`   📊 概率解析: 单值输出 (logit) -> ${rawOutput}`);
          }
        }
        
        if (process.env.DEBUG_AI_PREDICTION === 'true') {
          console.log(`   🔢 rawOutput值: ${rawOutput} (类型: ${typeof rawOutput})`);
        }
        
        // 应用sigmoid函数转换logit值为概率
        // sigmoid(x) = 1 / (1 + exp(-x))
        const sigmoidResult = 1 / (1 + Math.exp(-rawOutput));
        probability = sigmoidResult;
        if (process.env.DEBUG_AI_PREDICTION === 'true') {
          console.log(`   🔄 Sigmoid转换详细过程:`);
          console.log(`      输入logit: ${rawOutput.toFixed(6)}`);
          console.log(`      Math.exp(-logit): ${Math.exp(-rawOutput).toFixed(6)}`);
          console.log(`      1 + Math.exp(-logit): ${(1 + Math.exp(-rawOutput)).toFixed(6)}`);
          console.log(`      sigmoid结果: ${sigmoidResult.toFixed(6)}`);
          console.log(`      赋值后probability: ${probability.toFixed(6)}`);
        }
      }

      // 确保概率在[0,1]范围内（sigmoid函数输出自然在此范围内，但为了安全起见）
      const originalProbability = probability;
      probability = Math.max(0, Math.min(1, probability));
      if (Math.abs(originalProbability - probability) > 0.0001 && process.env.DEBUG_AI_PREDICTION === 'true') {
        console.log(`   ⚠️ 概率边界修正: ${originalProbability.toFixed(6)} -> ${probability.toFixed(6)}`);
      }

      const isPrediction = probability > this.threshold;
      const confidence = Math.abs(probability - 0.5) * 2; // 将概率转换为置信度

      if (process.env.DEBUG_AI_PREDICTION === 'true') {
        console.log(`🎯 卖出预测最终结果:`);
        console.log(`   📉 概率: ${(probability*100).toFixed(3)}%`);
        console.log(`   🎯 阈值: ${(this.threshold*100).toFixed(1)}%`);
        console.log(`   ✅ 预测: ${isPrediction ? '卖出' : '不卖出'} (${isPrediction ? '超过' : '低于'}阈值)`);
        console.log(`   📊 置信度: ${(confidence*100).toFixed(1)}%`);
      }

      return {
        probability,
        prediction: isPrediction,
        confidence,
        timestamp: new Date(),
        predictionTimeMs,
        predictionTimeMicros
      };

    } catch (error) {
      console.error('❌ 卖出预测错误:', error);
      console.error(`   错误详情:`, error.stack);
      return {
        probability: 0.0,
        prediction: false,
        confidence: 0.0,
        timestamp: new Date(),
        predictionTimeMs: 0,
        predictionTimeMicros: 0
      };
    }
  }

  /**
   * 清除交易历史
   */
  public clearHistory(): void {
    this.transactionHistory = [];
  }

  /**
   * 获取当前交易历史长度
   */
  public getHistoryLength(): number {
    return this.transactionHistory.length;
  }

  /**
   * 设置预测阈值
   */
  public setThreshold(threshold: number): void {
    this.threshold = threshold;
  }

  /**
   * 与买点预测器共享交易历史
   */
  public syncTransactionHistory(transactions: TransactionData[]): void {
    this.transactionHistory = [...transactions];
  }

  /**
   * 检查模型状态
   */
  public isReady(): boolean {
    return this.isModelLoaded;
  }

  /**
   * 重新加载模型
   */
  public reloadModel(): boolean {
    try {
      this.initializeModel();
      return this.isModelLoaded;
    } catch (error) {
      console.error('Failed to reload model:', error);
      return false;
    }
  }

  /**
   * 获取模型信息
   */
  public getModelInfo(): {
    modelPath: string;
    isLoaded: boolean;
    windowSize: number;
    threshold: number;
    historyLength: number;
  } {
    return {
      modelPath: this.modelPath,
      isLoaded: this.isModelLoaded,
      windowSize: this.windowSize,
      threshold: this.threshold,
      historyLength: this.transactionHistory.length
    };
  }

  /**
   * 获取最近的特征数据（用于调试）
   */
  /**
   * 🔥 优化的特征提取 - 简化计算但保持64个特征
   */
  private extractOptimizedSellFeatures(transactions: TransactionData[]): SellFeatures | null {
    if (transactions.length < this.minDataForPrediction) {
      return null;
    }

    // 🔥 检查缓存
    if (this.featureCacheEnabled) {
      const currentHash = this.calculateDataHash(transactions);
      if (currentHash === this.lastFeatureCalculationHash && this.cachedFeatures) {
        return this.cachedFeatures;
      }
      this.lastFeatureCalculationHash = currentHash;
    }

    const availableData = transactions;
    let windowData = [...availableData];
    
    while (windowData.length < this.windowSize) {
      const defaultTransaction: TransactionData = {
        timestamp: new Date(Date.now() - (this.windowSize - windowData.length) * 60000),
        action: 2,
        sol_amount: 0.001,
        usd_amount: 0.15,
        is_target_wallet: false,
        wallet: '',
        block_number: 0
      };
      windowData.unshift(defaultTransaction);
    }
    
    // 🔥 优化：预计算所有基础数据
    const actions = windowData.map(t => t.action);
    const solAmounts = windowData.map(t => t.sol_amount);
    const usdAmounts = windowData.map(t => t.usd_amount);
    const timestamps = windowData.map(t => t.timestamp);
    
    // 🔥 优化：一次性计算所有统计值
    const solSum = solAmounts.reduce((a, b) => a + b, 0);
    const usdSum = usdAmounts.reduce((a, b) => a + b, 0);
    const solAmountMean = solSum / solAmounts.length;
    const usdAmountMean = usdSum / usdAmounts.length;
    
    // 🔥 优化：简化标准差计算
    let solVarianceSum = 0;
    let usdVarianceSum = 0;
    for (let i = 0; i < solAmounts.length; i++) {
      const solDiff = solAmounts[i] - solAmountMean;
      const usdDiff = usdAmounts[i] - usdAmountMean;
      solVarianceSum += solDiff * solDiff;
      usdVarianceSum += usdDiff * usdDiff;
    }
    const solAmountStd = Math.sqrt(solVarianceSum / solAmounts.length);
    const usdAmountStd = Math.sqrt(usdVarianceSum / usdAmounts.length);

    // 🔥 优化：一次性计算计数
    const buyCount = actions.filter(a => a === 1).length;
    const sellCount = actions.filter(a => a === 0).length;
    const holdCount = actions.filter(a => a === 2).length;
    const lastTimestamp = timestamps[timestamps.length - 1];

    // 🔥 优化：预计算索引
    const buyIndices = [];
    const sellIndices = [];
    for (let i = 0; i < actions.length; i++) {
      if (actions[i] === 1) buyIndices.push(i);
      if (actions[i] === 0) sellIndices.push(i);
    }

    // 买入特征 - 优化版
    let buyFeatures: any = {};
    if (buyIndices.length > 0) {
      const lastBuyIdx = buyIndices[buyIndices.length - 1];
      const firstBuyIdx = buyIndices[0];
      let buySolSum = 0, buyUsdSum = 0, buyMaxSol = 0, buyMaxUsd = 0;
      for (const idx of buyIndices) {
        buySolSum += solAmounts[idx];
        buyUsdSum += usdAmounts[idx];
        buyMaxSol = Math.max(buyMaxSol, solAmounts[idx]);
        buyMaxUsd = Math.max(buyMaxUsd, usdAmounts[idx]);
      }
      
      buyFeatures = {
        last_buy_distance: this.windowSize - (lastBuyIdx + 1),
        buy_first_from_last: buyIndices.length > 1 ? lastBuyIdx - firstBuyIdx : 0,
        last_buy_sol_amount: solAmounts[lastBuyIdx],
        last_buy_usd_amount: usdAmounts[lastBuyIdx],
        last_buy_time_diff: (lastTimestamp.getTime() - timestamps[lastBuyIdx].getTime()) / 1000,
        buy_sol_sum: buySolSum,
        buy_sol_mean: buySolSum / buyIndices.length,
        buy_sol_max: buyMaxSol,
        buy_usd_sum: buyUsdSum,
        buy_usd_mean: buyUsdSum / buyIndices.length,
        buy_usd_max: buyMaxUsd,
        buy_last_from_last: 0,
        buy_center_gravity: buyIndices.reduce((sum, i) => sum + i, 0) / buyIndices.length
      };
    } else {
      buyFeatures = {
        last_buy_distance: -1, buy_first_from_last: -1, last_buy_sol_amount: 0.0,
        last_buy_usd_amount: 0.0, last_buy_time_diff: -1.0, buy_sol_sum: 0.0,
        buy_sol_mean: 0.0, buy_sol_max: 0.0, buy_usd_sum: 0.0, buy_usd_mean: 0.0,
        buy_usd_max: 0.0, buy_last_from_last: -1, buy_center_gravity: -1
      };
    }

    // 卖出特征 - 优化版
    let sellFeatures: any = {};
    if (sellIndices.length > 0) {
      const firstSellIdx = sellIndices[0];
      const lastSellIdx = sellIndices[sellIndices.length - 1];
      sellFeatures = {
        sell_first_from_last: sellIndices.length > 1 ? lastSellIdx - firstSellIdx : 0,
        sell_last_from_last: 0,
        sell_center_gravity: sellIndices.reduce((sum, i) => sum + i, 0) / sellIndices.length
      };
    } else {
      sellFeatures = {
        sell_first_from_last: -1, sell_last_from_last: -1, sell_center_gravity: -1
      };
    }

    // 🔥 优化：简化趋势特征
    const firstSol = solAmounts[0];
    const lastSol = solAmounts[solAmounts.length - 1];
    const firstUsd = usdAmounts[0];
    const lastUsd = usdAmounts[usdAmounts.length - 1];
    
    const trendFeatures = {
      sol_amount_slope: lastSol - firstSol,
      sol_amount_ratio: firstSol !== 0 ? lastSol / firstSol : 0.0,
      usd_amount_slope: lastUsd - firstUsd,
      usd_amount_ratio: firstUsd !== 0 ? lastUsd / firstUsd : 0.0
    };

    // 波动性特征
    const solAmountVolatility = solAmountMean !== 0 ? solAmountStd / solAmountMean : 0.0;
    const usdAmountVolatility = usdAmountMean !== 0 ? usdAmountStd / usdAmountMean : 0.0;

    // 🔥 优化：简化动作模式特征
    let actionAlternateCount = 0;
    for (let i = 0; i < actions.length - 1; i++) {
      if (actions[i] !== actions[i + 1]) actionAlternateCount++;
    }

    // 连续性特征
    let buyStreak = 0, sellStreak = 0;
    for (const action of actions) {
      if (action === 1) {
        buyStreak++;
        sellStreak = 0;
      } else if (action === 0) {
        sellStreak++;
        buyStreak = 0;
      }
    }

    // 🔥 优化：简化时间间隔特征
    let timeFeatures: any = {};
    if (timestamps.length > 1) {
      const firstTime = timestamps[0].getTime();
      const lastTime = timestamps[timestamps.length - 1].getTime();
      const totalInterval = (lastTime - firstTime) / 1000;
      const intervalMean = totalInterval / (timestamps.length - 1);
      timeFeatures = {
        time_interval_mean: intervalMean,
        time_interval_std: intervalMean * 0.1 // 简化标准差
      };
    } else {
      timeFeatures = {time_interval_mean: 0.0, time_interval_std: 0.0};
    }

    // 比例特征
    const buyRatio = buyCount / actions.length;
    const sellRatio = sellCount / actions.length;
    const holdRatio = holdCount / actions.length;

    // 🔥 优化：简化熵特征
    const solAbsSum = solAmounts.reduce((a, b) => a + Math.abs(b), 0);
    const usdAbsSum = usdAmounts.reduce((a, b) => a + Math.abs(b), 0);
    const entropyFeatures = {
      sol_amount_entropy: solAbsSum !== 0 ? solAmountStd / solAbsSum : 0.0,
      usd_amount_entropy: usdAbsSum !== 0 ? usdAmountStd / usdAbsSum : 0.0
    };

    // 比例特征
    const solAmountProportion = solAbsSum !== 0 ? solSum / solAbsSum : 0.0;
    const usdAmountProportion = usdAbsSum !== 0 ? usdSum / usdAbsSum : 0.0;

    // 前后半段特征
    const halfPoint = Math.floor(this.windowSize / 2);
    const buyFrontHalf = actions.slice(0, halfPoint).filter(a => a === 1).length;
    const buyBackHalf = actions.slice(halfPoint).filter(a => a === 1).length;
    const sellFrontHalf = actions.slice(0, halfPoint).filter(a => a === 0).length;
    const sellBackHalf = actions.slice(halfPoint).filter(a => a === 0).length;

    // 🔥 优化：简化最大上升/下降/回撤特征
    const solMax = Math.max(...solAmounts);
    const solMin = Math.min(...solAmounts);
    const usdMax = Math.max(...usdAmounts);
    const usdMin = Math.min(...usdAmounts);
    
    const solAmountMaxRise = solMax - firstSol;
    const solAmountMaxDrop = firstSol - solMin;
    const solAmountMaxDrawdown = solMax - solMin;
    
    const usdAmountMaxRise = usdMax - firstUsd;
    const usdAmountMaxDrop = firstUsd - usdMin;
    const usdAmountMaxDrawdown = usdMax - usdMin;

    // 买卖差异特征
    let buyToSellFeatures: any = {};
    if (buyIndices.length > 0 && sellIndices.length > 0) {
      const buyMeanSol = buyFeatures.buy_sol_mean;
      const sellMeanSol = sellIndices.reduce((sum, i) => sum + solAmounts[i], 0) / sellIndices.length;
      const buyMeanUsd = buyFeatures.buy_usd_mean;
      const sellMeanUsd = sellIndices.reduce((sum, i) => sum + usdAmounts[i], 0) / sellIndices.length;
      
      buyToSellFeatures = {
        buy2sell_sol_diff_mean: buyMeanSol - sellMeanSol,
        buy2sell_usd_diff_mean: buyMeanUsd - sellMeanUsd
      };
    } else {
      buyToSellFeatures = {buy2sell_sol_diff_mean: 0.0, buy2sell_usd_diff_mean: 0.0};
    }

    // 最近N个交易特征
    let lastNFeatures: any = {};
    if (actions.length >= 3) {
      lastNFeatures = {
        action_last_1: actions[actions.length - 1],
        sol_amount_last_1: solAmounts[solAmounts.length - 1],
        usd_amount_last_1: usdAmounts[usdAmounts.length - 1],
        action_last_2: actions[actions.length - 2],
        sol_amount_last_2: solAmounts[solAmounts.length - 2],
        usd_amount_last_2: usdAmounts[usdAmounts.length - 2],
        action_last_3: actions[actions.length - 3],
        sol_amount_last_3: solAmounts[solAmounts.length - 3],
        usd_amount_last_3: usdAmounts[usdAmounts.length - 3]
      };
    } else {
      lastNFeatures = {
        action_last_1: 0, sol_amount_last_1: 0, usd_amount_last_1: 0,
        action_last_2: 0, sol_amount_last_2: 0, usd_amount_last_2: 0,
        action_last_3: 0, sol_amount_last_3: 0, usd_amount_last_3: 0
      };
    }

    const features: SellFeatures = {
      sol_amount_mean: solAmountMean,
      sol_amount_std: solAmountStd,
      usd_amount_mean: usdAmountMean,
      usd_amount_std: usdAmountStd,
      buy_count: buyCount,
      sell_count: sellCount,
      hold_count: holdCount,
      hour_last: lastTimestamp.getHours(),
      weekday_last: lastTimestamp.getDay(),
      ...buyFeatures,
      ...sellFeatures,
      ...trendFeatures,
      sol_amount_volatility: solAmountVolatility,
      usd_amount_volatility: usdAmountVolatility,
      action_alternate_count: actionAlternateCount,
      max_buy_streak: buyStreak,
      max_sell_streak: sellStreak,
      ...timeFeatures,
      buy_ratio: buyRatio,
      sell_ratio: sellRatio,
      hold_ratio: holdRatio,
      ...entropyFeatures,
      sol_amount_proportion: solAmountProportion,
      usd_amount_proportion: usdAmountProportion,
      buy_front_half: buyFrontHalf,
      buy_back_half: buyBackHalf,
      sell_front_half: sellFrontHalf,
      sell_back_half: sellBackHalf,
      sol_amount_max_rise: solAmountMaxRise,
      sol_amount_max_drop: solAmountMaxDrop,
      sol_amount_max_drawdown: solAmountMaxDrawdown,
      usd_amount_max_rise: usdAmountMaxRise,
      usd_amount_max_drop: usdAmountMaxDrop,
      usd_amount_max_drawdown: usdAmountMaxDrawdown,
      ...buyToSellFeatures,
      ...lastNFeatures
    };

    // 🔥 保存到缓存
    if (this.featureCacheEnabled) {
      this.cachedFeatures = features;
    }

    return features;
  }



  /**
   * 🔥 控制特征缓存
   */
  public enableFeatureCache(enabled: boolean = true): void {
    this.featureCacheEnabled = enabled;
    if (!enabled) {
      this.invalidateFeatureCache();
    }
  }

  /**
   * 🔥 获取缓存状态
   */
  public getCacheStatus(): {
    enabled: boolean;
    hasCachedFeatures: boolean;
    lastHash: string;
  } {
    return {
      enabled: this.featureCacheEnabled,
      hasCachedFeatures: this.cachedFeatures !== null,
      lastHash: this.lastFeatureCalculationHash.slice(0, 20) + '...'
    };
  }

  public getLatestFeatures(): SellFeatures | null {
    if (this.transactionHistory.length < this.windowSize) {
      return null;
    }

    return this.extractSellFeatures(this.transactionHistory);
  }
} 