# 止损规则实现说明

## 🔍 当前止损配置

### 1. **双重止损机制**

系统现在实现了双重止损保护：

#### **A. 20%固定止损（新增）**
```typescript
const FIXED_STOP_LOSS_PERCENTAGE = -20; // 固定20%止损
if (priceChange <= FIXED_STOP_LOSS_PERCENTAGE) {
  console.log(`🔴 触发20%固定止损卖出 ${tokenDisplay} (损失: ${priceChange.toFixed(2)}%)`);
  await this.executeStopLoss(tokenAddress, instance, currentPrice);
  return;
}
```

#### **B. 25%动态止损（原有）**
```typescript
// 从环境变量读取，默认25%
const STOP_LOSS_PERCENTAGE = parseFloat(process.env.STOP_LOSS_PERCENTAGE || '0.25');

// 在PositionManager中计算止损价格
stopLossPrice: entryPrice * (1 - this.config.stopLossPercentage)
```

### 2. **止损触发优先级**

1. **优先**：20%固定止损（更严格）
2. **备用**：25%动态止损（PositionManager管理）

## 🔧 修复的问题

### 1. **止损价格计算修复**

**原始问题**：
```typescript
// ❌ 错误的损失百分比计算
const lossPercentage = ((position.stopLossPrice - position.entryPrice) / position.entryPrice * 100);
```

**修复后**：
```typescript
// ✅ 正确的实际损失百分比计算
const actualLossPercentage = ((currentPrice - position.entryPrice) / position.entryPrice * 100);
```

### 2. **增强的调试信息**

现在止损检查会显示：
- 当前价格 vs 买入价格
- 实际价格变化百分比
- PositionManager仓位状态
- 距离动态止损的距离
- 止损触发原因

## 📊 止损检查流程

### 1. **每次交易决策时检查**
```typescript
// 🔥 检查止损条件
if (hasPosition && instance.currentHolding) {
  const currentPrice = this.getLastTradePrice(tokenAddress);
  const position = this.positionManager.getPosition(tokenAddress);
  
  // 详细的止损检查和调试信息
}
```

### 2. **价格变化计算**
```typescript
const priceChange = ((currentPrice / instance.currentHolding.buyPrice - 1) * 100);
```

### 3. **双重检查机制**
- 先检查20%固定止损
- 再检查25%动态止损
- 任一触发都会执行卖出

## 🔍 调试信息示例

```
🔍 止损检查 PUMP/SOL:
   当前价格: 0.00001234
   买入价格: 0.00001500
   止损价格: 0.00001125
   PositionManager仓位: 存在
   价格变化: -17.73%
   距离动态止损: *****% (安全)
✅ 未触发止损，继续监控
```

## ⚙️ 环境变量配置

```bash
# 动态止损百分比（默认25%）
STOP_LOSS_PERCENTAGE=0.25

# 其他风险管理配置
MAX_DAILY_LOSS_SOL=0.5
CONSECUTIVE_LOSS_LIMIT=5
```

## 🔄 止损执行流程

### 1. **触发条件**
- 价格跌幅 ≥ 20%（固定止损）
- 或价格 ≤ 止损价格（动态止损）

### 2. **执行步骤**
1. 调用 `executeStopLoss()`
2. 执行真实卖出交易
3. 记录交易历史
4. 发送Telegram通知
5. 清空持仓
6. 移除token监控

### 3. **通知内容**
```
🔴 止损卖出执行

🪙 Token: PUMP/SOL
📦 卖出Token: 1000.00 个
💵 收到SOL: 0.0123 SOL
💲 卖出价格: $0.00001234/token
🛑 触发原因: 20%固定止损
💰 盈亏: -0.0045 SOL (-26.83%)
⏱️ 持仓时间: 15分钟
🤖 AI预测: 0.0% (止损强制卖出)
✅ 状态: 已确认
```

## 🎯 预期效果

### 1. **风险控制**
- 最大单笔损失限制在20%
- 防止深度亏损
- 保护交易资金

### 2. **快速响应**
- 实时价格监控
- 自动触发机制
- 无需人工干预

### 3. **透明度**
- 详细的调试日志
- 清晰的触发原因
- 完整的执行记录

## 🔍 故障排除

### 1. **止损未触发**
检查：
- PositionManager仓位是否存在
- 当前价格是否正确获取
- 价格变化计算是否准确

### 2. **调试模式**
```bash
# 启用详细的交易决策调试
DEBUG_TRADING_DECISIONS=true npm start
```

### 3. **手动检查**
- 查看控制台的止损检查日志
- 确认价格数据来源
- 验证仓位同步状态

这个双重止损机制确保了更好的风险控制，20%的固定止损提供了严格的保护，而25%的动态止损作为备用机制。
