{"name": "stream-and-parse-pump-fun-amm-transaction-instructions-via-grpc", "version": "1.0.0", "description": "", "main": "index.ts", "type": "commonjs", "scripts": {"watch": "npx ts-node-dev index.ts", "build": "npx tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1", "trading-bot": "npx ts-node trading-bot.ts", "paper-trading": "npx ts-node paper-trading-with-tracker.ts", "wallet-tracker": "npx ts-node wallet-tracker.ts", "test-wallet-tracker": "npx ts-node test-wallet-tracker.ts", "test-catboost": "npx ts-node test-catboost.ts", "test-price-service": "npx ts-node test-price-service.ts", "test-pumpswap": "npx ts-node test-pumpswap.ts", "test-pumpswap-jito": "npx ts-node test-pumpswap-jito.ts"}, "author": "impin2rex", "keywords": ["solana", "grpc", "pump.fun", "amm", "transaction", "parser", "typescript", "blockchain", "nodejs", "web3"], "license": "ISC", "devDependencies": {"@types/lodash": "^4.17.4", "@types/node": "^20.17.57", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsx": "^4.7.1", "typescript": "^5.8.3"}, "dependencies": {"@coral-xyz/anchor": "^0.30.1", "@grpc/grpc-js": "^1.13.4", "@pump-fun/pump-swap-sdk": "^0.0.1-beta.32", "@pythnetwork/pyth-solana-receiver": "^0.10.1", "@shyft-to/solana-transaction-parser": "^2.0.0", "@solana/web3.js": "^1.93.0", "@triton-one/yellowstone-grpc": "^4.0.0", "@types/express": "^5.0.2", "axios": "^1.9.0", "catboost": "^1.26.0", "dotenv": "^16.4.7", "express": "^5.1.0", "heapdump": "^0.3.15", "jito-ts": "^4.2.0", "lodash": "^4.17.21"}}