// 🔥 移除不存在的 trading-bot 导入，使用接口定义
interface TradingBot {
  subscribeToToken?(tokenAddress: string): Promise<void>;
  unsubscribeFromToken?(tokenAddress: string): Promise<void>;
  liquidatePosition?(tokenAddress: string): Promise<void>;
  getStats?(): any;
}

export interface TokenActivity {
  tokenAddress: string;
  lastActivityTime: Date;
  firstActivityTime: Date;
  transactionCount: number;
  totalVolume: number;
  isSubscribed: boolean;
}

export interface WalletTransaction {
  signature: string;
  timestamp: Date;
  slot: number;
  tokenAddress: string;
  action: 'buy' | 'sell' | 'unknown';
  solAmount: number;
  usdAmount: number;
  wallet: string;
}

export class DynamicSubscriptionManager {
  private targetWallet: string;
  private tokenActivities: Map<string, TokenActivity> = new Map();
  private walletTransactions: WalletTransaction[] = [];
  private tradingBot: TradingBot | null = null;
  private windowHours: number;
  private updateIntervalMs: number;
  private cleanupIntervalId: NodeJS.Timeout | null = null;

  constructor(
    targetWallet: string, 
    windowHours: number = 1, 
    updateIntervalMs: number = 60000 // 每分钟检查一次
  ) {
    this.targetWallet = targetWallet;
    this.windowHours = windowHours;
    this.updateIntervalMs = updateIntervalMs;
    
    console.log(`🎯 动态订阅管理器已启动`);
    console.log(`   目标钱包: ${targetWallet}`);
    console.log(`   活动窗口: ${windowHours} 小时`);
    console.log(`   更新间隔: ${updateIntervalMs / 1000} 秒`);
  }

  /**
   * 启动动态订阅管理
   */
  public start(tradingBot?: TradingBot): void {
    this.tradingBot = tradingBot || null;
    
    // 启动定期清理和更新
    this.cleanupIntervalId = setInterval(() => {
      this.cleanupExpiredTokens();
    }, this.updateIntervalMs);
    
    console.log(`✅ 动态订阅管理器已启动，开始监控钱包: ${this.targetWallet}`);
  }

  /**
   * 停止动态订阅管理
   */
  public stop(): void {
    if (this.cleanupIntervalId) {
      clearInterval(this.cleanupIntervalId);
      this.cleanupIntervalId = null;
    }
    console.log(`🛑 动态订阅管理器已停止`);
  }

  /**
   * 处理新的钱包交易
   */
  public async processWalletTransaction(
    signature: string,
    timestamp: Date,
    slot: number,
    tokenAddress: string,
    action: 'buy' | 'sell' | 'unknown',
    solAmount: number = 0,
    usdAmount: number = 0,
    wallet: string = 'unknown' // 添加钱包参数
  ): Promise<void> {
    // 记录交易
    const transaction: WalletTransaction = {
      signature,
      timestamp,
      slot,
      tokenAddress,
      action,
      solAmount,
      usdAmount,
      wallet: wallet || 'unknown' // 使用传入的钱包地址
    };
    
    this.walletTransactions.push(transaction);
    
    // 保持交易历史在合理范围内（最多保留24小时的数据）
    const cutoffTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
    this.walletTransactions = this.walletTransactions.filter(tx => tx.timestamp > cutoffTime);

    // 更新token活动
    await this.updateTokenActivity(tokenAddress, timestamp, solAmount, usdAmount);
    
    console.log(`📈 钱包交易: ${action} ${tokenAddress.slice(0, 8)}... (${solAmount.toFixed(4)} SOL)`);
  }

  /**
   * 更新token活动记录
   */
  private async updateTokenActivity(
    tokenAddress: string, 
    timestamp: Date, 
    solAmount: number, 
    usdAmount: number
  ): Promise<void> {
    let activity = this.tokenActivities.get(tokenAddress);
    
    if (!activity) {
      // 新的token，创建活动记录
      activity = {
        tokenAddress,
        lastActivityTime: timestamp,
        firstActivityTime: timestamp,
        transactionCount: 0,
        totalVolume: 0,
        isSubscribed: false
      };
      this.tokenActivities.set(tokenAddress, activity);
      console.log(`🆕 发现新token: ${tokenAddress.slice(0, 8)}...`);
    }

    // 更新活动数据
    activity.lastActivityTime = timestamp;
    activity.transactionCount++;
    activity.totalVolume += solAmount;

    // 如果还没有订阅这个token，则订阅
    if (!activity.isSubscribed && this.tradingBot) {
      await this.subscribeToToken(tokenAddress);
      activity.isSubscribed = true;
      console.log(`✅ 已订阅token: ${tokenAddress.slice(0, 8)}...`);
    }
  }

  /**
   * 清理过期的token
   */
  private async cleanupExpiredTokens(): Promise<void> {
    const now = new Date();
    const cutoffTime = new Date(now.getTime() - this.windowHours * 60 * 60 * 1000);
    
    const expiredTokens: string[] = [];
    
    for (const [tokenAddress, activity] of this.tokenActivities.entries()) {
      if (activity.lastActivityTime < cutoffTime) {
        expiredTokens.push(tokenAddress);
      }
    }

    for (const tokenAddress of expiredTokens) {
      await this.handleExpiredToken(tokenAddress);
    }

    if (expiredTokens.length > 0) {
      console.log(`🧹 清理了 ${expiredTokens.length} 个过期token`);
    }

    // 显示当前活跃token统计
    this.logCurrentStatus();
  }

  /**
   * 处理过期的token
   */
  private async handleExpiredToken(tokenAddress: string): Promise<void> {
    const activity = this.tokenActivities.get(tokenAddress);
    if (!activity) return;

    console.log(`⏰ Token过期: ${tokenAddress.slice(0, 8)}... (最后活动: ${activity.lastActivityTime.toLocaleString()})`);

    // 如果有交易机器人，先进行清仓
    if (this.tradingBot && activity.isSubscribed) {
      try {
        console.log(`💼 对过期token进行清仓: ${tokenAddress.slice(0, 8)}...`);
        await this.liquidateToken(tokenAddress);
      } catch (error) {
        console.error(`❌ 清仓失败 ${tokenAddress.slice(0, 8)}...:`, error);
      }
    }

    // 取消订阅
    if (activity.isSubscribed) {
      await this.unsubscribeFromToken(tokenAddress);
      console.log(`🚫 已取消订阅: ${tokenAddress.slice(0, 8)}...`);
    }

    // 从活动记录中移除
    this.tokenActivities.delete(tokenAddress);
  }

  /**
   * 订阅token
   */
  private async subscribeToToken(tokenAddress: string): Promise<void> {
    if (this.tradingBot) {
      // 使用现有的TradingBot接口
      try {
        // 调用交易机器人的订阅方法
        (this.tradingBot as any).subscribeToken?.(tokenAddress);
      } catch (error) {
        console.warn(`⚠️ TradingBot订阅方法不可用: ${error}`);
      }
    }
    console.log(`📊 开始监控token: ${tokenAddress.slice(0, 8)}...`);
  }

  /**
   * 取消订阅token
   */
  private async unsubscribeFromToken(tokenAddress: string): Promise<void> {
    if (this.tradingBot) {
      try {
        // 调用交易机器人的取消订阅方法
        (this.tradingBot as any).unsubscribeToken?.(tokenAddress);
      } catch (error) {
        console.warn(`⚠️ TradingBot取消订阅方法不可用: ${error}`);
      }
    }
    console.log(`📊 停止监控token: ${tokenAddress.slice(0, 8)}...`);
  }

  /**
   * 清仓token
   */
  private async liquidateToken(tokenAddress: string): Promise<void> {
    if (!this.tradingBot) return;
    
    try {
      console.log(`💼 执行token清仓: ${tokenAddress.slice(0, 8)}...`);
      
      // 首先检查TradingBot是否有liquidateToken方法
      if (typeof (this.tradingBot as any).liquidateToken === 'function') {
        await (this.tradingBot as any).liquidateToken(tokenAddress);
        console.log(`✅ Token清仓完成: ${tokenAddress.slice(0, 8)}...`);
      } else {
        // 如果没有专门的清仓方法，尝试其他方式
        console.log(`⚠️ 直接清仓方法不可用，尝试其他方式处理token: ${tokenAddress.slice(0, 8)}...`);
        
        // 检查是否有持仓信息
        const stats = this.tradingBot.getStats();
        if (stats && stats.activePositions > 0) {
          console.log(`💼 检测到活跃仓位，token: ${tokenAddress.slice(0, 8)}...`);
          console.log(`📊 当前统计: ${stats.activePositions} 个活跃仓位, 总余额: ${stats.currentBalance?.toFixed(4) || 'N/A'} SOL`);
          
          // 尝试获取当前持仓
          let hasHolding = false;
          try {
            const holdings = (this.tradingBot as any).getCurrentHoldings?.();
            if (holdings && Array.isArray(holdings)) {
              hasHolding = holdings.includes(tokenAddress);
              console.log(`🔍 持仓检查: ${hasHolding ? '有持仓' : '无持仓'} - ${tokenAddress.slice(0, 8)}...`);
            }
          } catch (err) {
            console.log(`⚠️ 无法获取持仓详情: ${err}`);
          }
          
          if (hasHolding) {
            console.log(`⚠️ 检测到token持仓但无法自动清仓`);
            console.log(`📋 建议手动处理: ${tokenAddress.slice(0, 8)}...`);
            console.log(`🔗 Token地址: ${tokenAddress}`);
          }
        }
        
        console.log(`📝 Token过期处理完成: ${tokenAddress.slice(0, 8)}...`);
      }
      
    } catch (error) {
      console.error(`❌ Token处理失败 ${tokenAddress.slice(0, 8)}...:`, error);
      // 不要重新抛出错误，避免中断其他token的处理
    }
  }

  /**
   * 获取当前活跃的token列表
   */
  public getActiveTokens(): string[] {
    const now = new Date();
    const cutoffTime = new Date(now.getTime() - this.windowHours * 60 * 60 * 1000);
    
    return Array.from(this.tokenActivities.values())
      .filter(activity => activity.lastActivityTime >= cutoffTime)
      .map(activity => activity.tokenAddress);
  }

  /**
   * 获取token活动统计
   */
  public getTokenActivityStats(): TokenActivity[] {
    return Array.from(this.tokenActivities.values())
      .sort((a, b) => b.lastActivityTime.getTime() - a.lastActivityTime.getTime());
  }

  /**
   * 获取钱包交易历史
   */
  public getWalletTransactionHistory(hours: number = 1): WalletTransaction[] {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.walletTransactions
      .filter(tx => tx.timestamp >= cutoffTime)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * 显示当前状态
   */
  private logCurrentStatus(): void {
    const activeTokens = this.getActiveTokens();
    const totalTokens = this.tokenActivities.size;
    const recentTransactions = this.getWalletTransactionHistory(1);
    
    console.log(`\n📊 动态订阅状态:`);
    console.log(`   活跃Token数量: ${activeTokens.length}/${totalTokens}`);
    console.log(`   最近1小时交易: ${recentTransactions.length} 笔`);
    
    if (activeTokens.length > 0) {
      console.log(`   当前监控Token:`);
      activeTokens.slice(0, 5).forEach((token, index) => {
        const activity = this.tokenActivities.get(token);
        if (activity) {
          const timeSinceLastActivity = Math.floor((Date.now() - activity.lastActivityTime.getTime()) / (1000 * 60));
          console.log(`     ${index + 1}. ${token.slice(0, 8)}... (${timeSinceLastActivity}分钟前, ${activity.transactionCount}笔交易)`);
        }
      });
      if (activeTokens.length > 5) {
        console.log(`     ... 还有 ${activeTokens.length - 5} 个token`);
      }
    }
    console.log('');
  }

  /**
   * 检查钱包是否是目标钱包
   */
  public isTargetWallet(walletAddress: string): boolean {
    return walletAddress === this.targetWallet;
  }

  /**
   * 获取目标钱包地址
   */
  public getTargetWallet(): string {
    return this.targetWallet;
  }
} 