export interface RiskConfig {
  initialCapital?: number;
  maxLossPerTrade?: number;
  stopLossPercentage?: number;
  takeProfitPercentage?: number;
  maxDailyLoss?: number;
  maxDrawdown?: number;
  maxPositionSize?: number;
  cooldownPeriod?: number; // 亏损后的冷却期（毫秒）
}

export interface RiskMetrics {
  currentBalance: number;
  dailyPnL: number;
  maxDrawdown: number;
  consecutiveLosses: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  lastTradeTime: Date | null;
  lastLossTime: Date | null;
  riskScore: number; // 0-100的风险评分
}

export class RiskManager {
  private config: RiskConfig;
  private metrics: RiskMetrics;
  private dailyStartBalance: number;
  private highWaterMark: number;
  private tradeHistory: Array<{
    timestamp: Date;
    pnl: number;
    success: boolean;
  }> = [];

  constructor(config: RiskConfig = {}) {
    this.config = {
      initialCapital: 3.0,
      maxLossPerTrade: 0.1,
      stopLossPercentage: 0.1, // 10%
      takeProfitPercentage: 0.2, // 20%
      maxDailyLoss: 0.5,
      maxDrawdown: 0.3, // 30%
      maxPositionSize: 0.2, // 20% of capital per position
      cooldownPeriod: 300000, // 5分钟冷却期
      ...config
    };

    this.dailyStartBalance = this.config.initialCapital || 3.0;
    this.highWaterMark = this.dailyStartBalance;
    
    this.metrics = {
      currentBalance: this.dailyStartBalance,
      dailyPnL: 0,
      maxDrawdown: 0,
      consecutiveLosses: 0,
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      lastTradeTime: null,
      lastLossTime: null,
      riskScore: 0
    };
  }

  /**
   * 启动风险管理器
   */
  public start(): void {
    this.log('info', 'Risk manager started');
    this.resetDailyMetrics();
    
    // 每天重置日内统计
    setInterval(() => {
      this.resetDailyMetrics();
    }, 24 * 60 * 60 * 1000); // 24小时
  }

  /**
   * 停止风险管理器
   */
  public stop(): void {
    this.log('info', 'Risk manager stopped');
  }

  /**
   * 检查是否可以进行交易
   */
  public canTrade(): boolean {
    // 检查日内最大亏损
    if (this.metrics.dailyPnL <= -Math.abs(this.config.maxDailyLoss || 0.5)) {
      this.log('warn', `Daily loss limit reached: ${this.metrics.dailyPnL.toFixed(4)} SOL`);
      return false;
    }

    // 检查最大回撤
    const currentDrawdown = (this.highWaterMark - this.metrics.currentBalance) / this.highWaterMark;
    if (currentDrawdown >= (this.config.maxDrawdown || 0.3)) {
      this.log('warn', `Max drawdown reached: ${(currentDrawdown * 100).toFixed(2)}%`);
      return false;
    }

    // 检查连续亏损后的冷却期
    if (this.metrics.consecutiveLosses >= 3 && this.metrics.lastLossTime) {
      const timeSinceLastLoss = Date.now() - this.metrics.lastLossTime.getTime();
      if (timeSinceLastLoss < (this.config.cooldownPeriod || 300000)) {
        const remainingCooldown = (this.config.cooldownPeriod || 300000) - timeSinceLastLoss;
        this.log('warn', `In cooldown period: ${Math.ceil(remainingCooldown / 1000)}s remaining`);
        return false;
      }
    }

    // 检查风险评分
    const riskScore = this.calculateRiskScore();
    if (riskScore > 80) {
      this.log('warn', `Risk score too high: ${riskScore}`);
      return false;
    }

    return true;
  }

  /**
   * 检查是否可以承担指定金额的交易
   */
  public canAffordTrade(tradeAmount: number): boolean {
    // 检查余额是否充足
    if (tradeAmount > this.metrics.currentBalance) {
      this.log('warn', `Insufficient balance: ${this.metrics.currentBalance.toFixed(4)} SOL < ${tradeAmount} SOL`);
      return false;
    }

    // 检查单笔交易风险
    if (tradeAmount > (this.config.maxLossPerTrade || 0.1)) {
      this.log('warn', `Trade amount exceeds max loss per trade: ${tradeAmount} SOL > ${this.config.maxLossPerTrade} SOL`);
      return false;
    }

    // 检查仓位大小限制
    const positionSizeRatio = tradeAmount / this.metrics.currentBalance;
    if (positionSizeRatio > (this.config.maxPositionSize || 0.2)) {
      this.log('warn', `Position size too large: ${(positionSizeRatio * 100).toFixed(2)}% > ${((this.config.maxPositionSize || 0.2) * 100).toFixed(2)}%`);
      return false;
    }

    return true;
  }

  /**
   * 记录交易结果
   */
  public recordTrade(pnl: number, tradeAmount: number): void {
    const isWinning = pnl > 0;
    const timestamp = new Date();

    // 更新余额和PnL
    this.metrics.currentBalance += pnl;
    this.metrics.dailyPnL += pnl;
    this.metrics.totalTrades++;
    this.metrics.lastTradeTime = timestamp;

    // 更新高水位线
    if (this.metrics.currentBalance > this.highWaterMark) {
      this.highWaterMark = this.metrics.currentBalance;
    }

    // 更新最大回撤
    const currentDrawdown = (this.highWaterMark - this.metrics.currentBalance) / this.highWaterMark;
    if (currentDrawdown > this.metrics.maxDrawdown) {
      this.metrics.maxDrawdown = currentDrawdown;
    }

    // 更新胜负统计
    if (isWinning) {
      this.metrics.winningTrades++;
      this.metrics.consecutiveLosses = 0; // 重置连续亏损计数
    } else {
      this.metrics.losingTrades++;
      this.metrics.consecutiveLosses++;
      this.metrics.lastLossTime = timestamp;
    }

    // 添加到交易历史
    this.tradeHistory.push({
      timestamp,
      pnl,
      success: isWinning
    });

    // 保持历史记录在合理范围内
    if (this.tradeHistory.length > 1000) {
      this.tradeHistory = this.tradeHistory.slice(-1000);
    }

    // 更新风险评分
    this.metrics.riskScore = this.calculateRiskScore();

    this.log('info', `Trade recorded: PnL ${pnl.toFixed(4)} SOL, Balance: ${this.metrics.currentBalance.toFixed(4)} SOL, Risk Score: ${this.metrics.riskScore}`);
  }

  /**
   * 计算建议的仓位大小
   */
  public calculatePositionSize(accountBalance: number, riskPerTrade: number = 0.02): number {
    // Kelly Criterion改良版
    const winRate = this.metrics.totalTrades > 0 ? this.metrics.winningTrades / this.metrics.totalTrades : 0.5;
    const avgWin = this.getAverageWin();
    const avgLoss = Math.abs(this.getAverageLoss());
    
    if (avgLoss === 0) {
      return Math.min(accountBalance * riskPerTrade, this.config.maxLossPerTrade || 0.1);
    }

    const payoffRatio = avgWin / avgLoss;
    const kellyFraction = (winRate * payoffRatio - (1 - winRate)) / payoffRatio;
    
    // 限制Kelly分数在合理范围内
    const adjustedKelly = Math.max(0, Math.min(kellyFraction, 0.25)); // 最大25%
    
    // 根据风险评分调整
    const riskAdjustment = Math.max(0.1, 1 - (this.metrics.riskScore / 100));
    
    const suggestedSize = accountBalance * adjustedKelly * riskAdjustment;
    
    // 确保不超过最大限制
    return Math.min(
      suggestedSize,
      this.config.maxLossPerTrade || 0.1,
      accountBalance * (this.config.maxPositionSize || 0.2)
    );
  }

  /**
   * 获取风险指标
   */
  public getRiskMetrics(): RiskMetrics {
    return { ...this.metrics };
  }

  /**
   * 计算风险评分 (0-100)
   */
  private calculateRiskScore(): number {
    let score = 0;

    // 回撤风险 (0-25分)
    const drawdownRisk = (this.metrics.maxDrawdown / (this.config.maxDrawdown || 0.3)) * 25;
    score += Math.min(drawdownRisk, 25);

    // 连续亏损风险 (0-20分)
    const consecutiveLossRisk = Math.min(this.metrics.consecutiveLosses / 5, 1) * 20;
    score += consecutiveLossRisk;

    // 日内亏损风险 (0-25分)
    const dailyLossRisk = Math.abs(this.metrics.dailyPnL) / Math.abs(this.config.maxDailyLoss || 0.5) * 25;
    score += Math.min(dailyLossRisk, 25);

    // 胜率风险 (0-15分)
    const winRate = this.metrics.totalTrades > 0 ? this.metrics.winningTrades / this.metrics.totalTrades : 0.5;
    const winRateRisk = (1 - winRate) * 15;
    score += winRateRisk;

    // 资金使用率风险 (0-15分)
    const capitalUsageRisk = (this.config.initialCapital! - this.metrics.currentBalance) / this.config.initialCapital! * 15;
    score += Math.max(0, capitalUsageRisk);

    return Math.min(100, Math.max(0, score));
  }

  /**
   * 获取平均盈利
   */
  private getAverageWin(): number {
    const winningTrades = this.tradeHistory.filter(trade => trade.pnl > 0);
    if (winningTrades.length === 0) return 0;
    
    const totalWins = winningTrades.reduce((sum, trade) => sum + trade.pnl, 0);
    return totalWins / winningTrades.length;
  }

  /**
   * 获取平均亏损
   */
  private getAverageLoss(): number {
    const losingTrades = this.tradeHistory.filter(trade => trade.pnl < 0);
    if (losingTrades.length === 0) return 0;
    
    const totalLosses = losingTrades.reduce((sum, trade) => sum + trade.pnl, 0);
    return totalLosses / losingTrades.length;
  }

  /**
   * 重置日内统计
   */
  private resetDailyMetrics(): void {
    this.dailyStartBalance = this.metrics.currentBalance;
    this.metrics.dailyPnL = 0;
    this.log('info', 'Daily metrics reset');
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<RiskConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.log('info', 'Risk manager configuration updated');
  }

  /**
   * 强制重置风险状态（紧急情况使用）
   */
  public emergencyReset(): void {
    this.metrics.consecutiveLosses = 0;
    this.metrics.lastLossTime = null;
    this.metrics.riskScore = this.calculateRiskScore();
    this.log('warn', 'Emergency risk reset performed');
  }

  /**
   * 获取交易建议
   */
  public getTradingAdvice(): {
    canTrade: boolean;
    reason: string;
    suggestedPositionSize: number;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  } {
    const canTrade = this.canTrade();
    const riskScore = this.metrics.riskScore;
    const suggestedSize = this.calculatePositionSize(this.metrics.currentBalance);
    
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    let reason = '';

    if (riskScore < 30) {
      riskLevel = 'LOW';
      reason = 'Risk conditions are favorable for trading';
    } else if (riskScore < 60) {
      riskLevel = 'MEDIUM';
      reason = 'Moderate risk conditions, trade with caution';
    } else if (riskScore < 80) {
      riskLevel = 'HIGH';
      reason = 'High risk conditions, consider reducing position sizes';
    } else {
      riskLevel = 'CRITICAL';
      reason = 'Critical risk levels, trading not recommended';
    }

    if (!canTrade) {
      reason = 'Trading blocked by risk management rules';
    }

    return {
      canTrade,
      reason,
      suggestedPositionSize: suggestedSize,
      riskLevel
    };
  }

  /**
   * 日志记录
   */
  private log(level: 'info' | 'warn' | 'error', message: string): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${level.toUpperCase()}] [RiskManager] ${message}`);
  }
} 