"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RiskManager = void 0;
var RiskManager = /** @class */ (function () {
    function RiskManager(config) {
        if (config === void 0) { config = {}; }
        this.tradeHistory = [];
        this.config = __assign({ initialCapital: 3.0, maxLossPerTrade: 0.1, stopLossPercentage: 0.1, takeProfitPercentage: 0.2, maxDailyLoss: 0.5, maxDrawdown: 0.3, maxPositionSize: 0.2, cooldownPeriod: 300000 }, config);
        this.dailyStartBalance = this.config.initialCapital || 3.0;
        this.highWaterMark = this.dailyStartBalance;
        this.metrics = {
            currentBalance: this.dailyStartBalance,
            dailyPnL: 0,
            maxDrawdown: 0,
            consecutiveLosses: 0,
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            lastTradeTime: null,
            lastLossTime: null,
            riskScore: 0
        };
    }
    /**
     * 启动风险管理器
     */
    RiskManager.prototype.start = function () {
        var _this = this;
        this.log('info', 'Risk manager started');
        this.resetDailyMetrics();
        // 每天重置日内统计
        setInterval(function () {
            _this.resetDailyMetrics();
        }, 24 * 60 * 60 * 1000); // 24小时
    };
    /**
     * 停止风险管理器
     */
    RiskManager.prototype.stop = function () {
        this.log('info', 'Risk manager stopped');
    };
    /**
     * 检查是否可以进行交易
     */
    RiskManager.prototype.canTrade = function () {
        // 检查日内最大亏损
        if (this.metrics.dailyPnL <= -Math.abs(this.config.maxDailyLoss || 0.5)) {
            this.log('warn', "Daily loss limit reached: ".concat(this.metrics.dailyPnL.toFixed(4), " SOL"));
            return false;
        }
        // 检查最大回撤
        var currentDrawdown = (this.highWaterMark - this.metrics.currentBalance) / this.highWaterMark;
        if (currentDrawdown >= (this.config.maxDrawdown || 0.3)) {
            this.log('warn', "Max drawdown reached: ".concat((currentDrawdown * 100).toFixed(2), "%"));
            return false;
        }
        // 检查连续亏损后的冷却期
        if (this.metrics.consecutiveLosses >= 3 && this.metrics.lastLossTime) {
            var timeSinceLastLoss = Date.now() - this.metrics.lastLossTime.getTime();
            if (timeSinceLastLoss < (this.config.cooldownPeriod || 300000)) {
                var remainingCooldown = (this.config.cooldownPeriod || 300000) - timeSinceLastLoss;
                this.log('warn', "In cooldown period: ".concat(Math.ceil(remainingCooldown / 1000), "s remaining"));
                return false;
            }
        }
        // 检查风险评分
        var riskScore = this.calculateRiskScore();
        if (riskScore > 80) {
            this.log('warn', "Risk score too high: ".concat(riskScore));
            return false;
        }
        return true;
    };
    /**
     * 检查是否可以承担指定金额的交易
     */
    RiskManager.prototype.canAffordTrade = function (tradeAmount) {
        // 检查余额是否充足
        if (tradeAmount > this.metrics.currentBalance) {
            this.log('warn', "Insufficient balance: ".concat(this.metrics.currentBalance.toFixed(4), " SOL < ").concat(tradeAmount, " SOL"));
            return false;
        }
        // 检查单笔交易风险
        if (tradeAmount > (this.config.maxLossPerTrade || 0.1)) {
            this.log('warn', "Trade amount exceeds max loss per trade: ".concat(tradeAmount, " SOL > ").concat(this.config.maxLossPerTrade, " SOL"));
            return false;
        }
        // 检查仓位大小限制
        var positionSizeRatio = tradeAmount / this.metrics.currentBalance;
        if (positionSizeRatio > (this.config.maxPositionSize || 0.2)) {
            this.log('warn', "Position size too large: ".concat((positionSizeRatio * 100).toFixed(2), "% > ").concat(((this.config.maxPositionSize || 0.2) * 100).toFixed(2), "%"));
            return false;
        }
        return true;
    };
    /**
     * 记录交易结果
     */
    RiskManager.prototype.recordTrade = function (pnl, tradeAmount) {
        var isWinning = pnl > 0;
        var timestamp = new Date();
        // 更新余额和PnL
        this.metrics.currentBalance += pnl;
        this.metrics.dailyPnL += pnl;
        this.metrics.totalTrades++;
        this.metrics.lastTradeTime = timestamp;
        // 更新高水位线
        if (this.metrics.currentBalance > this.highWaterMark) {
            this.highWaterMark = this.metrics.currentBalance;
        }
        // 更新最大回撤
        var currentDrawdown = (this.highWaterMark - this.metrics.currentBalance) / this.highWaterMark;
        if (currentDrawdown > this.metrics.maxDrawdown) {
            this.metrics.maxDrawdown = currentDrawdown;
        }
        // 更新胜负统计
        if (isWinning) {
            this.metrics.winningTrades++;
            this.metrics.consecutiveLosses = 0; // 重置连续亏损计数
        }
        else {
            this.metrics.losingTrades++;
            this.metrics.consecutiveLosses++;
            this.metrics.lastLossTime = timestamp;
        }
        // 添加到交易历史
        this.tradeHistory.push({
            timestamp: timestamp,
            pnl: pnl,
            success: isWinning
        });
        // 保持历史记录在合理范围内
        if (this.tradeHistory.length > 1000) {
            this.tradeHistory = this.tradeHistory.slice(-1000);
        }
        // 更新风险评分
        this.metrics.riskScore = this.calculateRiskScore();
        this.log('info', "Trade recorded: PnL ".concat(pnl.toFixed(4), " SOL, Balance: ").concat(this.metrics.currentBalance.toFixed(4), " SOL, Risk Score: ").concat(this.metrics.riskScore));
    };
    /**
     * 计算建议的仓位大小
     */
    RiskManager.prototype.calculatePositionSize = function (accountBalance, riskPerTrade) {
        if (riskPerTrade === void 0) { riskPerTrade = 0.02; }
        // Kelly Criterion改良版
        var winRate = this.metrics.totalTrades > 0 ? this.metrics.winningTrades / this.metrics.totalTrades : 0.5;
        var avgWin = this.getAverageWin();
        var avgLoss = Math.abs(this.getAverageLoss());
        if (avgLoss === 0) {
            return Math.min(accountBalance * riskPerTrade, this.config.maxLossPerTrade || 0.1);
        }
        var payoffRatio = avgWin / avgLoss;
        var kellyFraction = (winRate * payoffRatio - (1 - winRate)) / payoffRatio;
        // 限制Kelly分数在合理范围内
        var adjustedKelly = Math.max(0, Math.min(kellyFraction, 0.25)); // 最大25%
        // 根据风险评分调整
        var riskAdjustment = Math.max(0.1, 1 - (this.metrics.riskScore / 100));
        var suggestedSize = accountBalance * adjustedKelly * riskAdjustment;
        // 确保不超过最大限制
        return Math.min(suggestedSize, this.config.maxLossPerTrade || 0.1, accountBalance * (this.config.maxPositionSize || 0.2));
    };
    /**
     * 获取风险指标
     */
    RiskManager.prototype.getRiskMetrics = function () {
        return __assign({}, this.metrics);
    };
    /**
     * 计算风险评分 (0-100)
     */
    RiskManager.prototype.calculateRiskScore = function () {
        var score = 0;
        // 回撤风险 (0-25分)
        var drawdownRisk = (this.metrics.maxDrawdown / (this.config.maxDrawdown || 0.3)) * 25;
        score += Math.min(drawdownRisk, 25);
        // 连续亏损风险 (0-20分)
        var consecutiveLossRisk = Math.min(this.metrics.consecutiveLosses / 5, 1) * 20;
        score += consecutiveLossRisk;
        // 日内亏损风险 (0-25分)
        var dailyLossRisk = Math.abs(this.metrics.dailyPnL) / Math.abs(this.config.maxDailyLoss || 0.5) * 25;
        score += Math.min(dailyLossRisk, 25);
        // 胜率风险 (0-15分)
        var winRate = this.metrics.totalTrades > 0 ? this.metrics.winningTrades / this.metrics.totalTrades : 0.5;
        var winRateRisk = (1 - winRate) * 15;
        score += winRateRisk;
        // 资金使用率风险 (0-15分)
        var capitalUsageRisk = (this.config.initialCapital - this.metrics.currentBalance) / this.config.initialCapital * 15;
        score += Math.max(0, capitalUsageRisk);
        return Math.min(100, Math.max(0, score));
    };
    /**
     * 获取平均盈利
     */
    RiskManager.prototype.getAverageWin = function () {
        var winningTrades = this.tradeHistory.filter(function (trade) { return trade.pnl > 0; });
        if (winningTrades.length === 0)
            return 0;
        var totalWins = winningTrades.reduce(function (sum, trade) { return sum + trade.pnl; }, 0);
        return totalWins / winningTrades.length;
    };
    /**
     * 获取平均亏损
     */
    RiskManager.prototype.getAverageLoss = function () {
        var losingTrades = this.tradeHistory.filter(function (trade) { return trade.pnl < 0; });
        if (losingTrades.length === 0)
            return 0;
        var totalLosses = losingTrades.reduce(function (sum, trade) { return sum + trade.pnl; }, 0);
        return totalLosses / losingTrades.length;
    };
    /**
     * 重置日内统计
     */
    RiskManager.prototype.resetDailyMetrics = function () {
        this.dailyStartBalance = this.metrics.currentBalance;
        this.metrics.dailyPnL = 0;
        this.log('info', 'Daily metrics reset');
    };
    /**
     * 更新配置
     */
    RiskManager.prototype.updateConfig = function (newConfig) {
        this.config = __assign(__assign({}, this.config), newConfig);
        this.log('info', 'Risk manager configuration updated');
    };
    /**
     * 强制重置风险状态（紧急情况使用）
     */
    RiskManager.prototype.emergencyReset = function () {
        this.metrics.consecutiveLosses = 0;
        this.metrics.lastLossTime = null;
        this.metrics.riskScore = this.calculateRiskScore();
        this.log('warn', 'Emergency risk reset performed');
    };
    /**
     * 获取交易建议
     */
    RiskManager.prototype.getTradingAdvice = function () {
        var canTrade = this.canTrade();
        var riskScore = this.metrics.riskScore;
        var suggestedSize = this.calculatePositionSize(this.metrics.currentBalance);
        var riskLevel;
        var reason = '';
        if (riskScore < 30) {
            riskLevel = 'LOW';
            reason = 'Risk conditions are favorable for trading';
        }
        else if (riskScore < 60) {
            riskLevel = 'MEDIUM';
            reason = 'Moderate risk conditions, trade with caution';
        }
        else if (riskScore < 80) {
            riskLevel = 'HIGH';
            reason = 'High risk conditions, consider reducing position sizes';
        }
        else {
            riskLevel = 'CRITICAL';
            reason = 'Critical risk levels, trading not recommended';
        }
        if (!canTrade) {
            reason = 'Trading blocked by risk management rules';
        }
        return {
            canTrade: canTrade,
            reason: reason,
            suggestedPositionSize: suggestedSize,
            riskLevel: riskLevel
        };
    };
    /**
     * 日志记录
     */
    RiskManager.prototype.log = function (level, message) {
        var timestamp = new Date().toISOString();
        console.log("[".concat(timestamp, "] [").concat(level.toUpperCase(), "] [RiskManager] ").concat(message));
    };
    return RiskManager;
}());
exports.RiskManager = RiskManager;
