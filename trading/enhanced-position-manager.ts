import { Position, TradingConfig } from '../types/trading-interfaces';

export class EnhancedPositionManager {
  private positions: Map<string, Position> = new Map();
  private positionHistory: Position[] = [];
  private config: TradingConfig;

  constructor(config: TradingConfig) {
    this.config = config;
  }

  public openPosition(tokenAddress: string, amount: number, entryPrice: number): Position {
    const position: Position = {
      id: `${tokenAddress}_${Date.now()}`,
      tokenAddress,
      amount,
      entryPrice,
      timestamp: new Date(),
      status: 'open',
      stopLossPrice: entryPrice * (1 - this.config.stopLossPercentage / 100)
    };

    this.positions.set(tokenAddress, position);
    console.log(`📈 开仓: ${tokenAddress.slice(0, 8)}... 数量: ${amount} 价格: ${entryPrice}`);
    
    return position;
  }

  public closePosition(tokenAddress: string, exitPrice: number, pnl: number): Position | null {
    const position = this.positions.get(tokenAddress);
    if (!position) {
      console.warn(`⚠️  尝试平仓不存在的仓位: ${tokenAddress}`);
      return null;
    }

    // 更新仓位信息
    position.status = 'closed';
    position.exitPrice = exitPrice;
    position.exitTimestamp = new Date();
    position.pnl = pnl;

    // 移动到历史记录
    this.positionHistory.push({ ...position });
    this.positions.delete(tokenAddress);

    // 保持历史记录在合理范围内
    if (this.positionHistory.length > 1000) {
      this.positionHistory = this.positionHistory.slice(-1000);
    }

    const holdingTime = position.exitTimestamp.getTime() - position.timestamp.getTime();
    const holdingMinutes = Math.round(holdingTime / (1000 * 60));
    
    console.log(`📉 平仓: ${tokenAddress.slice(0, 8)}... 价格: ${exitPrice} PnL: ${pnl.toFixed(4)} SOL 持仓时间: ${holdingMinutes}分钟`);
    
    return position;
  }

  public checkStopLoss(tokenAddress: string, currentPrice: number): boolean {
    const position = this.positions.get(tokenAddress);
    if (!position || !position.stopLossPrice) {
      return false;
    }

    const shouldStopLoss = currentPrice <= position.stopLossPrice;
    if (shouldStopLoss) {
      console.log(`🛑 触发止损: ${tokenAddress.slice(0, 8)}... 当前价格: ${currentPrice} 止损价格: ${position.stopLossPrice}`);
    }

    return shouldStopLoss;
  }

  public updateStopLoss(tokenAddress: string, newStopLossPrice: number): boolean {
    const position = this.positions.get(tokenAddress);
    if (!position) {
      return false;
    }

    position.stopLossPrice = newStopLossPrice;
    console.log(`🎯 更新止损: ${tokenAddress.slice(0, 8)}... 新止损价格: ${newStopLossPrice}`);
    return true;
  }

  public getPosition(tokenAddress: string): Position | undefined {
    return this.positions.get(tokenAddress);
  }

  public getActivePositions(): Position[] {
    return Array.from(this.positions.values());
  }

  public canOpenNewPosition(): boolean {
    return this.positions.size < this.config.maxPositions;
  }

  public getPositionStats(): {
    activePositions: number;
    totalPositions: number;
    winningPositions: number;
    losingPositions: number;
    winRate: number;
    totalRealizedPnL: number;
    averagePnL: number;
    averageHoldingTime: number;
  } {
    const closedPositions = this.positionHistory.filter(p => p.status === 'closed' && p.pnl !== undefined);
    const winningPositions = closedPositions.filter(p => p.pnl! > 0);
    const losingPositions = closedPositions.filter(p => p.pnl! <= 0);
    
    const totalRealizedPnL = closedPositions.reduce((sum, p) => sum + (p.pnl || 0), 0);
    const averagePnL = closedPositions.length > 0 ? totalRealizedPnL / closedPositions.length : 0;
    
    // 计算平均持仓时间（分钟）
    const holdingTimes = closedPositions
      .filter(p => p.exitTimestamp)
      .map(p => p.exitTimestamp!.getTime() - p.timestamp.getTime());
    const averageHoldingTime = holdingTimes.length > 0 
      ? holdingTimes.reduce((sum, time) => sum + time, 0) / (holdingTimes.length * 1000 * 60)
      : 0;

    return {
      activePositions: this.positions.size,
      totalPositions: this.positionHistory.length,
      winningPositions: winningPositions.length,
      losingPositions: losingPositions.length,
      winRate: closedPositions.length > 0 ? (winningPositions.length / closedPositions.length) * 100 : 0,
      totalRealizedPnL,
      averagePnL,
      averageHoldingTime
    };
  }

  public getPositionValue(tokenAddress: string, currentPrice: number): number {
    const position = this.positions.get(tokenAddress);
    if (!position) return 0;
    
    return position.amount * currentPrice;
  }

  public getUnrealizedPnL(tokenAddress: string, currentPrice: number): number {
    const position = this.positions.get(tokenAddress);
    if (!position) return 0;
    
    const currentValue = position.amount * currentPrice;
    const originalValue = position.amount * position.entryPrice;
    return currentValue - originalValue;
  }

  public getAllUnrealizedPnL(priceMap: Map<string, number>): number {
    let totalUnrealized = 0;
    
    for (const [tokenAddress, position] of this.positions) {
      const currentPrice = priceMap.get(tokenAddress);
      if (currentPrice) {
        totalUnrealized += this.getUnrealizedPnL(tokenAddress, currentPrice);
      }
    }
    
    return totalUnrealized;
  }

  public getPositionRisk(tokenAddress: string, currentPrice: number): {
    riskLevel: 'low' | 'medium' | 'high';
    drawdown: number;
    daysSinceOpen: number;
  } {
    const position = this.positions.get(tokenAddress);
    if (!position) {
      return { riskLevel: 'low', drawdown: 0, daysSinceOpen: 0 };
    }

    const unrealizedPnL = this.getUnrealizedPnL(tokenAddress, currentPrice);
    const originalValue = position.amount * position.entryPrice;
    const drawdown = Math.abs(unrealizedPnL / originalValue) * 100;
    
    const daysSinceOpen = (Date.now() - position.timestamp.getTime()) / (1000 * 60 * 60 * 24);
    
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    if (drawdown > 20 || daysSinceOpen > 7) {
      riskLevel = 'high';
    } else if (drawdown > 10 || daysSinceOpen > 3) {
      riskLevel = 'medium';
    }

    return { riskLevel, drawdown, daysSinceOpen };
  }

  public getHighRiskPositions(priceMap: Map<string, number>): Array<{
    tokenAddress: string;
    position: Position;
    risk: { riskLevel: 'low' | 'medium' | 'high'; drawdown: number; daysSinceOpen: number };
  }> {
    const highRiskPositions = [];
    
    for (const [tokenAddress, position] of this.positions) {
      const currentPrice = priceMap.get(tokenAddress);
      if (currentPrice) {
        const risk = this.getPositionRisk(tokenAddress, currentPrice);
        if (risk.riskLevel === 'high') {
          highRiskPositions.push({ tokenAddress, position, risk });
        }
      }
    }
    
    return highRiskPositions;
  }

  public liquidateAllPositions(): Position[] {
    const positionsToLiquidate = Array.from(this.positions.values());
    this.positions.clear();
    
    console.log(`🚨 强制平仓所有持仓 (${positionsToLiquidate.length}个)`);
    return positionsToLiquidate;
  }

  public getPositionSummary(): string {
    const stats = this.getPositionStats();
    return `📊 仓位概览: 活跃${stats.activePositions}个 | 总计${stats.totalPositions}个 | 胜率${stats.winRate.toFixed(1)}% | 平均PnL${stats.averagePnL.toFixed(4)}SOL`;
  }
} 