import { BuyPredictor, TransactionData, BuyPredictionResult } from '../predictors/buy-predictor';
import { SellPredictor, SellPredictionResult } from '../predictors/sell-predictor';
import { PumpSwapManager } from './pump-swap-manager';
import { PositionManager } from './position-manager';
import { RiskManager } from './risk-manager';

export interface TradingConfig {
  // 模型配置
  buyModelPath?: string;
  sellModelPath?: string;
  buyThreshold?: number;
  sellThreshold?: number;
  
  // 交易配置
  tradeAmountSol?: number;
  maxPositions?: number;
  initialCapitalSol?: number;
  
  // 风控配置
  maxLossPerTradeSol?: number;
  stopLossPercentage?: number;
  takeProfitPercentage?: number;
  maxDailyLoss?: number;
  
  // 其他配置
  enablePaperTrading?: boolean;
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
}

export interface TradeSignal {
  type: 'buy' | 'sell';
  tokenAddress: string;
  probability: number;
  confidence: number;
  timestamp: Date;
  prediction: BuyPredictionResult | SellPredictionResult;
}

export interface TradeResult {
  success: boolean;
  transactionId?: string;
  error?: string;
  actualPrice?: number;
  slippage?: number;
  gasFee?: number;
}

export interface TradingStats {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  winRate: number;
  totalPnL: number;
  totalFees: number;
  averagePnL: number;
  maxDrawdown: number;
  currentBalance: number;
  activePositions: number;
}

export class TradingController {
  private buyPredictor: BuyPredictor;
  private sellPredictor: SellPredictor;
  private swapManager: PumpSwapManager;
  private positionManager: PositionManager;
  private riskManager: RiskManager;
  
  private config: TradingConfig;
  private isRunning: boolean = false;
  private tokenSubscriptions: Map<string, boolean> = new Map();
  private transactionHistory: Map<string, TransactionData[]> = new Map();
  
  // 交易统计
  private stats: TradingStats = {
    totalTrades: 0,
    winningTrades: 0,
    losingTrades: 0,
    winRate: 0,
    totalPnL: 0,
    totalFees: 0,
    averagePnL: 0,
    maxDrawdown: 0,
    currentBalance: 0,
    activePositions: 0
  };

  constructor(config: TradingConfig) {
    this.config = {
      buyModelPath: '../catboost_focal_blocktol_2025.cbm',
      sellModelPath: '../best_catboost_sell_model.cbm',
      buyThreshold: 0.5,
      sellThreshold: 0.7,
      tradeAmountSol: 0.1,
      maxPositions: 5,
      initialCapitalSol: 3.0,
      maxLossPerTradeSol: 0.1,
      stopLossPercentage: 0.1, // 10%
      takeProfitPercentage: 0.2, // 20%
      maxDailyLoss: 0.5,
      enablePaperTrading: false,
      logLevel: 'info',
      ...config
    };

    this.initializeComponents();
  }

  private initializeComponents(): void {
    // 初始化预测器
    this.buyPredictor = new BuyPredictor(
      this.config.buyModelPath,
      10, // window size for buy
      this.config.buyThreshold
    );

    this.sellPredictor = new SellPredictor(
      this.config.sellModelPath,
      30, // window size for sell
      this.config.sellThreshold
    );

    // 初始化交易管理器
    this.swapManager = new PumpSwapManager({
      paperTrading: this.config.enablePaperTrading
    });

    this.positionManager = new PositionManager({
      maxPositions: this.config.maxPositions,
      tradeAmount: this.config.tradeAmountSol
    });

    this.riskManager = new RiskManager({
      initialCapital: this.config.initialCapitalSol,
      maxLossPerTrade: this.config.maxLossPerTradeSol,
      stopLossPercentage: this.config.stopLossPercentage,
      takeProfitPercentage: this.config.takeProfitPercentage,
      maxDailyLoss: this.config.maxDailyLoss
    });

    // 初始化统计
    this.stats.currentBalance = this.config.initialCapitalSol || 3.0;
  }

  /**
   * 开始交易控制器
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      this.log('warn', 'Trading controller is already running');
      return;
    }

    this.isRunning = true;
    this.log('info', 'Trading controller started');
    
    // 启动各个组件
    await this.swapManager.initialize();
    this.positionManager.start();
    this.riskManager.start();

    // 开始定期检查（每秒检查一次）
    this.startPeriodicChecks();
  }

  /**
   * 停止交易控制器
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    this.log('info', 'Trading controller stopped');
    
    // 停止各个组件
    this.positionManager.stop();
    this.riskManager.stop();
  }

  /**
   * 订阅Token进行交易
   */
  public subscribeToken(tokenAddress: string): void {
    if (this.tokenSubscriptions.has(tokenAddress)) {
      this.log('warn', `Token ${tokenAddress} is already subscribed`);
      return;
    }

    this.tokenSubscriptions.set(tokenAddress, true);
    this.transactionHistory.set(tokenAddress, []);
    this.log('info', `Subscribed to token: ${tokenAddress}`);
  }

  /**
   * 取消订阅Token
   */
  public unsubscribeToken(tokenAddress: string): void {
    if (!this.tokenSubscriptions.has(tokenAddress)) {
      this.log('warn', `Token ${tokenAddress} is not subscribed`);
      return;
    }

    this.tokenSubscriptions.delete(tokenAddress);
    this.transactionHistory.delete(tokenAddress);
    
    // 关闭该Token的所有持仓
    this.positionManager.closeAllPositions(tokenAddress);
    
    this.log('info', `Unsubscribed from token: ${tokenAddress}`);
  }

  /**
   * 处理新的交易数据
   */
  public async onNewTransaction(tokenAddress: string, transaction: TransactionData): Promise<void> {
    if (!this.isRunning || !this.tokenSubscriptions.has(tokenAddress)) {
      return;
    }

    // 添加到历史记录
    const history = this.transactionHistory.get(tokenAddress) || [];
    history.push(transaction);
    
    // 保持历史记录在合理范围内
    if (history.length > 1000) {
      history.splice(0, history.length - 1000);
    }
    
    this.transactionHistory.set(tokenAddress, history);

    // 更新预测器历史
    this.buyPredictor.addTransaction(transaction);
    this.sellPredictor.addTransaction(transaction);

    // 进行预测和交易决策
    await this.makeTradingDecision(tokenAddress);
  }

  /**
   * 执行交易决策
   */
  private async makeTradingDecision(tokenAddress: string): Promise<void> {
    try {
      // 检查风控条件
      if (!this.riskManager.canTrade()) {
        this.log('warn', 'Trading blocked by risk manager');
        return;
      }

      const currentPosition = this.positionManager.getPosition(tokenAddress);

      if (!currentPosition) {
        // 没有持仓，检查买入信号
        const buySignal = await this.checkBuySignal(tokenAddress);
        if (buySignal) {
          await this.executeBuy(tokenAddress, buySignal);
        }
      } else {
        // 有持仓，检查卖出信号
        const sellSignal = await this.checkSellSignal(tokenAddress);
        if (sellSignal) {
          await this.executeSell(tokenAddress, sellSignal);
        }
        
        // 同时检查止损和止盈
        await this.checkStopLossAndTakeProfit(tokenAddress, currentPosition);
      }

    } catch (error) {
      this.log('error', `Error in trading decision for ${tokenAddress}: ${error}`);
    }
  }

  /**
   * 检查买入信号
   */
  private async checkBuySignal(tokenAddress: string): Promise<TradeSignal | null> {
    const prediction = await this.buyPredictor.predictBuy();
    
    if (prediction.prediction && prediction.probability > this.config.buyThreshold!) {
      this.log('info', `Buy signal detected for ${tokenAddress}: ${prediction.probability.toFixed(3)}`);
      
      return {
        type: 'buy',
        tokenAddress,
        probability: prediction.probability,
        confidence: prediction.confidence,
        timestamp: prediction.timestamp,
        prediction
      };
    }

    return null;
  }

  /**
   * 检查卖出信号
   */
  private async checkSellSignal(tokenAddress: string): Promise<TradeSignal | null> {
    const prediction = await this.sellPredictor.predictSell();
    
    if (prediction.prediction && prediction.probability > this.config.sellThreshold!) {
      this.log('info', `Sell signal detected for ${tokenAddress}: ${prediction.probability.toFixed(3)}`);
      
      return {
        type: 'sell',
        tokenAddress,
        probability: prediction.probability,
        confidence: prediction.confidence,
        timestamp: prediction.timestamp,
        prediction
      };
    }

    return null;
  }

  /**
   * 执行买入交易
   */
  private async executeBuy(tokenAddress: string, signal: TradeSignal): Promise<void> {
    try {
      // 检查是否可以开新仓
      if (!this.positionManager.canOpenNewPosition()) {
        this.log('warn', `Cannot open new position for ${tokenAddress}: max positions reached`);
        return;
      }

      // 检查资金是否充足
      if (!this.riskManager.canAffordTrade(this.config.tradeAmountSol!)) {
        this.log('warn', `Cannot afford trade for ${tokenAddress}: insufficient balance`);
        return;
      }

      this.log('info', `Executing buy for ${tokenAddress}, amount: ${this.config.tradeAmountSol} SOL`);

      // 执行买入交易
      const tradeResult = await this.swapManager.buy(
        tokenAddress,
        this.config.tradeAmountSol!,
        0.05 // 5% slippage tolerance
      );

      if (tradeResult.success) {
        // 记录新仓位
        this.positionManager.openPosition({
          tokenAddress,
          side: 'long',
          amount: this.config.tradeAmountSol!,
          entryPrice: tradeResult.actualPrice!,
          timestamp: new Date(),
          transactionId: tradeResult.transactionId!
        });

        // 更新统计
        this.stats.totalTrades++;
        this.stats.totalFees += tradeResult.gasFee || 0;
        this.stats.activePositions = this.positionManager.getActivePositionCount();

        this.log('info', `Buy executed successfully for ${tokenAddress}: ${tradeResult.transactionId}`);
      } else {
        this.log('error', `Buy failed for ${tokenAddress}: ${tradeResult.error}`);
      }

    } catch (error) {
      this.log('error', `Error executing buy for ${tokenAddress}: ${error}`);
    }
  }

  /**
   * 执行卖出交易
   */
  private async executeSell(tokenAddress: string, signal: TradeSignal): Promise<void> {
    try {
      const position = this.positionManager.getPosition(tokenAddress);
      if (!position) {
        this.log('warn', `No position to sell for ${tokenAddress}`);
        return;
      }

      this.log('info', `Executing sell for ${tokenAddress}, amount: ${position.amount} SOL`);

      // 执行卖出交易
      const tradeResult = await this.swapManager.sell(
        tokenAddress,
        position.amount,
        0.05 // 5% slippage tolerance
      );

      if (tradeResult.success) {
        // 计算盈亏
        const pnl = (tradeResult.actualPrice! - position.entryPrice) * position.amount;
        const isWinning = pnl > 0;

        // 关闭仓位
        this.positionManager.closePosition(tokenAddress, {
          exitPrice: tradeResult.actualPrice!,
          timestamp: new Date(),
          transactionId: tradeResult.transactionId!,
          pnl
        });

        // 更新统计
        this.stats.totalTrades++;
        this.stats.totalFees += tradeResult.gasFee || 0;
        this.stats.totalPnL += pnl;
        this.stats.currentBalance += pnl;
        this.stats.activePositions = this.positionManager.getActivePositionCount();
        
        if (isWinning) {
          this.stats.winningTrades++;
        } else {
          this.stats.losingTrades++;
        }
        
        this.stats.winRate = this.stats.winningTrades / this.stats.totalTrades;
        this.stats.averagePnL = this.stats.totalPnL / this.stats.totalTrades;

        this.log('info', `Sell executed successfully for ${tokenAddress}: ${tradeResult.transactionId}, PnL: ${pnl.toFixed(4)} SOL`);
      } else {
        this.log('error', `Sell failed for ${tokenAddress}: ${tradeResult.error}`);
      }

    } catch (error) {
      this.log('error', `Error executing sell for ${tokenAddress}: ${error}`);
    }
  }

  /**
   * 检查止损和止盈
   */
  private async checkStopLossAndTakeProfit(tokenAddress: string, position: any): Promise<void> {
    try {
      // 获取当前价格
      const currentPrice = await this.swapManager.getCurrentPrice(tokenAddress);
      const pnlPercentage = (currentPrice - position.entryPrice) / position.entryPrice;

      // 检查止损
      if (pnlPercentage <= -this.config.stopLossPercentage!) {
        this.log('warn', `Stop loss triggered for ${tokenAddress}: ${(pnlPercentage * 100).toFixed(2)}%`);
        await this.executeSell(tokenAddress, {
          type: 'sell',
          tokenAddress,
          probability: 1.0,
          confidence: 1.0,
          timestamp: new Date(),
          prediction: {
            probability: 1.0,
            prediction: true,
            confidence: 1.0,
            timestamp: new Date(),
            predictionTimeMs: 0,
            predictionTimeMicros: 0
          }
        });
        return;
      }

      // 检查止盈
      if (pnlPercentage >= this.config.takeProfitPercentage!) {
        this.log('info', `Take profit triggered for ${tokenAddress}: ${(pnlPercentage * 100).toFixed(2)}%`);
        await this.executeSell(tokenAddress, {
          type: 'sell',
          tokenAddress,
          probability: 1.0,
          confidence: 1.0,
          timestamp: new Date(),
          prediction: {
            probability: 1.0,
            prediction: true,
            confidence: 1.0,
            timestamp: new Date(),
            predictionTimeMs: 0,
            predictionTimeMicros: 0
          }
        });
      }

    } catch (error) {
      this.log('error', `Error checking stop loss/take profit for ${tokenAddress}: ${error}`);
    }
  }

  /**
   * 开始定期检查
   */
  private startPeriodicChecks(): void {
    setInterval(async () => {
      if (!this.isRunning) return;

      // 检查所有持仓的止损止盈
      for (const tokenAddress of this.tokenSubscriptions.keys()) {
        const position = this.positionManager.getPosition(tokenAddress);
        if (position) {
          await this.checkStopLossAndTakeProfit(tokenAddress, position);
        }
      }

      // 定期输出统计信息
      if (this.stats.totalTrades > 0 && this.stats.totalTrades % 10 === 0) {
        this.logTradingStats();
      }
    }, 1000); // 每秒检查一次
  }

  /**
   * 获取交易统计
   */
  public getTradingStats(): TradingStats {
    return { ...this.stats };
  }

  /**
   * 输出交易统计
   */
  private logTradingStats(): void {
    this.log('info', '=== Trading Statistics ===');
    this.log('info', `Total Trades: ${this.stats.totalTrades}`);
    this.log('info', `Win Rate: ${(this.stats.winRate * 100).toFixed(2)}%`);
    this.log('info', `Total PnL: ${this.stats.totalPnL.toFixed(4)} SOL`);
    this.log('info', `Average PnL: ${this.stats.averagePnL.toFixed(4)} SOL`);
    this.log('info', `Current Balance: ${this.stats.currentBalance.toFixed(4)} SOL`);
    this.log('info', `Active Positions: ${this.stats.activePositions}`);
    this.log('info', `Total Fees: ${this.stats.totalFees.toFixed(4)} SOL`);
    this.log('info', '========================');
  }

  /**
   * 获取订阅的Token列表
   */
  public getSubscribedTokens(): string[] {
    return Array.from(this.tokenSubscriptions.keys());
  }

  /**
   * 获取Token的交易历史
   */
  public getTokenHistory(tokenAddress: string): TransactionData[] {
    return this.transactionHistory.get(tokenAddress) || [];
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<TradingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 更新子组件配置
    if (newConfig.buyThreshold !== undefined) {
      this.buyPredictor.setThreshold(newConfig.buyThreshold);
    }
    if (newConfig.sellThreshold !== undefined) {
      this.sellPredictor.setThreshold(newConfig.sellThreshold);
    }
  }

  /**
   * 日志记录
   */
  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string): void {
    const levels = { debug: 0, info: 1, warn: 2, error: 3 };
    const configLevel = levels[this.config.logLevel || 'info'];
    
    if (levels[level] >= configLevel) {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] [${level.toUpperCase()}] [TradingController] ${message}`);
    }
  }
} 