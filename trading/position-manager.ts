export interface Position {
  id: string;
  tokenAddress: string;
  side: 'long' | 'short';
  amount: number;
  entryPrice: number;
  timestamp: Date;
  transactionId: string;
  status: 'open' | 'closed';
  exitPrice?: number;
  exitTimestamp?: Date;
  exitTransactionId?: string;
  pnl?: number;
}

export interface PositionConfig {
  maxPositions?: number;
  tradeAmount?: number;
}

export interface ClosePositionData {
  exitPrice: number;
  timestamp: Date;
  transactionId: string;
  pnl: number;
}

export class PositionManager {
  private positions: Map<string, Position> = new Map();
  private config: PositionConfig;
  private positionHistory: Position[] = [];

  constructor(config: PositionConfig = {}) {
    this.config = {
      maxPositions: 5,
      tradeAmount: 0.1,
      ...config
    };
  }

  /**
   * 启动仓位管理器
   */
  public start(): void {
    this.log('info', 'Position manager started');
  }

  /**
   * 停止仓位管理器
   */
  public stop(): void {
    this.log('info', 'Position manager stopped');
  }

  /**
   * 开启新仓位
   */
  public openPosition(positionData: {
    tokenAddress: string;
    side: 'long' | 'short';
    amount: number;
    entryPrice: number;
    timestamp: Date;
    transactionId: string;
  }): Position {
    const position: Position = {
      id: this.generatePositionId(),
      ...positionData,
      status: 'open'
    };

    this.positions.set(position.tokenAddress, position);
    this.log('info', `Opened position: ${position.id} for ${position.tokenAddress}, amount: ${position.amount} SOL`);

    return position;
  }

  /**
   * 关闭仓位
   */
  public closePosition(tokenAddress: string, closeData: ClosePositionData): Position | null {
    const position = this.positions.get(tokenAddress);
    
    if (!position) {
      this.log('warn', `No position found for token: ${tokenAddress}`);
      return null;
    }

    if (position.status === 'closed') {
      this.log('warn', `Position ${position.id} is already closed`);
      return position;
    }

    // 更新仓位信息
    position.exitPrice = closeData.exitPrice;
    position.exitTimestamp = closeData.timestamp;
    position.exitTransactionId = closeData.transactionId;
    position.pnl = closeData.pnl;
    position.status = 'closed';

    // 从活跃仓位中移除，添加到历史记录
    this.positions.delete(tokenAddress);
    this.positionHistory.push(position);

    // 保持历史记录在合理范围内
    if (this.positionHistory.length > 1000) {
      this.positionHistory = this.positionHistory.slice(-1000);
    }

    this.log('info', `Closed position: ${position.id} for ${position.tokenAddress}, PnL: ${position.pnl?.toFixed(4)} SOL`);

    return position;
  }

  /**
   * 关闭指定token的所有仓位
   */
  public closeAllPositions(tokenAddress: string): Position[] {
    const closedPositions: Position[] = [];
    const position = this.positions.get(tokenAddress);

    if (position) {
      // 强制关闭仓位（用于紧急情况）
      const closeData: ClosePositionData = {
        exitPrice: position.entryPrice, // 使用入场价格作为退出价格
        timestamp: new Date(),
        transactionId: `force_close_${Date.now()}`,
        pnl: 0 // 强制平仓不计算盈亏
      };

      const closedPosition = this.closePosition(tokenAddress, closeData);
      if (closedPosition) {
        closedPositions.push(closedPosition);
      }
    }

    return closedPositions;
  }

  /**
   * 获取指定token的仓位
   */
  public getPosition(tokenAddress: string): Position | null {
    return this.positions.get(tokenAddress) || null;
  }

  /**
   * 获取所有活跃仓位
   */
  public getActivePositions(): Position[] {
    return Array.from(this.positions.values());
  }

  /**
   * 获取活跃仓位数量
   */
  public getActivePositionCount(): number {
    return this.positions.size;
  }

  /**
   * 检查是否可以开新仓位
   */
  public canOpenNewPosition(): boolean {
    return this.positions.size < (this.config.maxPositions || 5);
  }

  /**
   * 获取仓位历史
   */
  public getPositionHistory(): Position[] {
    return [...this.positionHistory];
  }

  /**
   * 获取指定token的仓位历史
   */
  public getTokenPositionHistory(tokenAddress: string): Position[] {
    return this.positionHistory.filter(pos => pos.tokenAddress === tokenAddress);
  }

  /**
   * 计算总未实现盈亏
   */
  public async calculateUnrealizedPnL(priceProvider: (tokenAddress: string) => Promise<number>): Promise<number> {
    let totalUnrealizedPnL = 0;

    for (const position of this.positions.values()) {
      try {
        const currentPrice = await priceProvider(position.tokenAddress);
        const unrealizedPnL = (currentPrice - position.entryPrice) * position.amount;
        totalUnrealizedPnL += unrealizedPnL;
      } catch (error) {
        this.log('warn', `Failed to calculate unrealized PnL for ${position.tokenAddress}: ${error}`);
      }
    }

    return totalUnrealizedPnL;
  }

  /**
   * 计算总已实现盈亏
   */
  public calculateRealizedPnL(): number {
    return this.positionHistory
      .filter(pos => pos.pnl !== undefined)
      .reduce((total, pos) => total + (pos.pnl || 0), 0);
  }

  /**
   * 获取仓位统计信息
   */
  public getPositionStats(): {
    activePositions: number;
    totalPositions: number;
    winningPositions: number;
    losingPositions: number;
    winRate: number;
    totalRealizedPnL: number;
    averagePnL: number;
    maxWin: number;
    maxLoss: number;
  } {
    const realizedPositions = this.positionHistory.filter(pos => pos.pnl !== undefined);
    const winningPositions = realizedPositions.filter(pos => (pos.pnl || 0) > 0);
    const losingPositions = realizedPositions.filter(pos => (pos.pnl || 0) < 0);
    
    const totalRealizedPnL = this.calculateRealizedPnL();
    const winRate = realizedPositions.length > 0 ? winningPositions.length / realizedPositions.length : 0;
    const averagePnL = realizedPositions.length > 0 ? totalRealizedPnL / realizedPositions.length : 0;
    
    const pnls = realizedPositions.map(pos => pos.pnl || 0);
    const maxWin = pnls.length > 0 ? Math.max(...pnls) : 0;
    const maxLoss = pnls.length > 0 ? Math.min(...pnls) : 0;

    return {
      activePositions: this.positions.size,
      totalPositions: realizedPositions.length,
      winningPositions: winningPositions.length,
      losingPositions: losingPositions.length,
      winRate,
      totalRealizedPnL,
      averagePnL,
      maxWin,
      maxLoss
    };
  }

  /**
   * 更新仓位的市场价格（用于显示未实现盈亏）
   */
  public async updatePositionPrices(priceProvider: (tokenAddress: string) => Promise<number>): Promise<void> {
    for (const position of this.positions.values()) {
      try {
        const currentPrice = await priceProvider(position.tokenAddress);
        const unrealizedPnL = (currentPrice - position.entryPrice) * position.amount;
        
        // 这里可以存储当前价格和未实现盈亏，但不修改原始仓位数据
        this.log('debug', `Position ${position.id}: current price ${currentPrice}, unrealized PnL: ${unrealizedPnL.toFixed(4)} SOL`);
      } catch (error) {
        this.log('warn', `Failed to update price for position ${position.id}: ${error}`);
      }
    }
  }

  /**
   * 清除所有仓位历史
   */
  public clearHistory(): void {
    this.positionHistory = [];
    this.log('info', 'Position history cleared');
  }

  /**
   * 生成仓位ID
   */
  private generatePositionId(): string {
    return `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 日志记录
   */
  private log(level: 'debug' | 'info' | 'warn' | 'error', message: string): void {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${level.toUpperCase()}] [PositionManager] ${message}`);
  }
} 