import "dotenv/config";
import Client from "@triton-one/yellowstone-grpc";
import { 
  PublicKey, 
  Connection,
  Keypair,
  LAMPORTS_PER_SOL
} from "@solana/web3.js";
import bs58 from "bs58";

// 导入新的模块化组件
import { TradingConfig, TradingStats, SubscribeRequest, TokenTradingInstance, TradingDataProvider } from '../types/trading-interfaces';
import { EnhancedRiskManager } from './enhanced-risk-manager';
import { EnhancedPositionManager } from './enhanced-position-manager';
import { EnhancedConfigValidator } from '../config/enhanced-config-validator';
import { TradingUtils } from '../utils/trading-utils';

// 导入现有的组件
import { DynamicSubscriptionManager } from '../dynamic-subscription-manager';
import { TradingBot } from '../trading-bot';
import { TradingSystemConfig } from '../trading-config';
import { TransactionData, BuyPredictor } from '../predictors/buy-predictor';
import { SellPredictor } from "../predictors/sell-predictor";
import { TelegramNotifier } from "../utils/telegram-notifier";
import PriceService from "../utils/price-service";

// 常量配置
const TARGET_WALLET = process.env.TARGET_WALLET || '8J5GUAf7hr3LTPHJSkwrKFDNJPtAXtLHhnNHq6XxTLrW';
const USE_JITO = process.env.USE_JITO === 'true';
const TRADE_AMOUNT_SOL = 0.0001;
const MAX_MONITORED_TOKENS = parseInt(process.env.MAX_MONITORED_TOKENS || '10');

export class RealTradingCore implements TradingDataProvider {
  // 核心组件
  private subscriptionManager: DynamicSubscriptionManager;
  private tradingBot: TradingBot;
  private riskManager: EnhancedRiskManager;
  private positionManager: EnhancedPositionManager;
  private telegramNotifier: TelegramNotifier;
  private priceService: PriceService;
  
  // 配置
  private config: TradingSystemConfig;
  private tradingConfig: TradingConfig;
  
  // 网络连接
  private grpcClient: Client | null = null;
  private connection: Connection;
  private wallet: Keypair;
  
  // 状态管理
  public isRunning: boolean = false;
  private tokenInstances: Map<string, TokenTradingInstance> = new Map();
  private currentSubscription: Set<string> = new Set([TARGET_WALLET]);
  
  // 定时器
  private predictionTimer: NodeJS.Timeout | null = null;
  private statusMonitorTimer: NodeJS.Timeout | null = null;
  private blockhashUpdateTimer: NodeJS.Timeout | null = null;
  
  // 缓存和状态
  private currentBlockhash: string = '';
  private lastBlockhashUpdate: Date = new Date(0);
  private tokenSymbolCache: Map<string, string> = new Map();
  
  // 统计数据
  private stats = {
    totalTransactions: 0,
    totalTokensSeen: 0,
    totalMonitored: 0,
    uptime: new Date(),
    lastProcessedTransaction: new Date(),
    networkErrors: 0,
    reconnections: 0
  };

  constructor() {
    console.log('🚀 初始化 Real Trading Core...');
    
    // 初始化钱包
    this.initializeWallet();
    
    // 初始化连接
    this.connection = new Connection(
      process.env.SOLANA_RPC_ENDPOINT || "https://solana-rpc.publicnode.com"
    );
    
    // 创建默认配置
    this.tradingConfig = EnhancedConfigValidator.createDefaultConfig();
    this.tradingConfig.tradeAmountSol = TRADE_AMOUNT_SOL;
    this.tradingConfig.paperTrading = false; // 真实交易模式
    
    // 验证配置
    const validation = EnhancedConfigValidator.validate(this.tradingConfig);
    if (!validation.valid) {
      console.error('❌ 配置验证失败:', validation.errors);
      throw new Error('配置验证失败');
    }
    
    if (validation.warnings.length > 0) {
      console.warn('⚠️  配置警告:', validation.warnings);
    }
    
    // 初始化管理器
    this.riskManager = new EnhancedRiskManager(this.tradingConfig);
    this.positionManager = new EnhancedPositionManager(this.tradingConfig);
    
    // 初始化通知服务
    this.telegramNotifier = new TelegramNotifier();
    this.priceService = PriceService.getInstance();
    
    // 显示配置摘要
    EnhancedConfigValidator.showConfigSummary(this.tradingConfig);
    
    console.log('✅ Real Trading Core 初始化完成');
  }

  private initializeWallet(): void {
    const privateKey = process.env.PRIVATE_KEY;
    if (!privateKey) {
      throw new Error("PRIVATE_KEY not found in environment variables");
    }

    try {
      console.log(`🔑 加载钱包...`);
      const secretKeyBytes = bs58.decode(privateKey);
      
      if (secretKeyBytes.length !== 64) {
        throw new Error(`私钥字节长度错误: 期望64字节，实际${secretKeyBytes.length}字节`);
      }
      
      this.wallet = Keypair.fromSecretKey(secretKeyBytes);
      console.log(`✅ 钱包加载成功: ${this.wallet.publicKey.toBase58()}`);
      
    } catch (error) {
      console.error('❌ 钱包加载失败:', error);
      throw error;
    }
  }

  public async start(): Promise<void> {
    try {
      console.log('🎯 启动 Real Trading Core...');
      
      // 验证环境
      const envValidation = EnhancedConfigValidator.validateEnvironment();
      if (!envValidation.valid) {
        console.error('❌ 环境验证失败:', envValidation.errors);
        throw new Error('环境配置不完整');
      }
      
      if (envValidation.warnings.length > 0) {
        console.warn('⚠️  环境警告:', envValidation.warnings);
      }
      
      // 连接GRPC
      await this.connectGrpc();
      
      // 启动监控
      this.startMonitoring();
      
      // 更新状态
      this.isRunning = true;
      this.stats.uptime = new Date();
      
      console.log('✅ Real Trading Core 启动成功');
      
    } catch (error) {
      console.error('❌ 启动失败:', error);
      await this.stop();
      throw error;
    }
  }

  private async connectGrpc(): Promise<void> {
    const endpoint = process.env.ENDPOINT || 'https://solana-yellowstone-grpc.publicnode.com:443';
    console.log(`🔌 连接GRPC: ${endpoint}`);
    
    this.grpcClient = new Client(endpoint, process.env.X_TOKEN, undefined);
    console.log('✅ GRPC客户端创建成功');
  }

  private startMonitoring(): void {
    console.log('📊 启动监控服务...');
    
    // 启动状态监控
    this.statusMonitorTimer = setInterval(() => {
      this.printStatus();
    }, 60000); // 每分钟打印状态
    
    // 启动Blockhash更新
    this.blockhashUpdateTimer = setInterval(async () => {
      await this.updateBlockhash();
    }, USE_JITO ? 30000 : 2000);
    
    // 启动AI预测
    this.predictionTimer = setInterval(async () => {
      await this.runPredictions();
    }, 30000); // 每30秒运行一次预测
    
    console.log('✅ 监控服务启动完成');
  }

  private async updateBlockhash(): Promise<void> {
    try {
      const { blockhash } = await this.connection.getLatestBlockhash('finalized');
      this.currentBlockhash = blockhash;
      this.lastBlockhashUpdate = new Date();
    } catch (error) {
      console.error('⚠️  Blockhash更新失败:', TradingUtils.formatError(error));
    }
  }

  private async runPredictions(): Promise<void> {
    const activeTokens = Array.from(this.tokenInstances.keys());
    
    for (const tokenAddress of activeTokens) {
      try {
        await this.makePredictionForToken(tokenAddress);
      } catch (error) {
        console.error(`⚠️  Token预测失败 ${TradingUtils.formatTokenDisplay(tokenAddress)}:`, 
          TradingUtils.formatError(error));
      }
    }
  }

  private async makePredictionForToken(tokenAddress: string): Promise<void> {
    const instance = this.tokenInstances.get(tokenAddress);
    if (!instance || !instance.isActive) return;

    // 检查风险管理
    const riskCheck = this.riskManager.canTrade();
    if (!riskCheck.allowed) {
      console.log(`🚫 风险检查失败: ${riskCheck.reason}`);
      return;
    }

    // 这里会调用AI模型进行预测
    // 简化版本，实际实现需要集成BuyPredictor和SellPredictor
    const buyPrediction = Math.random(); // 临时占位符
    const sellPrediction = Math.random(); // 临时占位符
    
    await this.executeTradeDecision(tokenAddress, buyPrediction, sellPrediction, instance);
  }

  private async executeTradeDecision(
    tokenAddress: string, 
    buyPrediction: number, 
    sellPrediction: number, 
    instance: TokenTradingInstance
  ): Promise<void> {
    const hasPosition = instance.currentHolding !== null;
    
    if (!hasPosition && buyPrediction > this.tradingConfig.buyThreshold) {
      // 执行买入逻辑
      console.log(`💰 触发买入信号: ${TradingUtils.formatTokenDisplay(tokenAddress)} (${(buyPrediction * 100).toFixed(1)}%)`);
      // await this.executeRealBuy(tokenAddress, instance);
    } else if (hasPosition && sellPrediction > this.tradingConfig.sellThreshold) {
      // 执行卖出逻辑
      console.log(`💸 触发卖出信号: ${TradingUtils.formatTokenDisplay(tokenAddress)} (${(sellPrediction * 100).toFixed(1)}%)`);
      // await this.executeRealSell(tokenAddress, instance.currentHolding!.amount);
    }
  }

  private printStatus(): void {
    const riskStats = this.riskManager.getStats();
    const positionStats = this.positionManager.getPositionStats();
    
    console.log('\n' + '='.repeat(80));
    console.log(`📊 Real Trading Core 状态报告 - ${new Date().toLocaleString('zh-CN')}`);
    console.log('='.repeat(80));
    
    // 系统状态
    console.log('🖥️  系统状态:');
    console.log(`   运行状态: ${this.isRunning ? '✅ 运行中' : '❌ 停止'}`);
    console.log(`   运行时间: ${TradingUtils.formatHoldingTime(this.stats.uptime)}`);
    console.log(`   监控Token: ${this.tokenInstances.size}个`);
    console.log(`   网络错误: ${this.stats.networkErrors}次`);
    
    // 风险管理状态
    console.log('\n⚠️  风险管理:');
    console.log(`   交易状态: ${riskStats.tradingPaused ? '🔴 暂停' : '🟢 正常'}`);
    console.log(`   当前余额: ${TradingUtils.formatSolAmount(riskStats.currentBalance)}`);
    console.log(`   日内PnL: ${TradingUtils.formatSolAmount(riskStats.dailyPnL)}`);
    console.log(`   总PnL: ${TradingUtils.formatSolAmount(riskStats.totalPnL)}`);
    console.log(`   胜率: ${TradingUtils.formatPercentage(riskStats.winRate)}`);
    console.log(`   连续亏损: ${riskStats.consecutiveLosses}次`);
    console.log(`   风险等级: ${this.getRiskLevelDisplay(this.riskManager.getRiskLevel())}`);
    
    // 仓位状态
    console.log('\n📈 仓位管理:');
    console.log(`   活跃仓位: ${positionStats.activePositions}/${this.tradingConfig.maxPositions}`);
    console.log(`   总交易数: ${positionStats.totalPositions}`);
    console.log(`   平均PnL: ${TradingUtils.formatSolAmount(positionStats.averagePnL)}`);
    console.log(`   平均持仓时间: ${positionStats.averageHoldingTime.toFixed(1)}分钟`);
    
    console.log('='.repeat(80) + '\n');
  }

  private getRiskLevelDisplay(level: 'low' | 'medium' | 'high' | 'critical'): string {
    switch (level) {
      case 'low': return '🟢 低风险';
      case 'medium': return '🟡 中等风险';
      case 'high': return '🟠 高风险';
      case 'critical': return '🔴 临界风险';
    }
  }

  public async stop(): Promise<void> {
    console.log('🛑 停止 Real Trading Core...');
    
    this.isRunning = false;
    
    // 清理定时器
    if (this.statusMonitorTimer) {
      clearInterval(this.statusMonitorTimer);
      this.statusMonitorTimer = null;
    }
    
    if (this.blockhashUpdateTimer) {
      clearInterval(this.blockhashUpdateTimer);
      this.blockhashUpdateTimer = null;
    }
    
    if (this.predictionTimer) {
      clearInterval(this.predictionTimer);
      this.predictionTimer = null;
    }
    
    // 强制平仓
    await this.liquidateAllPositions();
    
    // 关闭GRPC连接
    if (this.grpcClient) {
      this.grpcClient = null;
    }
    
    // 打印最终统计
    this.printFinalStats();
    
    console.log('✅ Real Trading Core 已停止');
  }

  private async liquidateAllPositions(): Promise<void> {
    const positions = this.positionManager.liquidateAllPositions();
    
    for (const position of positions) {
      console.log(`🚨 强制平仓: ${TradingUtils.formatTokenDisplay(position.tokenAddress)}`);
      // 这里需要实现实际的卖出逻辑
    }
  }

  private printFinalStats(): void {
    const finalStats = this.riskManager.getStats();
    const positionStats = this.positionManager.getPositionStats();
    
    console.log('\n' + '🏁 '.repeat(20));
    console.log('📋 最终交易统计:');
    console.log('='.repeat(80));
    console.log(`总交易数: ${finalStats.totalTrades}`);
    console.log(`胜率: ${TradingUtils.formatPercentage(finalStats.winRate)}`);
    console.log(`总PnL: ${TradingUtils.formatSolAmount(finalStats.totalPnL)}`);
    console.log(`总手续费: ${TradingUtils.formatSolAmount(finalStats.totalFees)}`);
    console.log(`最大回撤: ${TradingUtils.formatPercentage(finalStats.maxDrawdown)}`);
    console.log(`运行时长: ${TradingUtils.formatHoldingTime(this.stats.uptime)}`);
    console.log('='.repeat(80));
  }

  // 实现TradingDataProvider接口
  public getActiveTokens(): string[] {
    return Array.from(this.tokenInstances.keys()).filter(address => {
      const instance = this.tokenInstances.get(address);
      return instance?.isActive ?? false;
    });
  }

  public getTokenStats(): Array<{
    tokenAddress: string;
    totalTrades: number;
    totalPnL: number;
    transactionCount: number;
    winRate: number;
  }> {
    return Array.from(this.tokenInstances.entries()).map(([address, instance]) => ({
      tokenAddress: address,
      totalTrades: instance.stats.totalTrades,
      totalPnL: instance.stats.totalPnL,
      transactionCount: instance.activity.transactionCount,
      winRate: instance.stats.totalTrades > 0 ? 
        (instance.stats.successfulTrades / instance.stats.totalTrades) * 100 : 0
    }));
  }

  public getCurrentPositions(): Array<{
    tokenAddress: string;
    position: number;
    pnl: number;
    buyPrice: number;
    currentPrice: number;
    holdingTime: number;
  }> {
    const positions = this.positionManager.getActivePositions();
    return positions.map(position => ({
      tokenAddress: position.tokenAddress,
      position: position.amount,
      pnl: position.pnl || 0,
      buyPrice: position.entryPrice,
      currentPrice: position.entryPrice, // 需要实现实时价格获取
      holdingTime: Date.now() - position.timestamp.getTime()
    }));
  }

  public getSellOnlyBufferInfo(): Array<{
    tokenAddress: string;
    reason: string;
    addedTime: Date;
    bufferDurationMinutes: number;
    hasPosition: boolean;
  }> {
    // 简化版本，返回空数组
    return [];
  }

  public isSystemRunning(): boolean {
    return this.isRunning;
  }

  // 获取风险管理器（用于外部访问）
  public getRiskManager(): EnhancedRiskManager {
    return this.riskManager;
  }

  // 获取仓位管理器（用于外部访问）
  public getPositionManager(): EnhancedPositionManager {
    return this.positionManager;
  }
} 