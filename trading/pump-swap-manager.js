"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PumpSwapManager = void 0;
var web3_js_1 = require("@solana/web3.js");
var pump_swap_sdk_1 = require("@pump-fun/pump-swap-sdk");
var PumpSwapManager = /** @class */ (function () {
    function PumpSwapManager(config) {
        if (config === void 0) { config = {}; }
        this.config = __assign({ paperTrading: false, rpcEndpoint: process.env.SOLANA_RPC_ENDPOINT || 'https://api.mainnet-beta.solana.com', privateKey: process.env.PRIVATE_KEY }, config);
        this.paperTrading = this.config.paperTrading || false;
        // 在paper trading模式下，直接生成虚拟钱包
        if (this.paperTrading) {
            this.wallet = web3_js_1.Keypair.generate();
            console.log('[PumpSwapManager] Paper trading mode: using virtual wallet');
        }
        else {
            // 只有在真实交易模式下才需要私钥
            if (!this.config.privateKey) {
                throw new Error('Private key is required for live trading');
            }
            try {
                this.wallet = web3_js_1.Keypair.fromSecretKey(Buffer.from(this.config.privateKey, 'base64'));
            }
            catch (error) {
                throw new Error("Invalid private key format: ".concat(error));
            }
        }
        // 初始化连接
        this.connection = new web3_js_1.Connection(this.config.rpcEndpoint);
        // 初始化Pump SDK - 需要传入connection参数
        this.pumpSdk = new pump_swap_sdk_1.PumpAmmSdk(this.connection);
    }
    /**
     * 初始化SDK
     */
    PumpSwapManager.prototype.initialize = function () {
        return __awaiter(this, void 0, void 0, function () {
            var balance, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 4, , 5]);
                        this.log('info', 'Initializing PumpSwapManager...');
                        if (!this.paperTrading) return [3 /*break*/, 1];
                        this.log('info', 'Paper trading mode enabled');
                        return [3 /*break*/, 3];
                    case 1: return [4 /*yield*/, this.connection.getBalance(this.wallet.publicKey)];
                    case 2:
                        balance = _a.sent();
                        this.log('info', "Wallet balance: ".concat(balance / 1e9, " SOL"));
                        if (balance < 0.001 * 1e9) { // 至少需要0.001 SOL作为gas费
                            throw new Error('Insufficient SOL balance for trading');
                        }
                        _a.label = 3;
                    case 3:
                        this.log('info', 'PumpSwapManager initialized successfully');
                        return [3 /*break*/, 5];
                    case 4:
                        error_1 = _a.sent();
                        this.log('error', "Failed to initialize PumpSwapManager: ".concat(error_1));
                        throw error_1;
                    case 5: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 执行买入操作
     */
    PumpSwapManager.prototype.buy = function (tokenAddress_1, solAmount_1) {
        return __awaiter(this, arguments, void 0, function (tokenAddress, solAmount, slippageTolerance, lastTradePrice) {
            if (slippageTolerance === void 0) { slippageTolerance = 0.05; }
            return __generator(this, function (_a) {
                this.log('info', "Attempting to buy ".concat(solAmount, " SOL worth of ").concat(tokenAddress));
                // 暂时使用模拟交易，避免复杂的SDK集成问题
                return [2 /*return*/, this.simulateBuy(tokenAddress, solAmount, slippageTolerance, lastTradePrice)];
            });
        });
    };
    /**
     * 执行卖出操作
     */
    PumpSwapManager.prototype.sell = function (tokenAddress_1, tokenAmount_1) {
        return __awaiter(this, arguments, void 0, function (tokenAddress, tokenAmount, slippageTolerance, lastTradePrice) {
            if (slippageTolerance === void 0) { slippageTolerance = 0.05; }
            return __generator(this, function (_a) {
                this.log('info', "Attempting to sell ".concat(tokenAmount, " tokens of ").concat(tokenAddress));
                // 暂时使用模拟交易，避免复杂的SDK集成问题
                return [2 /*return*/, this.simulateSell(tokenAddress, tokenAmount, slippageTolerance, lastTradePrice)];
            });
        });
    };
    /**
     * 获取Token当前价格
     */
    PumpSwapManager.prototype.getCurrentPrice = function (tokenAddress) {
        return __awaiter(this, void 0, void 0, function () {
            var tokenMint, pool, testAmount, expectedSol, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 3, , 4]);
                        // 如果是纸上交易，返回模拟价格
                        if (this.paperTrading) {
                            return [2 /*return*/, 0.001 + Math.random() * 0.002]; // 模拟价格：0.001-0.003
                        }
                        tokenMint = new web3_js_1.PublicKey(tokenAddress);
                        return [4 /*yield*/, this.getOrCreatePool(tokenMint)];
                    case 1:
                        pool = _a.sent();
                        if (!pool) {
                            throw new Error('Failed to get pool for token');
                        }
                        testAmount = 1000;
                        return [4 /*yield*/, this.pumpSdk.swapAutocompleteQuoteFromBase(pool, testAmount, 0.01, // 1% slippage
                            "baseToQuote")];
                    case 2:
                        expectedSol = _a.sent();
                        return [2 /*return*/, Number(expectedSol) / testAmount]; // 每个token的SOL价格
                    case 3:
                        error_2 = _a.sent();
                        this.log('error', "Failed to get current price for ".concat(tokenAddress, ": ").concat(error_2));
                        return [2 /*return*/, undefined]; // 🔥 删除默认价格，无法获取真实价格时返回undefined
                    case 4: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 获取或创建池子
     */
    PumpSwapManager.prototype.getOrCreatePool = function (tokenMint) {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                try {
                    // 这里需要根据pump.fun的实际API来实现
                    // 当前只是一个占位符实现
                    return [2 /*return*/, {
                            baseMint: tokenMint,
                            quoteMint: new web3_js_1.PublicKey('So11111111111111111111111111111111111111112'), // WSOL
                            // 其他池子相关信息
                        }];
                }
                catch (error) {
                    this.log('error', "Failed to get or create pool: ".concat(error));
                    return [2 /*return*/, null];
                }
                return [2 /*return*/];
            });
        });
    };
    /**
     * 获取交易费用
     */
    PumpSwapManager.prototype.getTransactionFee = function (txid) {
        return __awaiter(this, void 0, void 0, function () {
            var transaction, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        return [4 /*yield*/, this.connection.getTransaction(txid, {
                                commitment: 'confirmed'
                            })];
                    case 1:
                        transaction = _a.sent();
                        if (transaction && transaction.meta) {
                            return [2 /*return*/, (transaction.meta.fee || 0) / 1e9]; // 转换为SOL
                        }
                        return [2 /*return*/, 0.000005]; // 默认费用估算
                    case 2:
                        error_3 = _a.sent();
                        this.log('error', "Failed to get transaction fee: ".concat(error_3));
                        return [2 /*return*/, 0.000005]; // 默认费用估算
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 模拟买入（纸上交易）- 使用真实市场价格
     */
    PumpSwapManager.prototype.simulateBuy = function (tokenAddress, solAmount, slippageTolerance, lastTradePrice) {
        return __awaiter(this, void 0, void 0, function () {
            var delay, successRate, buySlippage, actualBuyPrice, simulatedSlippage, simulatedFee, txId;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log("\n\uD83D\uDCB0 [\u6A21\u62DF\u4E70\u5165] \u5F00\u59CB\u6267\u884C:");
                        console.log("   \uD83E\uDE99 Token: ".concat(tokenAddress.slice(0, 8), "..."));
                        console.log("   \uD83D\uDCB5 \u91D1\u989D: ".concat(solAmount, " SOL"));
                        console.log("   \uD83D\uDCCA \u6ED1\u70B9\u5BB9\u5FCD: ".concat((slippageTolerance * 100).toFixed(1), "%"));
                        // 🔥 必须有真实的市场价格才能进行模拟交易
                        if (!lastTradePrice) {
                            console.log("   \u274C \u65E0\u6CD5\u83B7\u53D6\u771F\u5B9E\u5E02\u573A\u4EF7\u683C\uFF0C\u62D2\u7EDD\u6A21\u62DF\u4E70\u5165");
                            return [2 /*return*/, {
                                    success: false,
                                    error: 'No real market price available for simulation'
                                }];
                        }
                        console.log("   \uD83D\uDCCA \u4F7F\u7528\u771F\u5B9E\u5E02\u573A\u4EF7\u683C: ".concat(lastTradePrice.toFixed(8), " SOL/token"));
                        delay = 100 + Math.random() * 400;
                        console.log("   \u23F1\uFE0F  \u6A21\u62DF\u7F51\u7EDC\u5EF6\u8FDF: ".concat(delay.toFixed(0), "ms"));
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, delay); })];
                    case 1:
                        _a.sent();
                        successRate = Math.random();
                        console.log("   \uD83C\uDFB2 \u6210\u529F\u7387\u68C0\u67E5: ".concat((successRate * 100).toFixed(1), "%"));
                        if (successRate < 0.05) {
                            console.log("   \u274C \u6A21\u62DF\u4EA4\u6613\u5931\u8D25 (5%\u5931\u8D25\u7387)");
                            return [2 /*return*/, {
                                    success: false,
                                    error: 'Simulated transaction failure'
                                }];
                        }
                        buySlippage = 0.001 + Math.random() * 0.004;
                        actualBuyPrice = lastTradePrice * (1 + buySlippage);
                        console.log("   \uD83D\uDCCA \u771F\u5B9E\u4EF7\u683C\u8BA1\u7B97:");
                        console.log("      \u5E02\u573A\u4EF7\u683C: ".concat(lastTradePrice.toFixed(8), " SOL/token"));
                        console.log("      \u4E70\u5165\u6ED1\u70B9: +".concat((buySlippage * 100).toFixed(3), "%"));
                        console.log("      \u5B9E\u9645\u4E70\u5165\u4EF7: ".concat(actualBuyPrice.toFixed(8), " SOL/token"));
                        simulatedSlippage = Math.random() * Math.min(slippageTolerance, 0.005);
                        simulatedFee = 0.000005;
                        txId = "BUY_".concat(tokenAddress.slice(0, 8), "_").concat(Date.now());
                        console.log("   \u2705 \u6A21\u62DF\u4E70\u5165\u6210\u529F!");
                        console.log("   \uD83D\uDCCB \u4EA4\u6613ID: ".concat(txId));
                        console.log("   \uD83D\uDCB2 \u6267\u884C\u4EF7\u683C: ".concat(actualBuyPrice.toFixed(8), " SOL/token"));
                        console.log("   \uD83D\uDCC8 \u5B9E\u9645\u6ED1\u70B9: ".concat((simulatedSlippage * 100).toFixed(3), "%"));
                        console.log("   \uD83D\uDCB8 Gas\u8D39\u7528: ".concat(simulatedFee.toFixed(8), " SOL"));
                        return [2 /*return*/, {
                                success: true,
                                transactionId: txId,
                                actualPrice: actualBuyPrice,
                                slippage: simulatedSlippage,
                                gasFee: simulatedFee,
                                marketPrice: lastTradePrice // 返回原始市场价格
                            }];
                }
            });
        });
    };
    /**
     * 模拟卖出（纸上交易）- 使用真实市场价格
     */
    PumpSwapManager.prototype.simulateSell = function (tokenAddress, tokenAmount, slippageTolerance, lastTradePrice) {
        return __awaiter(this, void 0, void 0, function () {
            var delay, successRate, sellSlippage, actualSellPrice, simulatedSlippage, simulatedFee, txId;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        console.log("\n\uD83D\uDCB8 [\u6A21\u62DF\u5356\u51FA] \u5F00\u59CB\u6267\u884C:");
                        console.log("   \uD83E\uDE99 Token: ".concat(tokenAddress.slice(0, 8), "..."));
                        console.log("   \uD83D\uDD22 \u6570\u91CF: ".concat(tokenAmount, " tokens"));
                        console.log("   \uD83D\uDCCA \u6ED1\u70B9\u5BB9\u5FCD: ".concat((slippageTolerance * 100).toFixed(1), "%"));
                        // 🔥 必须有真实的市场价格才能进行模拟交易
                        if (!lastTradePrice) {
                            console.log("   \u274C \u65E0\u6CD5\u83B7\u53D6\u771F\u5B9E\u5E02\u573A\u4EF7\u683C\uFF0C\u62D2\u7EDD\u6A21\u62DF\u5356\u51FA");
                            return [2 /*return*/, {
                                    success: false,
                                    error: 'No real market price available for simulation'
                                }];
                        }
                        console.log("   \uD83D\uDCCA \u4F7F\u7528\u771F\u5B9E\u5E02\u573A\u4EF7\u683C: ".concat(lastTradePrice.toFixed(8), " SOL/token"));
                        delay = 100 + Math.random() * 400;
                        console.log("   \u23F1\uFE0F  \u6A21\u62DF\u7F51\u7EDC\u5EF6\u8FDF: ".concat(delay.toFixed(0), "ms"));
                        return [4 /*yield*/, new Promise(function (resolve) { return setTimeout(resolve, delay); })];
                    case 1:
                        _a.sent();
                        successRate = Math.random();
                        console.log("   \uD83C\uDFB2 \u6210\u529F\u7387\u68C0\u67E5: ".concat((successRate * 100).toFixed(1), "%"));
                        if (successRate < 0.05) {
                            console.log("   \u274C \u6A21\u62DF\u4EA4\u6613\u5931\u8D25 (5%\u5931\u8D25\u7387)");
                            return [2 /*return*/, {
                                    success: false,
                                    error: 'Simulated transaction failure'
                                }];
                        }
                        sellSlippage = 0.001 + Math.random() * 0.004;
                        actualSellPrice = lastTradePrice * (1 - sellSlippage);
                        console.log("   \uD83D\uDCCA \u771F\u5B9E\u4EF7\u683C\u8BA1\u7B97:");
                        console.log("      \u5E02\u573A\u4EF7\u683C: ".concat(lastTradePrice.toFixed(8), " SOL/token"));
                        console.log("      \u5356\u51FA\u6ED1\u70B9: -".concat((sellSlippage * 100).toFixed(3), "%"));
                        console.log("      \u5B9E\u9645\u5356\u51FA\u4EF7: ".concat(actualSellPrice.toFixed(8), " SOL/token"));
                        simulatedSlippage = Math.random() * Math.min(slippageTolerance, 0.005);
                        simulatedFee = 0.000005;
                        txId = "SELL_".concat(tokenAddress.slice(0, 8), "_").concat(Date.now());
                        console.log("   \u2705 \u6A21\u62DF\u5356\u51FA\u6210\u529F!");
                        console.log("   \uD83D\uDCCB \u4EA4\u6613ID: ".concat(txId));
                        console.log("   \uD83D\uDCB2 \u6267\u884C\u4EF7\u683C: ".concat(actualSellPrice.toFixed(8), " SOL/token"));
                        console.log("   \uD83D\uDCC8 \u5B9E\u9645\u6ED1\u70B9: ".concat((simulatedSlippage * 100).toFixed(3), "%"));
                        console.log("   \uD83D\uDCB8 Gas\u8D39\u7528: ".concat(simulatedFee.toFixed(8), " SOL"));
                        return [2 /*return*/, {
                                success: true,
                                transactionId: txId,
                                actualPrice: actualSellPrice,
                                slippage: simulatedSlippage,
                                gasFee: simulatedFee,
                                marketPrice: lastTradePrice // 返回原始市场价格
                            }];
                }
            });
        });
    };
    /**
     * 日志记录
     */
    PumpSwapManager.prototype.log = function (level, message) {
        var timestamp = new Date().toISOString();
        console.log("[".concat(timestamp, "] [").concat(level.toUpperCase(), "] [PumpSwapManager] ").concat(message));
    };
    return PumpSwapManager;
}());
exports.PumpSwapManager = PumpSwapManager;
