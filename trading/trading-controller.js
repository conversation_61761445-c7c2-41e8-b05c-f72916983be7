"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingController = void 0;
var buy_predictor_1 = require("../predictors/buy-predictor");
var sell_predictor_1 = require("../predictors/sell-predictor");
var pump_swap_manager_1 = require("./pump-swap-manager");
var position_manager_1 = require("./position-manager");
var risk_manager_1 = require("./risk-manager");
var TradingController = /** @class */ (function () {
    function TradingController(config) {
        this.isRunning = false;
        this.tokenSubscriptions = new Map();
        this.transactionHistory = new Map();
        // 交易统计
        this.stats = {
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            winRate: 0,
            totalPnL: 0,
            totalFees: 0,
            averagePnL: 0,
            maxDrawdown: 0,
            currentBalance: 0,
            activePositions: 0
        };
        this.config = __assign({ buyModelPath: '../catboost_focal_blocktol_2025.cbm', sellModelPath: '../best_catboost_sell_model.cbm', buyThreshold: 0.5, sellThreshold: 0.7, tradeAmountSol: 0.1, maxPositions: 5, initialCapitalSol: 3.0, maxLossPerTradeSol: 0.1, stopLossPercentage: 0.1, takeProfitPercentage: 0.2, maxDailyLoss: 0.5, enablePaperTrading: false, logLevel: 'info' }, config);
        this.initializeComponents();
    }
    TradingController.prototype.initializeComponents = function () {
        // 初始化预测器
        this.buyPredictor = new buy_predictor_1.BuyPredictor(this.config.buyModelPath, 10, // window size for buy
        this.config.buyThreshold);
        this.sellPredictor = new sell_predictor_1.SellPredictor(this.config.sellModelPath, 30, // window size for sell
        this.config.sellThreshold);
        // 初始化交易管理器
        this.swapManager = new pump_swap_manager_1.PumpSwapManager({
            paperTrading: this.config.enablePaperTrading
        });
        this.positionManager = new position_manager_1.PositionManager({
            maxPositions: this.config.maxPositions,
            tradeAmount: this.config.tradeAmountSol
        });
        this.riskManager = new risk_manager_1.RiskManager({
            initialCapital: this.config.initialCapitalSol,
            maxLossPerTrade: this.config.maxLossPerTradeSol,
            stopLossPercentage: this.config.stopLossPercentage,
            takeProfitPercentage: this.config.takeProfitPercentage,
            maxDailyLoss: this.config.maxDailyLoss
        });
        // 初始化统计
        this.stats.currentBalance = this.config.initialCapitalSol || 3.0;
    };
    /**
     * 开始交易控制器
     */
    TradingController.prototype.start = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (this.isRunning) {
                            this.log('warn', 'Trading controller is already running');
                            return [2 /*return*/];
                        }
                        this.isRunning = true;
                        this.log('info', 'Trading controller started');
                        // 启动各个组件
                        return [4 /*yield*/, this.swapManager.initialize()];
                    case 1:
                        // 启动各个组件
                        _a.sent();
                        this.positionManager.start();
                        this.riskManager.start();
                        // 开始定期检查（每秒检查一次）
                        this.startPeriodicChecks();
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 停止交易控制器
     */
    TradingController.prototype.stop = function () {
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_a) {
                if (!this.isRunning) {
                    return [2 /*return*/];
                }
                this.isRunning = false;
                this.log('info', 'Trading controller stopped');
                // 停止各个组件
                this.positionManager.stop();
                this.riskManager.stop();
                return [2 /*return*/];
            });
        });
    };
    /**
     * 订阅Token进行交易
     */
    TradingController.prototype.subscribeToken = function (tokenAddress) {
        if (this.tokenSubscriptions.has(tokenAddress)) {
            this.log('warn', "Token ".concat(tokenAddress, " is already subscribed"));
            return;
        }
        this.tokenSubscriptions.set(tokenAddress, true);
        this.transactionHistory.set(tokenAddress, []);
        this.log('info', "Subscribed to token: ".concat(tokenAddress));
    };
    /**
     * 取消订阅Token
     */
    TradingController.prototype.unsubscribeToken = function (tokenAddress) {
        if (!this.tokenSubscriptions.has(tokenAddress)) {
            this.log('warn', "Token ".concat(tokenAddress, " is not subscribed"));
            return;
        }
        this.tokenSubscriptions.delete(tokenAddress);
        this.transactionHistory.delete(tokenAddress);
        // 关闭该Token的所有持仓
        this.positionManager.closeAllPositions(tokenAddress);
        this.log('info', "Unsubscribed from token: ".concat(tokenAddress));
    };
    /**
     * 处理新的交易数据
     */
    TradingController.prototype.onNewTransaction = function (tokenAddress, transaction) {
        return __awaiter(this, void 0, void 0, function () {
            var history;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        if (!this.isRunning || !this.tokenSubscriptions.has(tokenAddress)) {
                            return [2 /*return*/];
                        }
                        history = this.transactionHistory.get(tokenAddress) || [];
                        history.push(transaction);
                        // 保持历史记录在合理范围内
                        if (history.length > 1000) {
                            history.splice(0, history.length - 1000);
                        }
                        this.transactionHistory.set(tokenAddress, history);
                        // 更新预测器历史
                        this.buyPredictor.addTransaction(transaction);
                        this.sellPredictor.addTransaction(transaction);
                        // 进行预测和交易决策
                        return [4 /*yield*/, this.makeTradingDecision(tokenAddress)];
                    case 1:
                        // 进行预测和交易决策
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 执行交易决策
     */
    TradingController.prototype.makeTradingDecision = function (tokenAddress) {
        return __awaiter(this, void 0, void 0, function () {
            var currentPosition, buySignal, sellSignal, error_1;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 10, , 11]);
                        // 检查风控条件
                        if (!this.riskManager.canTrade()) {
                            this.log('warn', 'Trading blocked by risk manager');
                            return [2 /*return*/];
                        }
                        currentPosition = this.positionManager.getPosition(tokenAddress);
                        if (!!currentPosition) return [3 /*break*/, 4];
                        return [4 /*yield*/, this.checkBuySignal(tokenAddress)];
                    case 1:
                        buySignal = _a.sent();
                        if (!buySignal) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.executeBuy(tokenAddress, buySignal)];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3: return [3 /*break*/, 9];
                    case 4: return [4 /*yield*/, this.checkSellSignal(tokenAddress)];
                    case 5:
                        sellSignal = _a.sent();
                        if (!sellSignal) return [3 /*break*/, 7];
                        return [4 /*yield*/, this.executeSell(tokenAddress, sellSignal)];
                    case 6:
                        _a.sent();
                        _a.label = 7;
                    case 7: 
                    // 同时检查止损和止盈
                    return [4 /*yield*/, this.checkStopLossAndTakeProfit(tokenAddress, currentPosition)];
                    case 8:
                        // 同时检查止损和止盈
                        _a.sent();
                        _a.label = 9;
                    case 9: return [3 /*break*/, 11];
                    case 10:
                        error_1 = _a.sent();
                        this.log('error', "Error in trading decision for ".concat(tokenAddress, ": ").concat(error_1));
                        return [3 /*break*/, 11];
                    case 11: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 检查买入信号
     */
    TradingController.prototype.checkBuySignal = function (tokenAddress) {
        return __awaiter(this, void 0, void 0, function () {
            var prediction;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.buyPredictor.predictBuy()];
                    case 1:
                        prediction = _a.sent();
                        if (prediction.prediction && prediction.probability > this.config.buyThreshold) {
                            this.log('info', "Buy signal detected for ".concat(tokenAddress, ": ").concat(prediction.probability.toFixed(3)));
                            return [2 /*return*/, {
                                    type: 'buy',
                                    tokenAddress: tokenAddress,
                                    probability: prediction.probability,
                                    confidence: prediction.confidence,
                                    timestamp: prediction.timestamp,
                                    prediction: prediction
                                }];
                        }
                        return [2 /*return*/, null];
                }
            });
        });
    };
    /**
     * 检查卖出信号
     */
    TradingController.prototype.checkSellSignal = function (tokenAddress) {
        return __awaiter(this, void 0, void 0, function () {
            var prediction;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, this.sellPredictor.predictSell()];
                    case 1:
                        prediction = _a.sent();
                        if (prediction.prediction && prediction.probability > this.config.sellThreshold) {
                            this.log('info', "Sell signal detected for ".concat(tokenAddress, ": ").concat(prediction.probability.toFixed(3)));
                            return [2 /*return*/, {
                                    type: 'sell',
                                    tokenAddress: tokenAddress,
                                    probability: prediction.probability,
                                    confidence: prediction.confidence,
                                    timestamp: prediction.timestamp,
                                    prediction: prediction
                                }];
                        }
                        return [2 /*return*/, null];
                }
            });
        });
    };
    /**
     * 执行买入交易
     */
    TradingController.prototype.executeBuy = function (tokenAddress, signal) {
        return __awaiter(this, void 0, void 0, function () {
            var tradeResult, error_2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        // 检查是否可以开新仓
                        if (!this.positionManager.canOpenNewPosition()) {
                            this.log('warn', "Cannot open new position for ".concat(tokenAddress, ": max positions reached"));
                            return [2 /*return*/];
                        }
                        // 检查资金是否充足
                        if (!this.riskManager.canAffordTrade(this.config.tradeAmountSol)) {
                            this.log('warn', "Cannot afford trade for ".concat(tokenAddress, ": insufficient balance"));
                            return [2 /*return*/];
                        }
                        this.log('info', "Executing buy for ".concat(tokenAddress, ", amount: ").concat(this.config.tradeAmountSol, " SOL"));
                        return [4 /*yield*/, this.swapManager.buy(tokenAddress, this.config.tradeAmountSol, 0.05 // 5% slippage tolerance
                            )];
                    case 1:
                        tradeResult = _a.sent();
                        if (tradeResult.success) {
                            // 记录新仓位
                            this.positionManager.openPosition({
                                tokenAddress: tokenAddress,
                                side: 'long',
                                amount: this.config.tradeAmountSol,
                                entryPrice: tradeResult.actualPrice,
                                timestamp: new Date(),
                                transactionId: tradeResult.transactionId
                            });
                            // 更新统计
                            this.stats.totalTrades++;
                            this.stats.totalFees += tradeResult.gasFee || 0;
                            this.stats.activePositions = this.positionManager.getActivePositionCount();
                            this.log('info', "Buy executed successfully for ".concat(tokenAddress, ": ").concat(tradeResult.transactionId));
                        }
                        else {
                            this.log('error', "Buy failed for ".concat(tokenAddress, ": ").concat(tradeResult.error));
                        }
                        return [3 /*break*/, 3];
                    case 2:
                        error_2 = _a.sent();
                        this.log('error', "Error executing buy for ".concat(tokenAddress, ": ").concat(error_2));
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 执行卖出交易
     */
    TradingController.prototype.executeSell = function (tokenAddress, signal) {
        return __awaiter(this, void 0, void 0, function () {
            var position, tradeResult, pnl, isWinning, error_3;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 2, , 3]);
                        position = this.positionManager.getPosition(tokenAddress);
                        if (!position) {
                            this.log('warn', "No position to sell for ".concat(tokenAddress));
                            return [2 /*return*/];
                        }
                        this.log('info', "Executing sell for ".concat(tokenAddress, ", amount: ").concat(position.amount, " SOL"));
                        return [4 /*yield*/, this.swapManager.sell(tokenAddress, position.amount, 0.05 // 5% slippage tolerance
                            )];
                    case 1:
                        tradeResult = _a.sent();
                        if (tradeResult.success) {
                            pnl = (tradeResult.actualPrice - position.entryPrice) * position.amount;
                            isWinning = pnl > 0;
                            // 关闭仓位
                            this.positionManager.closePosition(tokenAddress, {
                                exitPrice: tradeResult.actualPrice,
                                timestamp: new Date(),
                                transactionId: tradeResult.transactionId,
                                pnl: pnl
                            });
                            // 更新统计
                            this.stats.totalTrades++;
                            this.stats.totalFees += tradeResult.gasFee || 0;
                            this.stats.totalPnL += pnl;
                            this.stats.currentBalance += pnl;
                            this.stats.activePositions = this.positionManager.getActivePositionCount();
                            if (isWinning) {
                                this.stats.winningTrades++;
                            }
                            else {
                                this.stats.losingTrades++;
                            }
                            this.stats.winRate = this.stats.winningTrades / this.stats.totalTrades;
                            this.stats.averagePnL = this.stats.totalPnL / this.stats.totalTrades;
                            this.log('info', "Sell executed successfully for ".concat(tokenAddress, ": ").concat(tradeResult.transactionId, ", PnL: ").concat(pnl.toFixed(4), " SOL"));
                        }
                        else {
                            this.log('error', "Sell failed for ".concat(tokenAddress, ": ").concat(tradeResult.error));
                        }
                        return [3 /*break*/, 3];
                    case 2:
                        error_3 = _a.sent();
                        this.log('error', "Error executing sell for ".concat(tokenAddress, ": ").concat(error_3));
                        return [3 /*break*/, 3];
                    case 3: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 检查止损和止盈
     */
    TradingController.prototype.checkStopLossAndTakeProfit = function (tokenAddress, position) {
        return __awaiter(this, void 0, void 0, function () {
            var currentPrice, pnlPercentage, error_4;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        _a.trys.push([0, 6, , 7]);
                        return [4 /*yield*/, this.swapManager.getCurrentPrice(tokenAddress)];
                    case 1:
                        currentPrice = _a.sent();
                        pnlPercentage = (currentPrice - position.entryPrice) / position.entryPrice;
                        if (!(pnlPercentage <= -this.config.stopLossPercentage)) return [3 /*break*/, 3];
                        this.log('warn', "Stop loss triggered for ".concat(tokenAddress, ": ").concat((pnlPercentage * 100).toFixed(2), "%"));
                        return [4 /*yield*/, this.executeSell(tokenAddress, {
                                type: 'sell',
                                tokenAddress: tokenAddress,
                                probability: 1.0,
                                confidence: 1.0,
                                timestamp: new Date(),
                                prediction: {
                                    probability: 1.0,
                                    prediction: true,
                                    confidence: 1.0,
                                    timestamp: new Date(),
                                    predictionTimeMs: 0,
                                    predictionTimeMicros: 0
                                }
                            })];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                    case 3:
                        if (!(pnlPercentage >= this.config.takeProfitPercentage)) return [3 /*break*/, 5];
                        this.log('info', "Take profit triggered for ".concat(tokenAddress, ": ").concat((pnlPercentage * 100).toFixed(2), "%"));
                        return [4 /*yield*/, this.executeSell(tokenAddress, {
                                type: 'sell',
                                tokenAddress: tokenAddress,
                                probability: 1.0,
                                confidence: 1.0,
                                timestamp: new Date(),
                                prediction: {
                                    probability: 1.0,
                                    prediction: true,
                                    confidence: 1.0,
                                    timestamp: new Date(),
                                    predictionTimeMs: 0,
                                    predictionTimeMicros: 0
                                }
                            })];
                    case 4:
                        _a.sent();
                        _a.label = 5;
                    case 5: return [3 /*break*/, 7];
                    case 6:
                        error_4 = _a.sent();
                        this.log('error', "Error checking stop loss/take profit for ".concat(tokenAddress, ": ").concat(error_4));
                        return [3 /*break*/, 7];
                    case 7: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 开始定期检查
     */
    TradingController.prototype.startPeriodicChecks = function () {
        var _this = this;
        setInterval(function () { return __awaiter(_this, void 0, void 0, function () {
            var _i, _a, tokenAddress, position;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        if (!this.isRunning)
                            return [2 /*return*/];
                        _i = 0, _a = this.tokenSubscriptions.keys();
                        _b.label = 1;
                    case 1:
                        if (!(_i < _a.length)) return [3 /*break*/, 4];
                        tokenAddress = _a[_i];
                        position = this.positionManager.getPosition(tokenAddress);
                        if (!position) return [3 /*break*/, 3];
                        return [4 /*yield*/, this.checkStopLossAndTakeProfit(tokenAddress, position)];
                    case 2:
                        _b.sent();
                        _b.label = 3;
                    case 3:
                        _i++;
                        return [3 /*break*/, 1];
                    case 4:
                        // 定期输出统计信息
                        if (this.stats.totalTrades > 0 && this.stats.totalTrades % 10 === 0) {
                            this.logTradingStats();
                        }
                        return [2 /*return*/];
                }
            });
        }); }, 1000); // 每秒检查一次
    };
    /**
     * 获取交易统计
     */
    TradingController.prototype.getTradingStats = function () {
        return __assign({}, this.stats);
    };
    /**
     * 输出交易统计
     */
    TradingController.prototype.logTradingStats = function () {
        this.log('info', '=== Trading Statistics ===');
        this.log('info', "Total Trades: ".concat(this.stats.totalTrades));
        this.log('info', "Win Rate: ".concat((this.stats.winRate * 100).toFixed(2), "%"));
        this.log('info', "Total PnL: ".concat(this.stats.totalPnL.toFixed(4), " SOL"));
        this.log('info', "Average PnL: ".concat(this.stats.averagePnL.toFixed(4), " SOL"));
        this.log('info', "Current Balance: ".concat(this.stats.currentBalance.toFixed(4), " SOL"));
        this.log('info', "Active Positions: ".concat(this.stats.activePositions));
        this.log('info', "Total Fees: ".concat(this.stats.totalFees.toFixed(4), " SOL"));
        this.log('info', '========================');
    };
    /**
     * 获取订阅的Token列表
     */
    TradingController.prototype.getSubscribedTokens = function () {
        return Array.from(this.tokenSubscriptions.keys());
    };
    /**
     * 获取Token的交易历史
     */
    TradingController.prototype.getTokenHistory = function (tokenAddress) {
        return this.transactionHistory.get(tokenAddress) || [];
    };
    /**
     * 更新配置
     */
    TradingController.prototype.updateConfig = function (newConfig) {
        this.config = __assign(__assign({}, this.config), newConfig);
        // 更新子组件配置
        if (newConfig.buyThreshold !== undefined) {
            this.buyPredictor.setThreshold(newConfig.buyThreshold);
        }
        if (newConfig.sellThreshold !== undefined) {
            this.sellPredictor.setThreshold(newConfig.sellThreshold);
        }
    };
    /**
     * 日志记录
     */
    TradingController.prototype.log = function (level, message) {
        var levels = { debug: 0, info: 1, warn: 2, error: 3 };
        var configLevel = levels[this.config.logLevel || 'info'];
        if (levels[level] >= configLevel) {
            var timestamp = new Date().toISOString();
            console.log("[".concat(timestamp, "] [").concat(level.toUpperCase(), "] [TradingController] ").concat(message));
        }
    };
    return TradingController;
}());
exports.TradingController = TradingController;
