import { TradingConfig, TradingStats } from '../types/trading-interfaces';

export class EnhancedRiskManager {
  private config: TradingConfig;
  private stats: TradingStats;
  private dailyStartBalance: number;
  private highWaterMark: number;
  private tradeHistory: Array<{ timestamp: Date; pnl: number; success: boolean }> = [];

  constructor(config: TradingConfig) {
    this.config = config;
    this.dailyStartBalance = config.initialCapitalSol;
    this.highWaterMark = config.initialCapitalSol;
    
    this.stats = {
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      winRate: 0,
      totalPnL: 0,
      totalFees: 0,
      averagePnL: 0,
      maxDrawdown: 0,
      currentBalance: config.initialCapitalSol,
      activePositions: 0,
      consecutiveLosses: 0,
      dailyPnL: 0,
      tradingPaused: false,
      pauseReason: ''
    };
  }

  public canTrade(): { allowed: boolean; reason: string } {
    // 检查是否已暂停交易
    if (this.stats.tradingPaused) {
      return { allowed: false, reason: `交易已暂停: ${this.stats.pauseReason}` };
    }

    // 检查日内损失限制
    if (this.stats.dailyPnL <= -this.config.maxDailyLossSol) {
      this.pauseTrading('达到日内最大损失限制');
      return { allowed: false, reason: '达到日内最大损失限制' };
    }

    // 检查连续亏损限制
    if (this.stats.consecutiveLosses >= this.config.consecutiveLossLimit) {
      this.pauseTrading('达到连续亏损限制');
      return { allowed: false, reason: '达到连续亏损限制' };
    }

    // 检查最大仓位数量
    if (this.stats.activePositions >= this.config.maxPositions) {
      return { allowed: false, reason: '达到最大仓位数量限制' };
    }

    return { allowed: true, reason: '风险检查通过' };
  }

  public recordTrade(pnl: number, fees: number = 0): void {
    const isWinning = pnl > 0;
    
    // 更新基础统计
    this.stats.totalTrades++;
    this.stats.totalPnL += pnl;
    this.stats.totalFees += fees;
    this.stats.currentBalance += pnl - fees;
    this.stats.dailyPnL += pnl - fees;
    
    // 更新胜负统计
    if (isWinning) {
      this.stats.winningTrades++;
      this.stats.consecutiveLosses = 0; // 重置连续亏损
    } else {
      this.stats.losingTrades++;
      this.stats.consecutiveLosses++;
    }
    
    // 计算胜率
    this.stats.winRate = this.stats.totalTrades > 0 ? 
      (this.stats.winningTrades / this.stats.totalTrades) * 100 : 0;
    
    // 计算平均PnL
    this.stats.averagePnL = this.stats.totalTrades > 0 ? 
      this.stats.totalPnL / this.stats.totalTrades : 0;
    
    // 更新最高水位和最大回撤
    if (this.stats.currentBalance > this.highWaterMark) {
      this.highWaterMark = this.stats.currentBalance;
    }
    
    const currentDrawdown = (this.highWaterMark - this.stats.currentBalance) / this.highWaterMark * 100;
    if (currentDrawdown > this.stats.maxDrawdown) {
      this.stats.maxDrawdown = currentDrawdown;
    }
    
    // 记录交易历史
    this.tradeHistory.push({
      timestamp: new Date(),
      pnl: pnl - fees,
      success: isWinning
    });
    
    // 保持历史记录在合理范围内（最近1000笔交易）
    if (this.tradeHistory.length > 1000) {
      this.tradeHistory = this.tradeHistory.slice(-1000);
    }
  }

  private pauseTrading(reason: string): void {
    this.stats.tradingPaused = true;
    this.stats.pauseReason = reason;
    console.log(`⚠️  交易已暂停: ${reason}`);
  }

  public resumeTrading(): void {
    this.stats.tradingPaused = false;
    this.stats.pauseReason = '';
    console.log('✅ 交易已恢复');
  }

  public forceResetRisk(): void {
    this.stats.consecutiveLosses = 0;
    this.stats.tradingPaused = false;
    this.stats.pauseReason = '';
    console.log('⚡ 风险状态已强制重置');
  }

  public resetDailyStats(): void {
    this.stats.dailyPnL = 0;
    this.dailyStartBalance = this.stats.currentBalance;
    console.log('🌅 日内统计已重置');
  }

  public getStats(): TradingStats {
    return { ...this.stats };
  }

  public updateActivePositions(count: number): void {
    this.stats.activePositions = count;
  }

  public getRecentPerformance(hours: number = 24): {
    recentTrades: number;
    recentWinRate: number;
    recentPnL: number;
  } {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    const recentTrades = this.tradeHistory.filter(trade => trade.timestamp >= cutoffTime);
    
    return {
      recentTrades: recentTrades.length,
      recentWinRate: recentTrades.length > 0 ? 
        (recentTrades.filter(t => t.success).length / recentTrades.length) * 100 : 0,
      recentPnL: recentTrades.reduce((sum, trade) => sum + trade.pnl, 0)
    };
  }

  public shouldReduceRisk(): boolean {
    const recent24h = this.getRecentPerformance(24);
    
    // 如果最近24小时胜率低于30%或者连续亏损超过3次
    return recent24h.recentWinRate < 30 || this.stats.consecutiveLosses >= 3;
  }

  public getRiskLevel(): 'low' | 'medium' | 'high' | 'critical' {
    if (this.stats.tradingPaused) return 'critical';
    if (this.stats.consecutiveLosses >= 3) return 'high';
    if (this.stats.maxDrawdown > 15) return 'high';
    if (this.stats.consecutiveLosses >= 2 || this.stats.maxDrawdown > 10) return 'medium';
    return 'low';
  }
} 