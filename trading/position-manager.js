"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PositionManager = void 0;
var PositionManager = /** @class */ (function () {
    function PositionManager(config) {
        if (config === void 0) { config = {}; }
        this.positions = new Map();
        this.positionHistory = [];
        this.config = __assign({ maxPositions: 5, tradeAmount: 0.1 }, config);
    }
    /**
     * 启动仓位管理器
     */
    PositionManager.prototype.start = function () {
        this.log('info', 'Position manager started');
    };
    /**
     * 停止仓位管理器
     */
    PositionManager.prototype.stop = function () {
        this.log('info', 'Position manager stopped');
    };
    /**
     * 开启新仓位
     */
    PositionManager.prototype.openPosition = function (positionData) {
        var position = __assign(__assign({ id: this.generatePositionId() }, positionData), { status: 'open' });
        this.positions.set(position.tokenAddress, position);
        this.log('info', "Opened position: ".concat(position.id, " for ").concat(position.tokenAddress, ", amount: ").concat(position.amount, " SOL"));
        return position;
    };
    /**
     * 关闭仓位
     */
    PositionManager.prototype.closePosition = function (tokenAddress, closeData) {
        var _a;
        var position = this.positions.get(tokenAddress);
        if (!position) {
            this.log('warn', "No position found for token: ".concat(tokenAddress));
            return null;
        }
        if (position.status === 'closed') {
            this.log('warn', "Position ".concat(position.id, " is already closed"));
            return position;
        }
        // 更新仓位信息
        position.exitPrice = closeData.exitPrice;
        position.exitTimestamp = closeData.timestamp;
        position.exitTransactionId = closeData.transactionId;
        position.pnl = closeData.pnl;
        position.status = 'closed';
        // 从活跃仓位中移除，添加到历史记录
        this.positions.delete(tokenAddress);
        this.positionHistory.push(position);
        // 保持历史记录在合理范围内
        if (this.positionHistory.length > 1000) {
            this.positionHistory = this.positionHistory.slice(-1000);
        }
        this.log('info', "Closed position: ".concat(position.id, " for ").concat(position.tokenAddress, ", PnL: ").concat((_a = position.pnl) === null || _a === void 0 ? void 0 : _a.toFixed(4), " SOL"));
        return position;
    };
    /**
     * 关闭指定token的所有仓位
     */
    PositionManager.prototype.closeAllPositions = function (tokenAddress) {
        var closedPositions = [];
        var position = this.positions.get(tokenAddress);
        if (position) {
            // 强制关闭仓位（用于紧急情况）
            var closeData = {
                exitPrice: position.entryPrice, // 使用入场价格作为退出价格
                timestamp: new Date(),
                transactionId: "force_close_".concat(Date.now()),
                pnl: 0 // 强制平仓不计算盈亏
            };
            var closedPosition = this.closePosition(tokenAddress, closeData);
            if (closedPosition) {
                closedPositions.push(closedPosition);
            }
        }
        return closedPositions;
    };
    /**
     * 获取指定token的仓位
     */
    PositionManager.prototype.getPosition = function (tokenAddress) {
        return this.positions.get(tokenAddress) || null;
    };
    /**
     * 获取所有活跃仓位
     */
    PositionManager.prototype.getActivePositions = function () {
        return Array.from(this.positions.values());
    };
    /**
     * 获取活跃仓位数量
     */
    PositionManager.prototype.getActivePositionCount = function () {
        return this.positions.size;
    };
    /**
     * 检查是否可以开新仓位
     */
    PositionManager.prototype.canOpenNewPosition = function () {
        return this.positions.size < (this.config.maxPositions || 5);
    };
    /**
     * 获取仓位历史
     */
    PositionManager.prototype.getPositionHistory = function () {
        return __spreadArray([], this.positionHistory, true);
    };
    /**
     * 获取指定token的仓位历史
     */
    PositionManager.prototype.getTokenPositionHistory = function (tokenAddress) {
        return this.positionHistory.filter(function (pos) { return pos.tokenAddress === tokenAddress; });
    };
    /**
     * 计算总未实现盈亏
     */
    PositionManager.prototype.calculateUnrealizedPnL = function (priceProvider) {
        return __awaiter(this, void 0, void 0, function () {
            var totalUnrealizedPnL, _i, _a, position, currentPrice, unrealizedPnL, error_1;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        totalUnrealizedPnL = 0;
                        _i = 0, _a = this.positions.values();
                        _b.label = 1;
                    case 1:
                        if (!(_i < _a.length)) return [3 /*break*/, 6];
                        position = _a[_i];
                        _b.label = 2;
                    case 2:
                        _b.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, priceProvider(position.tokenAddress)];
                    case 3:
                        currentPrice = _b.sent();
                        unrealizedPnL = (currentPrice - position.entryPrice) * position.amount;
                        totalUnrealizedPnL += unrealizedPnL;
                        return [3 /*break*/, 5];
                    case 4:
                        error_1 = _b.sent();
                        this.log('warn', "Failed to calculate unrealized PnL for ".concat(position.tokenAddress, ": ").concat(error_1));
                        return [3 /*break*/, 5];
                    case 5:
                        _i++;
                        return [3 /*break*/, 1];
                    case 6: return [2 /*return*/, totalUnrealizedPnL];
                }
            });
        });
    };
    /**
     * 计算总已实现盈亏
     */
    PositionManager.prototype.calculateRealizedPnL = function () {
        return this.positionHistory
            .filter(function (pos) { return pos.pnl !== undefined; })
            .reduce(function (total, pos) { return total + (pos.pnl || 0); }, 0);
    };
    /**
     * 获取仓位统计信息
     */
    PositionManager.prototype.getPositionStats = function () {
        var realizedPositions = this.positionHistory.filter(function (pos) { return pos.pnl !== undefined; });
        var winningPositions = realizedPositions.filter(function (pos) { return (pos.pnl || 0) > 0; });
        var losingPositions = realizedPositions.filter(function (pos) { return (pos.pnl || 0) < 0; });
        var totalRealizedPnL = this.calculateRealizedPnL();
        var winRate = realizedPositions.length > 0 ? winningPositions.length / realizedPositions.length : 0;
        var averagePnL = realizedPositions.length > 0 ? totalRealizedPnL / realizedPositions.length : 0;
        var pnls = realizedPositions.map(function (pos) { return pos.pnl || 0; });
        var maxWin = pnls.length > 0 ? Math.max.apply(Math, pnls) : 0;
        var maxLoss = pnls.length > 0 ? Math.min.apply(Math, pnls) : 0;
        return {
            activePositions: this.positions.size,
            totalPositions: realizedPositions.length,
            winningPositions: winningPositions.length,
            losingPositions: losingPositions.length,
            winRate: winRate,
            totalRealizedPnL: totalRealizedPnL,
            averagePnL: averagePnL,
            maxWin: maxWin,
            maxLoss: maxLoss
        };
    };
    /**
     * 更新仓位的市场价格（用于显示未实现盈亏）
     */
    PositionManager.prototype.updatePositionPrices = function (priceProvider) {
        return __awaiter(this, void 0, void 0, function () {
            var _i, _a, position, currentPrice, unrealizedPnL, error_2;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0:
                        _i = 0, _a = this.positions.values();
                        _b.label = 1;
                    case 1:
                        if (!(_i < _a.length)) return [3 /*break*/, 6];
                        position = _a[_i];
                        _b.label = 2;
                    case 2:
                        _b.trys.push([2, 4, , 5]);
                        return [4 /*yield*/, priceProvider(position.tokenAddress)];
                    case 3:
                        currentPrice = _b.sent();
                        unrealizedPnL = (currentPrice - position.entryPrice) * position.amount;
                        // 这里可以存储当前价格和未实现盈亏，但不修改原始仓位数据
                        this.log('debug', "Position ".concat(position.id, ": current price ").concat(currentPrice, ", unrealized PnL: ").concat(unrealizedPnL.toFixed(4), " SOL"));
                        return [3 /*break*/, 5];
                    case 4:
                        error_2 = _b.sent();
                        this.log('warn', "Failed to update price for position ".concat(position.id, ": ").concat(error_2));
                        return [3 /*break*/, 5];
                    case 5:
                        _i++;
                        return [3 /*break*/, 1];
                    case 6: return [2 /*return*/];
                }
            });
        });
    };
    /**
     * 清除所有仓位历史
     */
    PositionManager.prototype.clearHistory = function () {
        this.positionHistory = [];
        this.log('info', 'Position history cleared');
    };
    /**
     * 生成仓位ID
     */
    PositionManager.prototype.generatePositionId = function () {
        return "pos_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
    };
    /**
     * 日志记录
     */
    PositionManager.prototype.log = function (level, message) {
        var timestamp = new Date().toISOString();
        console.log("[".concat(timestamp, "] [").concat(level.toUpperCase(), "] [PositionManager] ").concat(message));
    };
    return PositionManager;
}());
exports.PositionManager = PositionManager;
