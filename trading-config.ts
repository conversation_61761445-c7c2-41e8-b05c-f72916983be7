export interface TradingSystemConfig {
  // 🔑 钱包和连接配置
  wallet: {
    privateKey: string; // Solana钱包私钥（base64格式）
    rpcEndpoint: string; // Solana RPC节点地址
  };
  
  // 💰 交易金额配置
  trading: {
    tradeAmountSol: number; // 每次交易金额（SOL）
    initialCapitalSol: number; // 初始资金（SOL）
    maxPositions: number; // 最大同时持仓数量
    paperTrading: boolean; // 是否启用纸上交易模式
    slippageTolerance: number; // 滑点容忍度（0.05 = 5%）
  };
  
  // 🛡️ 风险管理配置
  risk: {
    // 止损配置
    stopLossPercentage: number; // 止损百分比（0.1 = 10%）
    takeProfitPercentage: number; // 止盈百分比（0.2 = 20%）
    maxLossPerTradeSol: number; // 单笔最大亏损（SOL）
    
    // 累计止损配置
    maxDailyLossSol: number; // 日内最大亏损（SOL）
    maxTotalLossSol: number; // 累计最大亏损（SOL） - 达到后停止所有交易
    maxDrawdownPercentage: number; // 最大回撤百分比（0.3 = 30%）
    
    // 风控参数
    maxPositionSizePercentage: number; // 单仓位最大占总资金比例（0.2 = 20%）
    cooldownPeriodMinutes: number; // 连续亏损后冷却期（分钟）
    consecutiveLossLimit: number; // 连续亏损次数限制
  };
  
  // 🤖 AI模型配置
  models: {
    buyModelPath: string; // 买点预测模型路径
    sellModelPath: string; // 卖点预测模型路径
    buyThreshold: number; // 买入概率阈值（0.5 = 50%）
    sellThreshold: number; // 卖出概率阈值（0.7 = 70%）
    buyWindowSize: number; // 买点预测窗口大小
    sellWindowSize: number; // 卖点预测窗口大小
  };
  
  // 📊 监控配置
  monitoring: {
    targetTokenAddresses: string[]; // 监控的Token地址列表
    logLevel: 'debug' | 'info' | 'warn' | 'error'; // 日志级别
    enableDetailedLogs: boolean; // 是否启用详细日志
    statsUpdateIntervalSeconds: number; // 统计信息更新间隔（秒）
  };
  
  // 🌐 GRPC配置
  grpc: {
    endpoint: string; // Yellowstone GRPC端点
    token: string; // GRPC访问token
  };
}

// 支持部分配置的类型
export type PartialTradingSystemConfig = {
  wallet?: Partial<TradingSystemConfig['wallet']>;
  trading?: Partial<TradingSystemConfig['trading']>;
  risk?: Partial<TradingSystemConfig['risk']>;
  models?: Partial<TradingSystemConfig['models']>;
  monitoring?: Partial<TradingSystemConfig['monitoring']>;
  grpc?: Partial<TradingSystemConfig['grpc']>;
};

// 默认配置
export const DEFAULT_CONFIG: TradingSystemConfig = {
  wallet: {
    privateKey: process.env.PRIVATE_KEY || '',
    rpcEndpoint: process.env.SOLANA_RPC_ENDPOINT || 'https://api.mainnet-beta.solana.com'
  },
  
  trading: {
    tradeAmountSol: 0.1, // 每次交易0.1 SOL
    initialCapitalSol: 3.0, // 初始资金3 SOL
    maxPositions: 5, // 最多同时持有5个仓位
    paperTrading: false, // 默认真实交易
    slippageTolerance: 0.05 // 5%滑点容忍度
  },
  
  risk: {
    // 止损止盈
    stopLossPercentage: 0.1, // 10%止损
    takeProfitPercentage: 0.2, // 20%止盈
    maxLossPerTradeSol: 0.1, // 单笔最大亏损0.1 SOL
    
    // 累计风控
    maxDailyLossSol: 0.5, // 日内最大亏损0.5 SOL
    maxTotalLossSol: 1.0, // 🔴 累计最大亏损1.0 SOL（达到后停止交易）
    maxDrawdownPercentage: 0.3, // 30%最大回撤
    
    // 其他风控
    maxPositionSizePercentage: 0.2, // 单仓位最大20%资金
    cooldownPeriodMinutes: 5, // 连续亏损后5分钟冷却
    consecutiveLossLimit: 3 // 连续亏损3次后冷却
  },
  
  models: {
    buyModelPath: './catboost_focal_blocktol_2025.cbm',
    sellModelPath: './best_catboost_sell_model.cbm',
    buyThreshold: 0.5, // 50%概率买入
    sellThreshold: 0.7, // 70%概率卖出
    buyWindowSize: 10,
    sellWindowSize: 30
  },
  
  monitoring: {
    targetTokenAddresses: process.env.TARGET_TOKEN_ADDRESSES?.split(',').map(addr => addr.trim()) || [],
    logLevel: 'info',
    enableDetailedLogs: false,
    statsUpdateIntervalSeconds: 30
  },
  
  grpc: {
    endpoint: process.env.GRPC_ENDPOINT || 'https://grpc.yellowstone-mainnet.solana.com',
    token: process.env.GRPC_TOKEN || ''
  }
};

/**
 * 从环境变量和配置文件加载配置
 */
export function loadConfig(overrides: PartialTradingSystemConfig = {}): TradingSystemConfig {
  const config = JSON.parse(JSON.stringify(DEFAULT_CONFIG)); // 深拷贝
  
  // 应用用户覆盖配置
  return mergeConfig(config, overrides);
}

/**
 * 递归合并配置对象
 */
function mergeConfig(target: any, source: any): any {
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      if (!target[key]) target[key] = {};
      mergeConfig(target[key], source[key]);
    } else {
      target[key] = source[key];
    }
  }
  return target;
}

/**
 * 验证配置是否有效
 */
export function validateConfig(config: TradingSystemConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // 验证钱包配置
  if (!config.wallet.privateKey && !config.trading.paperTrading) {
    errors.push('真实交易模式下必须设置钱包私钥 (PRIVATE_KEY)');
  }
  
  if (!config.wallet.rpcEndpoint) {
    errors.push('必须设置Solana RPC端点 (SOLANA_RPC_ENDPOINT)');
  }
  
  // 验证交易金额
  if (config.trading.tradeAmountSol <= 0) {
    errors.push('交易金额必须大于0');
  }
  
  if (config.trading.initialCapitalSol <= 0) {
    errors.push('初始资金必须大于0');
  }
  
  if (config.trading.tradeAmountSol > config.trading.initialCapitalSol) {
    errors.push('单次交易金额不能超过初始资金');
  }
  
  // 验证风险参数
  if (config.risk.maxTotalLossSol <= 0) {
    errors.push('累计最大亏损必须大于0');
  }
  
  if (config.risk.maxTotalLossSol > config.trading.initialCapitalSol) {
    errors.push('累计最大亏损不能超过初始资金');
  }
  
  // 验证模型文件
  if (!config.models.buyModelPath || !config.models.sellModelPath) {
    errors.push('必须设置买卖预测模型路径');
  }
  
  // 验证监控配置
  if (config.monitoring.targetTokenAddresses.length === 0) {
    errors.push('必须设置至少一个监控Token地址');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * 显示配置摘要
 */
export function showConfigSummary(config: TradingSystemConfig): void {
  console.log('\n🎯 交易系统配置摘要:');
  console.log('='.repeat(60));
  
  console.log('\n💰 交易配置:');
  console.log(`  每次交易金额: ${config.trading.tradeAmountSol} SOL`);
  console.log(`  初始资金: ${config.trading.initialCapitalSol} SOL`);
  console.log(`  最大仓位数: ${config.trading.maxPositions}`);
  console.log(`  交易模式: ${config.trading.paperTrading ? '📝 纸上交易' : '💸 真实交易'}`);
  
  console.log('\n🛡️ 风险管理:');
  console.log(`  单笔止损: ${config.risk.stopLossPercentage * 100}%`);
  console.log(`  单笔止盈: ${config.risk.takeProfitPercentage * 100}%`);
  console.log(`  日内最大亏损: ${config.risk.maxDailyLossSol} SOL`);
  console.log(`  🔴 累计最大亏损: ${config.risk.maxTotalLossSol} SOL (达到后停止交易)`);
  console.log(`  最大回撤: ${config.risk.maxDrawdownPercentage * 100}%`);
  
  console.log('\n🤖 AI模型:');
  console.log(`  买入阈值: ${config.models.buyThreshold * 100}%`);
  console.log(`  卖出阈值: ${config.models.sellThreshold * 100}%`);
  
  console.log('\n📊 监控:');
  console.log(`  监控Token数量: ${config.monitoring.targetTokenAddresses.length}`);
  console.log(`  Token地址: ${config.monitoring.targetTokenAddresses.slice(0, 3).join(', ')}${config.monitoring.targetTokenAddresses.length > 3 ? '...' : ''}`);
  
  console.log('\n');
} 