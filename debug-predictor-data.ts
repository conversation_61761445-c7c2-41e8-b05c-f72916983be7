#!/usr/bin/env ts-node

/**
 * 🔍 预测器数据输入调试工具
 * 
 * 用于检查买入和卖出预测器的数据输入质量和特征处理是否正确
 */

import { BuyPredictorV4 } from './predictors/v4/buy-predictor-v4';
import { SellPredictorV4 } from './predictors/v4/sell-predictor-v4';
import * as fs from 'fs';

interface TestTransactionData {
  timestamp: Date;
  transaction_type: 'buy' | 'sell';
  sol_amount: number;
  usd_amount: number;
}

interface TestPriceData {
  timestamp: Date;
  price: number;
}

class PredictorDataDebugger {
  private buyPredictor: BuyPredictorV4;
  private sellPredictor: SellPredictorV4;

  constructor() {
    // 初始化V4预测器
    this.buyPredictor = new BuyPredictorV4(
      'predictors/v4/buy_predictor_v4_relative_features_20250624_143400.cbm',
      'predictors/v4/buy_predictor_v4_relative_features_scaler_20250624_143400.json',
      'predictors/v4/buy_predictor_v4_relative_features_features_20250624_143400.json',
      0.5
    );

    this.sellPredictor = new SellPredictorV4(
      'predictors/v4/sell_predictor_v4_json_20250624_124138.cbm',
      'predictors/v4/sell_predictor_v4_json_scaler_20250624_124138.json',
      'predictors/v4/sell_predictor_v4_json_features_20250624_124138.json',
      0.3
    );
  }

  /**
   * 等待模型加载完成
   */
  private async waitForModelsToLoad(): Promise<void> {
    console.log('🔄 等待模型加载...');
    let attempts = 0;
    const maxAttempts = 100;

    while (attempts < maxAttempts) {
      try {
        // 检查模型是否加载完成
        const buyTest = await this.buyPredictor.predictBuy();
        const sellTest = await this.sellPredictor.predictSell();
        
        if (buyTest && sellTest) {
          console.log('✅ 模型加载完成');
          return;
        }
      } catch (error) {
        // 模型还未加载完成，继续等待
      }
      
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
    
    throw new Error('模型加载超时');
  }

  /**
   * 生成测试数据
   */
  private generateTestData(): { transactions: TestTransactionData[], prices: TestPriceData[] } {
    const now = new Date();
    const transactions: TestTransactionData[] = [];
    const prices: TestPriceData[] = [];

    // 生成60秒内的测试交易数据
    for (let i = 0; i < 20; i++) {
      const timestamp = new Date(now.getTime() - (60 - i * 3) * 1000);
      const isBuy = Math.random() > 0.5;
      const solAmount = 0.1 + Math.random() * 2; // 0.1-2.1 SOL
      const price = 0.00001 + Math.random() * 0.0001; // 价格范围
      const usdAmount = solAmount * price * 200; // 假设SOL价格200美元

      transactions.push({
        timestamp,
        transaction_type: isBuy ? 'buy' : 'sell',
        sol_amount: solAmount,
        usd_amount: usdAmount
      });

      prices.push({
        timestamp,
        price
      });
    }

    return { transactions, prices };
  }

  /**
   * 检查数据质量
   */
  private checkDataQuality(transactions: TestTransactionData[], prices: TestPriceData[]): void {
    console.log('\n📊 数据质量检查:');
    
    // 检查交易数据
    const invalidTransactions = transactions.filter(tx => 
      tx.sol_amount <= 0 || 
      tx.usd_amount <= 0 || 
      !['buy', 'sell'].includes(tx.transaction_type)
    );
    
    console.log(`   💰 交易数据: ${transactions.length}笔 (无效: ${invalidTransactions.length}笔)`);
    
    // 检查价格数据
    const invalidPrices = prices.filter(p => p.price <= 0);
    console.log(`   💲 价格数据: ${prices.length}个 (无效: ${invalidPrices.length}个)`);
    
    // 检查时间范围
    const timeSpan = Math.max(...transactions.map(tx => tx.timestamp.getTime())) - 
                    Math.min(...transactions.map(tx => tx.timestamp.getTime()));
    console.log(`   ⏰ 时间跨度: ${(timeSpan / 1000).toFixed(1)}秒`);
    
    // 检查买卖比例
    const buyCount = transactions.filter(tx => tx.transaction_type === 'buy').length;
    const sellCount = transactions.filter(tx => tx.transaction_type === 'sell').length;
    console.log(`   📈 买卖比例: 买入${buyCount}笔 / 卖出${sellCount}笔`);
  }

  /**
   * 测试预测器
   */
  private async testPredictors(transactions: TestTransactionData[], prices: TestPriceData[]): Promise<void> {
    console.log('\n🤖 开始预测器测试...');

    // 添加数据到预测器
    for (const tx of transactions) {
      this.buyPredictor.addTransaction(tx);
      this.sellPredictor.addTransaction(tx);
    }

    for (const price of prices) {
      this.buyPredictor.addPriceData(price);
      this.sellPredictor.addPriceData(price);
    }

    // 执行预测
    console.log('\n🔮 执行预测...');
    
    const buyResult = await this.buyPredictor.predictBuy();
    const sellResult = await this.sellPredictor.predictSell();

    console.log('\n📊 预测结果:');
    console.log(`   📈 买入预测: ${(buyResult.probability * 100).toFixed(3)}% (置信度: ${(buyResult.confidence * 100).toFixed(1)}%)`);
    console.log(`   📉 卖出预测: ${(sellResult.probability * 100).toFixed(3)}% (置信度: ${(sellResult.confidence * 100).toFixed(1)}%)`);
    
    // 检查极端值
    if (buyResult.probability > 0.9 || buyResult.probability < 0.1) {
      console.log(`   ⚠️ 买入概率异常: ${(buyResult.probability * 100).toFixed(3)}%`);
    }
    
    if (sellResult.probability > 0.9 || sellResult.probability < 0.1) {
      console.log(`   ⚠️ 卖出概率异常: ${(sellResult.probability * 100).toFixed(3)}%`);
    }
  }

  /**
   * 检查标准化器参数
   */
  private checkScalerParams(): void {
    console.log('\n🔧 检查标准化器参数...');
    
    try {
      // 检查买入预测器标准化器
      const buyScalerPath = 'predictors/v4/buy_predictor_v4_relative_features_scaler_20250624_143400.json';
      const buyScaler = JSON.parse(fs.readFileSync(buyScalerPath, 'utf8'));
      
      console.log(`   📈 买入预测器标准化器:`);
      console.log(`      类型: ${buyScaler.scaler_type || 'StandardScaler'}`);
      console.log(`      特征数: ${buyScaler.center_?.length || buyScaler.mean_?.length || 0}`);
      console.log(`      样本数: ${buyScaler.n_samples_seen_ || 'unknown'}`);
      
      // 检查卖出预测器标准化器
      const sellScalerPath = 'predictors/v4/sell_predictor_v4_json_scaler_20250624_124138.json';
      const sellScaler = JSON.parse(fs.readFileSync(sellScalerPath, 'utf8'));
      
      console.log(`   📉 卖出预测器标准化器:`);
      console.log(`      类型: ${sellScaler.scaler_type || 'StandardScaler'}`);
      console.log(`      特征数: ${sellScaler.center_?.length || sellScaler.mean_?.length || 0}`);
      console.log(`      样本数: ${sellScaler.n_samples_seen_ || 'unknown'}`);
      
    } catch (error) {
      console.error(`   ❌ 标准化器参数检查失败: ${error}`);
    }
  }

  /**
   * 运行完整的调试流程
   */
  public async run(): Promise<void> {
    try {
      console.log('🔍 预测器数据输入调试工具启动');
      
      // 1. 等待模型加载
      await this.waitForModelsToLoad();
      
      // 2. 检查标准化器参数
      this.checkScalerParams();
      
      // 3. 生成测试数据
      const { transactions, prices } = this.generateTestData();
      
      // 4. 检查数据质量
      this.checkDataQuality(transactions, prices);
      
      // 5. 测试预测器
      await this.testPredictors(transactions, prices);
      
      console.log('\n✅ 调试完成');
      
    } catch (error) {
      console.error('❌ 调试失败:', error);
    }
  }
}

// 运行调试工具
if (require.main === module) {
  const debuggerInstance = new PredictorDataDebugger();
  debuggerInstance.run().then(() => {
    process.exit(0);
  }).catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export { PredictorDataDebugger };
