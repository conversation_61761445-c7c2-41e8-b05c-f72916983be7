import "dotenv/config";
import Client, {
  CommitmentLevel,
  SubscribeRequestAccountsDataSlice,
  SubscribeRequestFilterAccounts,
  SubscribeRequestFilterBlocks,
  SubscribeRequestFilterBlocksMeta,
  SubscribeRequestFilterEntry,
  SubscribeRequestFilterSlots,
  SubscribeRequestFilterTransactions,
} from "@triton-one/yellowstone-grpc";
import { 
  PublicKey, 
  VersionedTransactionResponse,
  Connection,
  Keypair,
  TransactionInstruction,
  VersionedTransaction,
  TransactionMessage,
  ComputeBudgetProgram,
  SystemProgram,
  LAMPORTS_PER_SOL
} from "@solana/web3.js";
import { 
  TOKEN_PROGRAM_ID, 
  getAssociatedTokenAddress,
  createAssociatedTokenAccountIdempotentInstruction,
  ASSOCIATED_TOKEN_PROGRAM_ID,
  getAccount,
  TokenAccountNotFoundError,
  createSyncNativeInstruction,
  getAssociatedTokenAddressSync,
  createCloseAccountInstruction
} from "@solana/spl-token";
import { Idl } from "@coral-xyz/anchor";
import { SolanaParser } from "@shyft-to/solana-transaction-parser";
import { SubscribeRequestPing } from "@triton-one/yellowstone-grpc/dist/types/grpc/geyser";
import { TransactionFormatter } from "./utils/transaction-formatter";
import { SolanaEventParser } from "./utils/event-parser";
import { bnLayoutFormatter } from "./utils/bn-layout-formatter";
import pumpFunAmmIdl from "./idls/pump_amm_0.1.0.json";
import { DynamicSubscriptionManager } from './dynamic-subscription-manager';
import { TradingSystemConfig, PartialTradingSystemConfig, loadConfig, validateConfig, showConfigSummary } from './trading-config';
import { BuyPredictorV4, BuyPredictionResultV4, TransactionDataV4 as BuyTransactionDataV4, PriceDataV4 as BuyPriceDataV4 } from './predictors/v4/buy-predictor-v4';
import { SellPredictorV4, SellPredictionResultV4, TransactionDataForSellV4, PriceDataV4 } from './predictors/v4/sell-predictor-v4';
import { TelegramNotifier, TradingDataProvider } from "./utils/telegram-notifier";
import PriceService from "./utils/price-service";
import bs58 from "bs58";
import axios from "axios";
import fetch from "node-fetch";

// 🔥 导入独立的风险管理模块
import { 
  TradingConfig, 
  TradingStats, 
  Position, 
  SimpleRiskManager, 
  PositionManager, 
  ConfigValidator 
} from './risk-manager';

// 常量初始化
const TXN_FORMATTER = new TransactionFormatter();
const PUMP_FUN_AMM_PROGRAM_ID = new PublicKey("pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA");
const PUMP_FUN_IX_PARSER = new SolanaParser([]);
PUMP_FUN_IX_PARSER.addParserFromIdl(PUMP_FUN_AMM_PROGRAM_ID.toBase58(), pumpFunAmmIdl as Idl);
const PUMP_FUN_EVENT_PARSER = new SolanaEventParser([], console);
PUMP_FUN_EVENT_PARSER.addParserFromIdl(PUMP_FUN_AMM_PROGRAM_ID.toBase58(), pumpFunAmmIdl as Idl);

// Pump.fun 常量
const WSOL_TOKEN_ACCOUNT = new PublicKey('So11111111111111111111111111111111111111112');
const GLOBAL_CONFIG = new PublicKey('ADyA8hdefvWN2dbGGWFotbzWxrAvLW83WG6QCVXvJKqw');
const EVENT_AUTHORITY = new PublicKey('GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR');
const PROTOCOL_FEE_RECIPIENT = new PublicKey('62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV');
const PROTOCOL_FEE_RECIPIENT_TOKEN_ACCOUNT = new PublicKey('94qWNrtmfn42h3ZjUZwWvK1MEo9uVmmrBPd2hpNjYDjb');

// 交易判别器
const BUY_DISCRIMINATOR = new Uint8Array([102, 6, 61, 18, 1, 218, 235, 234]);
const SELL_DISCRIMINATOR = new Uint8Array([51, 230, 133, 164, 1, 127, 131, 173]);

// Jito 配置
const JITO_BLOCK_ENGINE_URL = "https://frankfurt.mainnet.block-engine.jito.wtf";
const JITO_TIP_ACCOUNTS = [
    "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5",
    "HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe", 
    "Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY",
    "ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49",
    "DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh",
    "ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt",
    "DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL",
    "********************************************"
];

// 真实交易配置
const TARGET_WALLET = process.env.TARGET_WALLET || '8J5GUAf7hr3LTPHJSkwrKFDNJPtAXtLHhnNHq6XxTLrW';
const WINDOW_HOURS = parseFloat(process.env.WINDOW_HOURS || '1');
const UPDATE_INTERVAL_MS = parseInt(process.env.UPDATE_INTERVAL_MS || '60000');
const MIN_TRANSACTION_SOL = parseFloat(process.env.MIN_TRANSACTION_SOL || '0.001');
const MAX_MONITORED_TOKENS = parseInt(process.env.MAX_MONITORED_TOKENS || '15');

// 🔥 USE_JITO配置 - 必须在其他常量之前声明
const USE_JITO = process.env.USE_JITO === 'true';

  // 🔥 V4预测器配置 - 修正注释和变量名
  const USE_V4_PREDICTORS = true; // 使用V4预测器（48个相对特征）

  // 🔥 V4模型性能监控
  const V4_PERFORMANCE_STATS = {
    totalPredictions: 0,
    buyPredictions: 0,
    sellPredictions: 0,
    v4ModelLoadTime: 0,
    avgPredictionTime: 0,
    lastResetTime: new Date()
  };

  console.log(`🤖 预测器模式: V4模型 (48个相对特征)`);
  console.log(`📊 V4预测器已启用 - 基于买卖比例和相对变化特征`);
  console.log(`🚫 最小交易过滤: ≥${MIN_TRANSACTION_SOL} SOL`);

// 真实交易参数
const TRADE_AMOUNT_SOL = parseFloat(process.env.TRADE_AMOUNT_SOL || '0.0001'); // 单次买入金额，默认0.0001 SOL
const TIP_AMOUNT = 0.00001; // Jito tip 0.00001 SOL
const PRIORITY_FEE = 0.000001; // Priority fee 0.000001 SOL
// 滑点配置 - 从环境变量读取
const BUY_SLIPPAGE_PERCENT = parseFloat(process.env.BUY_SLIPPAGE_PERCENT || '5'); // 默认5%买入滑点
const SELL_SLIPPAGE_PERCENT = parseFloat(process.env.SELL_SLIPPAGE_PERCENT || '5'); // 默认5%卖出滑点
const BLOCKHASH_UPDATE_INTERVAL = USE_JITO ? 30000 : 2000; // Jito: 30秒, RPC: 2秒

// 钱包和连接配置
const PRIVATE_KEY = process.env.PRIVATE_KEY;
if (!PRIVATE_KEY) {
    throw new Error("PRIVATE_KEY not found in environment variables");
}

// 🔥 修复私钥格式问题 - 专门处理base58格式
let wallet: Keypair;
try {
  console.log(`🔑 尝试加载Base58私钥...`);
  console.log(`   私钥长度: ${PRIVATE_KEY.length} 字符`);
  console.log(`   私钥前缀: ${PRIVATE_KEY.substring(0, 10)}...`);
  
  // 解码base58私钥
  const secretKeyBytes = bs58.decode(PRIVATE_KEY);
  console.log(`   解码后字节长度: ${secretKeyBytes.length} bytes`);
  
  // Solana私钥应该是64字节
  if (secretKeyBytes.length !== 64) {
    throw new Error(`私钥字节长度错误: 期望64字节，实际${secretKeyBytes.length}字节`);
  }
  
  wallet = Keypair.fromSecretKey(secretKeyBytes);
  console.log(`✅ 私钥加载成功: ${wallet.publicKey.toBase58()}`);
  
} catch (error) {
  console.error('❌ Base58私钥加载失败');
  console.error('错误详情:', error);
  console.error('');
  console.error('请检查您的PRIVATE_KEY环境变量:');
  console.error('1. 确保是完整的64字节私钥的base58编码');
  console.error('2. 私钥应该类似: "5Kj...ABC" (88个字符左右)');
  console.error('3. 可以使用 solana-keygen 生成新的密钥对');
  console.error('');
  throw new Error(`私钥加载失败: ${error}`);
}

const connection = new Connection(process.env.SOLANA_RPC_ENDPOINT || "https://solana-rpc.publicnode.com");

// 🔥 新增：独立的查询RPC端点 - 专门用于余额查询、交易确认等查询操作
const QUERY_RPC_ENDPOINT = "https://mainnet.helius-rpc.com/?api-key=4c08b3c1-801f-4656-aa62-79deca4d1a18";
const queryConnection = new Connection(QUERY_RPC_ENDPOINT, {
  commitment: "confirmed",
  confirmTransactionInitialTimeout: 120000, // 2分钟超时
  wsEndpoint: undefined // 不使用websocket
});

console.log(`🔗 交易发送RPC: ${process.env.SOLANA_RPC_ENDPOINT || "https://solana-rpc.publicnode.com"}`);
console.log(`🔍 查询专用RPC: ${QUERY_RPC_ENDPOINT}`);

// 🔥 获取钱包实际SOL余额作为初始资金
async function getWalletSolBalance(): Promise<number> {
  try {
    const balance = await queryConnection.getBalance(wallet.publicKey);
    const solBalance = balance / 1000000000; // 转换为SOL
    console.log(`💰 钱包当前SOL余额: ${solBalance.toFixed(6)} SOL`);
    return solBalance;
  } catch (error) {
    console.error('❌ 获取钱包余额失败:', error);
    const fallbackBalance = parseFloat(process.env.INITIAL_CAPITAL_SOL || '5.0');
    console.log(`⚠️ 使用环境变量备用值: ${fallbackBalance} SOL`);
    return fallbackBalance;
  }
}

// Real Trading 配置 (初始值，将在main函数中更新)
let REAL_TRADING_CONFIG: PartialTradingSystemConfig = {
  trading: {
    tradeAmountSol: TRADE_AMOUNT_SOL,
    initialCapitalSol: parseFloat(process.env.INITIAL_CAPITAL_SOL || '5.0'), // 临时默认值
    maxPositions: parseInt(process.env.MAX_POSITIONS || '3'),
    paperTrading: false, // 🔥 真实交易模式
    slippageTolerance: parseFloat(process.env.SLIPPAGE_TOLERANCE || '0.05') // 5%滑点
  },
  
  risk: {
    stopLossPercentage: parseFloat(process.env.STOP_LOSS_PERCENTAGE || '0.08'),
    takeProfitPercentage: parseFloat(process.env.TAKE_PROFIT_PERCENTAGE || '0.15'),
    maxDailyLossSol: parseFloat(process.env.MAX_DAILY_LOSS_SOL || '1.0'),
    maxTotalLossSol: parseFloat(process.env.MAX_TOTAL_LOSS_SOL || '2.0'),
  },

  models: {
    buyThreshold: parseFloat(process.env.BUY_THRESHOLD || '0.7'), // 70%买入阈值，更严格
    sellThreshold: parseFloat(process.env.SELL_THRESHOLD || '0.6'), // 60%卖出阈值，更严格
  },

  monitoring: {
    targetTokenAddresses: ['So11111111111111111111111111111111111111112'],
    logLevel: 'info' as const,
    enableDetailedLogs: true,
    statsUpdateIntervalSeconds: 30
  },

  grpc: {
    endpoint: process.env.ENDPOINT || 'https://solana-yellowstone-grpc.publicnode.com:443',
    token: process.env.X_TOKEN
  }
};


// 状态更新间隔
const STATUS_UPDATE_INTERVAL = parseInt(process.env.STATUS_UPDATE_INTERVAL_MS || '120000');

console.log('🚀 Real Trading 动态订阅系统 (增强版GRPC解析)');
console.log('=' + '='.repeat(80));
console.log(`🎯 目标钱包: ${TARGET_WALLET}`);
console.log(`💰 真实交易模式: ✅ 启用`);
console.log(`💵 每笔交易: ${TRADE_AMOUNT_SOL} SOL`);
console.log(`🚀 交易方式: ${USE_JITO ? 'Jito Bundle' : 'RPC直发'}`);
if (USE_JITO) {
  console.log(`💸 Jito Tip: ${TIP_AMOUNT} SOL`);
}
console.log(`⚡ Priority Fee: ${PRIORITY_FEE} SOL`);
console.log(`📊 滑点容忍: 买入${BUY_SLIPPAGE_PERCENT}% | 卖出${SELL_SLIPPAGE_PERCENT}%`);
console.log(`🔄 Blockhash更新: 每${BLOCKHASH_UPDATE_INTERVAL/1000}秒 (${USE_JITO ? 'Jito优化' : 'RPC频率'})`);
console.log(`🎯 AI买入阈值: ${(REAL_TRADING_CONFIG.models?.buyThreshold! * 100).toFixed(1)}%`);
console.log(`🎯 AI卖出阈值: ${(REAL_TRADING_CONFIG.models?.sellThreshold! * 100).toFixed(1)}%`);
console.log(`🚫 最小交易过滤: ≥${MIN_TRANSACTION_SOL} SOL`);
console.log(`🔢 Token监控上限: ${MAX_MONITORED_TOKENS} 个`);
console.log(`💼 钱包地址: ${wallet.publicKey.toBase58()}`);
console.log('');

// 🔥 定义我自己的钱包地址，用于GRPC监控
const MY_WALLET = process.env.MY_WALLET || wallet.publicKey.toBase58();

// 🔥 新增：调试模式说明
if (process.env.DEBUG_PARSING === 'true') {
  console.log('🔍 调试模式已启用 - 将显示详细的解析信息');
} else {
  console.log('💡 提示: 设置 DEBUG_PARSING=true 可启用详细解析调试');
}
console.log(''); 

// =============================================================================
// 🔥 新增：专业交易系统组件
// =============================================================================

// 🔥 从环境变量读取新的配置
const STOP_LOSS_PERCENTAGE = parseFloat(process.env.STOP_LOSS_PERCENTAGE || '0.25'); // 默认25%止损
const MAX_DAILY_LOSS_SOL = parseFloat(process.env.MAX_DAILY_LOSS_SOL || '0.5'); // 默认0.5 SOL日亏损限制
const CONSECUTIVE_LOSS_LIMIT = parseInt(process.env.CONSECUTIVE_LOSS_LIMIT || '5'); // 🔥 默认连续亏损5笔后暂停交易

console.log('🛡️ 风险管理配置:');
console.log(`   止损比例: ${(STOP_LOSS_PERCENTAGE * 100).toFixed(1)}%`);
console.log(`   日亏损限制: ${MAX_DAILY_LOSS_SOL} SOL`);
console.log(`   连续亏损限制: ${CONSECUTIVE_LOSS_LIMIT === 999999 ? '已禁用' : CONSECUTIVE_LOSS_LIMIT + ' 笔'}`);
console.log(`   初始资金: 将从钱包实时获取`);
console.log('');

// 配置接口
interface SubscribeRequest {
  accounts: { [key: string]: SubscribeRequestFilterAccounts };
  slots: { [key: string]: SubscribeRequestFilterSlots };
  transactions: { [key: string]: SubscribeRequestFilterTransactions };
  transactionsStatus: { [key: string]: SubscribeRequestFilterTransactions };
  blocks: { [key: string]: SubscribeRequestFilterBlocks };
  blocksMeta: { [key: string]: SubscribeRequestFilterBlocksMeta };
  entry: { [key: string]: SubscribeRequestFilterEntry };
  commitment?: CommitmentLevel | undefined;
  accountsDataSlice: SubscribeRequestAccountsDataSlice[];
  ping?: SubscribeRequestPing | undefined;
}

// Token活动记录接口
interface TokenActivity {
  address: string;
  firstSeen: Date;
  lastSeen: Date;
  transactionCount: number;
  actions: Array<{
    type: 'buy' | 'sell';
    amount: number;
    timestamp: Date;
    price?: number;
  }>;
}

// 真实交易结果接口
interface RealTradeResult {
  success: boolean;
  bundleId?: string;
  actualPrice?: number;
  actualAmount?: number;
  gasFee?: number;
  error?: string;
}

// Pool数据接口
interface PoolData {
  poolAddress: PublicKey;
  mintAddress: PublicKey;
  coinCreator: PublicKey;
  poolBaseTokenAccount: PublicKey;
  poolQuoteTokenAccount: PublicKey;
  vaultAuthority: PublicKey;
  vaultAta: PublicKey;
  tokenBalance: number;
  solBalance: number;
  // 🔥 新增：vault ATA异步计算标记
  needsVaultAta?: boolean;
}

// Token交易实例接口
interface TokenTradingInstance {
  address: string;
  activity: TokenActivity;
  // 🔥 V4 predictors - a new generation of predictors
  buyPredictorV4: BuyPredictorV4;
  sellPredictorV4: SellPredictorV4;
  featureWindow: Array<any>;
  lastPrediction: Date;
  isActive: boolean;
  isTrading: boolean;
  lastTradeTime: Date;
  tradingHistory: Array<{
    type: 'buy' | 'sell';
    timestamp: Date;
    tokenAmount: number;
    solAmount: number;
    price: number;
    prediction: number;
    bundleId?: string;
  }>;
  currentHolding: {
    amount: number;
    buyPrice: number;
    buyTime: Date;
    buySolAmount: number;
    buyGasFee: number;
    buyPlatformFee: number;
    totalBuyCost: number;
    bundleId: string;
  } | null;
  stats: {
    totalTrades: number;
    successfulTrades: number;
    totalPnL: number;
    currentPosition: number;
  };
  lastProcessedTransactionTime?: Date;
  // 🔥 新增：Pool信息提取状态
  poolExtracted?: boolean;
  realPoolData?: PoolData;
  poolData?: PoolData; // 备用池子数据
  // 🔥 新增：交易状态跟踪
  pendingTransactions: Map<string, {
    type: 'buy' | 'sell';
    timestamp: Date;
    signature: string;
    confirmed: boolean;
  }>;
  lastConfirmedSellTime?: Date; // 最后一次确认卖出的时间
  // 🔥 新增：存储最近的AI预测结果
  lastSellPrediction?: number; // 最后一次卖出预测值 (0-100)
  lastBuyPrediction?: number; // 最后一次买入预测值 (0-100)
}

export class RealTradingWithTracker implements TradingDataProvider {
  private subscriptionManager: DynamicSubscriptionManager;
  private config: TradingSystemConfig;
  private currentTokenSubscriptions: Set<string> = new Set();
  private grpcClient: Client | null = null;
  private currentStream: any = null;
  public isRunning: boolean = false;
  private tokenActivities: Map<string, TokenActivity> = new Map();
  private currentSubscription: Set<string> = new Set([TARGET_WALLET, MY_WALLET]);
  private subscriptionUpdateTimer: NodeJS.Timeout | null = null;
  
  // 🔥 新增：专业交易管理器
  private riskManager: SimpleRiskManager;
  private positionManager: PositionManager;
  private tradingConfig: TradingConfig;
  private statusMonitorTimer: NodeJS.Timeout | null = null;
  
  // Token交易实例管理
  private tokenInstances: Map<string, TokenTradingInstance> = new Map();
  private predictionTimer: NodeJS.Timeout | null = null;
  private tokenStatsTimer: NodeJS.Timeout | null = null;
  
  // 🔥 新增：预测锁机制，防止同一token并发预测
  private predictionLocks: Set<string> = new Set();
  private lastPredictionTime: Map<string, Date> = new Map();

  // token监控队列 - 保持token在系统中监控一段时间
  private tokenMonitoringQueue: Array<{ address: string, addedTime: Date }> = [];
  private sellOnlyBufferQueue: Map<string, { 
    address: string, 
    addedTime: Date, 
    reason: string,
    lastCheckTime: Date 
  }> = new Map();

  // Telegram通知服务
  private telegramNotifier: TelegramNotifier;
  
  // 🔥 新增：价格服务
  private priceService: PriceService;
  
  // 统计数据
  private stats = {
    totalTransactions: 0,
    pumpFunTransactions: 0,
    uniqueTokens: 0,
    subscriptionUpdates: 0,
    totalTrades: 0,
    activeTradingTokens: 0
  };

  // 🔥 新增：Blockhash管理
  private currentBlockhash: string = '';
  private blockhashUpdateTimer: NodeJS.Timeout | null = null;
  private lastBlockhashUpdate: Date = new Date(0);

  // Token价格记录
  private tokenPriceHistory: Map<string, {
    lastPrice: number;
    lastUpdateTime: Date;
    priceStartBase: number;
  }> = new Map();

  // 真实价格记录系统
  private realPriceData: Map<string, Array<{
    price: number;
    timestamp: Date;
    solAmount: number;
    tokenAmount: number;
    action: 'buy' | 'sell';
    source: 'stream' | 'own_trade';
  }>> = new Map();

  // 🔥 新增：全局Pool信息缓存 - 不依赖token实例
  private globalPoolCache: Map<string, PoolData> = new Map();
  
  // 🔥 Token Symbol缓存
  private tokenSymbolCache: Map<string, string> = new Map();
  
  // 🔥 新增：已处理交易记录，防止重复处理
  private processedTransactions: Set<string> = new Set();

  constructor() {
    console.log('🤖 初始化AI驱动的Real Trading系统...');
    console.log(`💰 真实交易模式: ${!REAL_TRADING_CONFIG.trading?.paperTrading ? '启用' : '禁用'}`);
    console.log(`🎯 目标钱包: ${TARGET_WALLET}`);
    console.log(`💼 交易钱包: ${wallet.publicKey.toBase58()}`);

    // 验证钱包地址
    if (!this.validateWalletAddress(TARGET_WALLET)) {
      throw new Error(`❌ 无效的钱包地址: ${TARGET_WALLET}`);
    }

    // 🔥 创建新的交易配置
    this.tradingConfig = {
      tradeAmountSol: TRADE_AMOUNT_SOL,
      initialCapitalSol: REAL_TRADING_CONFIG.trading?.initialCapitalSol || parseFloat(process.env.INITIAL_CAPITAL_SOL || '5.0'),
      maxPositions: parseInt(process.env.MAX_POSITIONS || '3'),
      paperTrading: false,
      slippageTolerance: parseFloat(process.env.SLIPPAGE_TOLERANCE || '0.05'),
      maxDailyLossSol: MAX_DAILY_LOSS_SOL,
      stopLossPercentage: STOP_LOSS_PERCENTAGE,
      consecutiveLossLimit: CONSECUTIVE_LOSS_LIMIT,
      // 🔥 新增：连续买入失败限制 - 5次失败后暂停交易
      consecutiveBuyFailureLimit: parseInt(process.env.CONSECUTIVE_BUY_FAILURE_LIMIT || '5'),
      buyThreshold: parseFloat(process.env.BUY_THRESHOLD || '0.7'),
      sellThreshold: parseFloat(process.env.SELL_THRESHOLD || '0.6'),
      // 🔥 新增：交易量检查配置
      lowVolumeAutoSell: true,                                               // 默认启用低交易量自动卖出
      lowVolumeCheckMinutes: 2,                                             // 检查最近2分钟
      lowVolumeThreshold: 1,                                                // 最少1笔交易
      logLevel: 'info' as const,
      enableDetailedLogs: true,
      statsUpdateIntervalSeconds: 30
    };

    // 🔥 配置验证
    const validation = ConfigValidator.validate(this.tradingConfig);
    if (!validation.valid) {
      console.error('❌ 配置验证失败:');
      validation.errors.forEach(error => console.error(`  - ${error}`));
      throw new Error('配置无效，无法启动交易系统');
    }

    // 🔥 显示配置摘要
    ConfigValidator.showConfigSummary(this.tradingConfig);

    // 🔥 初始化专业管理器
    this.riskManager = new SimpleRiskManager(this.tradingConfig);
    this.positionManager = new PositionManager(this.tradingConfig);
    
    // 🔥 启动时重置风险状态（避免历史状态影响）
    this.riskManager.forceResetRisk();
    
    console.log('✅ 风险管理器已初始化');
    console.log('✅ 仓位管理器已初始化');
    console.log('🔄 风险状态已重置');
    console.log(`🗄️ 全局Pool缓存已初始化 (当前: ${this.globalPoolCache.size} 个)`);
    console.log(`🏷️ Token Symbol缓存已初始化 (当前: ${this.tokenSymbolCache.size} 个)`);

    // 初始化Telegram通知服务
    this.telegramNotifier = new TelegramNotifier();
    this.telegramNotifier.setDataProvider(this);
    
    // 🔥 初始化价格服务
    this.priceService = PriceService.getInstance();
    console.log('✅ 价格服务已初始化');
    
    // 初始化系统配置
    this.config = loadConfig(REAL_TRADING_CONFIG);
    
    // 🤖 TradingBot已移除 - 使用独立交易逻辑
    console.log('🤖 TradingBot已移除 - 使用独立交易逻辑');
    
    // 🎯 动态订阅管理器已启动
    console.log('🎯 动态订阅管理器已启动');

    // 初始化动态订阅管理器
    this.subscriptionManager = new DynamicSubscriptionManager(TARGET_WALLET);
    
    console.log('✅ Real Trading配置加载完成');
  }

  /**
   * 🔥 新增：预测器选择助手方法
   */
  private getBuyPredictor(instance: TokenTradingInstance) {
    return instance.buyPredictorV4; // 使用V4预测器
  }

  private getSellPredictor(instance: TokenTradingInstance) {
    return instance.sellPredictorV4; // 使用V4预测器
  }

  // 🔥 V4模型性能统计
  private trackV4PredictionPerformance(predictionType: 'buy' | 'sell', predictionTime: number) {
    if (!USE_V4_PREDICTORS) return;

    V4_PERFORMANCE_STATS.totalPredictions++;
    if (predictionType === 'buy') V4_PERFORMANCE_STATS.buyPredictions++;
    if (predictionType === 'sell') V4_PERFORMANCE_STATS.sellPredictions++;

    // 更新平均预测时间
    V4_PERFORMANCE_STATS.avgPredictionTime =
      (V4_PERFORMANCE_STATS.avgPredictionTime * (V4_PERFORMANCE_STATS.totalPredictions - 1) + predictionTime)
      / V4_PERFORMANCE_STATS.totalPredictions;

    // 每100次预测打印一次统计
    if (V4_PERFORMANCE_STATS.totalPredictions % 100 === 0) {
      console.log(`📊 V4预测器性能统计 (${V4_PERFORMANCE_STATS.totalPredictions}次预测):`);
      console.log(`   🔥 买入预测: ${V4_PERFORMANCE_STATS.buyPredictions}次`);
      console.log(`   🔥 卖出预测: ${V4_PERFORMANCE_STATS.sellPredictions}次`);
      console.log(`   ⏱️ 平均预测时间: ${V4_PERFORMANCE_STATS.avgPredictionTime.toFixed(3)}ms`);
      console.log(`   📈 预测频率: ${(V4_PERFORMANCE_STATS.totalPredictions / ((Date.now() - V4_PERFORMANCE_STATS.lastResetTime.getTime()) / 1000)).toFixed(2)} 预测/秒`);
    }
  }

  private async addTransactionToV4Predictors(instance: TokenTradingInstance, featureData: any): Promise<void> {
    if (!USE_V4_PREDICTORS) return;

    // 🔥 关键修复：正确处理交易类型，忽略未知类型
    let transactionType: 'buy' | 'sell' | null = null;
    if (featureData.action === 1) {
      transactionType = 'buy';
    } else if (featureData.action === 0) {
      transactionType = 'sell';
    }

    // 如果交易类型是未知的，则直接跳过，不添加到预测器
    if (transactionType === null) {
      if (process.env.DEBUG_TRANSACTION_FILTERING === 'true') {
        console.log(`   🟡 V4预测器：跳过未知类型的交易数据 (action: ${featureData.action})`);
      }
      return;
    }
    
    // 尝试从实时价格数据获取正确的价格
    let correctPrice: number | undefined = undefined;
    
    // 详细记录价格获取过程
    if (!this.realPriceData.has(instance.address)) {
      console.warn(`   ❌ V4预测器跳过交易：realPriceData 中没有 token ${instance.address.slice(0, 8)}... 的价格记录`);
      console.warn(`   📊 当前 realPriceData 中共有 ${this.realPriceData.size} 个token的价格数据`);
      return;
    }
    
    const priceHistory = this.realPriceData.get(instance.address)!;
    if (priceHistory.length === 0) {
      console.warn(`   ❌ V4预测器跳过交易：token ${instance.address.slice(0, 8)}... 的价格历史为空`);
      return;
    }
    
    const targetTime = featureData.timestamp.getTime();
    
    // 🔥 修复：先按时间差排序，找到最接近的价格记录
    const sortedPrices = [...priceHistory].sort((a, b) => 
      Math.abs(a.timestamp.getTime() - targetTime) - Math.abs(b.timestamp.getTime() - targetTime)
    );
    
    const nearestPrice = sortedPrices[0];
    const timeDiff = Math.abs(nearestPrice.timestamp.getTime() - targetTime);
    
    // 检查时间差是否在30秒内
    if (timeDiff > 30000) {
      console.warn(`   ❌ V4预测器跳过交易：token ${instance.address.slice(0, 8)}... 在30秒内没有价格数据`);
      console.warn(`   📅 交易时间: ${featureData.timestamp.toLocaleString()}`);
      console.warn(`   📅 最近价格时间: ${nearestPrice.timestamp.toLocaleString()}`);
      console.warn(`   ⏰ 时间差: ${(timeDiff / 1000).toFixed(1)}秒`);
      console.warn(`   📊 该token共有 ${priceHistory.length} 条价格记录`);
      return;
    }
    
    const closestPrice = nearestPrice;
    
    correctPrice = closestPrice.price;
    
    // 🔥 验证价格合理性，如果价格过小，可能需要重新计算
    if (correctPrice < 0.0000000001) {
      console.warn(`   ⚠️ 检测到极小价格 ${correctPrice.toFixed(12)}，可能存在Token数量单位问题`);
      // 尝试从原始数据重新计算价格
      if (closestPrice.solAmount > 0 && closestPrice.tokenAmount > 0) {
        let normalizedTokenAmount = closestPrice.tokenAmount;
        if (closestPrice.tokenAmount > 1_000_000_000_000) {
          normalizedTokenAmount = closestPrice.tokenAmount / 1_000_000_000; // 9位小数
        } else if (closestPrice.tokenAmount > 1_000_000_000) {
          normalizedTokenAmount = closestPrice.tokenAmount / 1_000_000; // 6位小数
        } else if (closestPrice.tokenAmount > 100_000_000) {
          normalizedTokenAmount = closestPrice.tokenAmount / 1_000_000; // 6位小数
        }
        const recalculatedPrice = closestPrice.solAmount / normalizedTokenAmount;
        console.log(`   🔧 价格重新计算: ${correctPrice.toFixed(12)} -> ${recalculatedPrice.toFixed(10)} SOL/token`);
        correctPrice = recalculatedPrice;
      }
    }
    
    console.log(`   ✅ V4预测器使用实时价格: ${correctPrice.toFixed(10)} SOL/token`);
    
    // 🔥 数据质量检查和修正
    const solAmount = featureData.sol_amount || 0;

    // 🔥 修复USD金额计算：买入预测器需要交易的USD价值
    let usdAmount = featureData.usd_amount || 0;
    if (usdAmount <= 0 && solAmount > 0) {
      try {
        // 使用实时SOL价格转换为USD价值
        usdAmount = await this.priceService.convertSolToUsd(solAmount);
        console.log(`   💰 买入预测器USD计算: ${solAmount.toFixed(4)} SOL = $${usdAmount.toFixed(2)} USD`);
      } catch (error) {
        // 使用缓存的SOL价格
        const cachedSolPrice = this.priceService.getCachedSolPrice();
        usdAmount = solAmount * cachedSolPrice;
        console.log(`   💰 买入预测器使用缓存SOL价格: ${solAmount.toFixed(4)} SOL = $${usdAmount.toFixed(2)} USD (SOL价格: $${cachedSolPrice})`);
      }
    }

    // 检查数据有效性
    if (solAmount <= 0) {
      console.log(`   ⚠️ V4预测器：跳过无效SOL金额的交易 (${solAmount})`);
      return;
    }

    if (usdAmount <= 0) {
      console.log(`   ⚠️ V4预测器：跳过无效USD金额的交易 (${usdAmount})`);
      return;
    }

    const v4BuyTransactionData: BuyTransactionDataV4 = {
      timestamp: featureData.timestamp,
      transaction_type: transactionType, // 使用验证过的类型
      sol_amount: solAmount,
      usd_amount: usdAmount
    };

    const v4SellTransactionData: TransactionDataForSellV4 = {
      timestamp: featureData.timestamp,
      transaction_type: transactionType,
      sol_amount: solAmount
    };

    instance.buyPredictorV4.addTransaction(v4BuyTransactionData);
    instance.sellPredictorV4.addTransaction(v4SellTransactionData);
    
    // 🔥 V4卖出预测器需要token价格数据（SOL/token比率）
    const tokenPrice = correctPrice || 0; // 这是SOL/token的价格
    const v4PriceData: PriceDataV4 = {
      timestamp: featureData.timestamp,
      price: tokenPrice
    };
    instance.sellPredictorV4.addPriceData(v4PriceData);

    if (process.env.DEBUG_PREDICTOR_DATA === 'true') {
      console.log(`   ✅ V4买入预测器数据: ${v4BuyTransactionData.transaction_type} ${v4BuyTransactionData.sol_amount}SOL $${v4BuyTransactionData.usd_amount}USD @${v4BuyTransactionData.timestamp.toLocaleTimeString()}`);
      console.log(`   ✅ V4卖出预测器价格: ${v4PriceData.price.toFixed(10)} SOL/token @${v4PriceData.timestamp.toLocaleTimeString()}`);
      console.log(`   📊 数据质量: SOL=${solAmount > 0 ? '✅' : '❌'} USD=${usdAmount > 0 ? '✅' : '❌'} TokenPrice=${tokenPrice > 0 ? '✅' : '❌'}`);

      // 🔍 价格定义验证
      if (usdAmount > 0 && solAmount > 0) {
        const impliedSolUsdRate = usdAmount / solAmount;
        console.log(`   🔍 买入预测器隐含SOL/USD汇率: $${impliedSolUsdRate.toFixed(2)}/SOL`);
      }
      if (tokenPrice > 0) {
        console.log(`   🔍 卖出预测器token价格: ${tokenPrice.toFixed(10)} SOL/token`);
      }
    }
  }

  /**
   * 验证钱包地址
   */
  private validateWalletAddress(address: string): boolean {
    try {
      new PublicKey(address);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 🔥 新增：更新Blockhash - 每5秒更新一次
   */
  private async updateBlockhash(): Promise<void> {
    try {
      const latestBlockhash = await connection.getLatestBlockhash();
      this.currentBlockhash = latestBlockhash.blockhash;
      this.lastBlockhashUpdate = new Date();
      
      console.log(`🔄 Blockhash已更新: ${this.currentBlockhash.slice(0, 8)}... (${this.lastBlockhashUpdate.toLocaleTimeString()})`);
    } catch (error) {
      console.error('❌ 更新Blockhash失败:', error);
    }
  }

  /**
   * 🔥 获取Token Symbol
   */
  private async getTokenSymbol(tokenAddress: string): Promise<string> {
    // 检查缓存
    if (this.tokenSymbolCache.has(tokenAddress)) {
      const cachedSymbol = this.tokenSymbolCache.get(tokenAddress)!;
      console.log(`🔍 使用缓存Token Symbol: ${cachedSymbol} for ${tokenAddress.slice(0, 8)}...`);
      return cachedSymbol;
    }

    try {
      console.log(`🔍 异步获取Token Symbol: ${tokenAddress.slice(0, 8)}...`);
      
      // 🔥 优先使用RPC获取token metadata - 支持多种metadata格式
      const tokenMint = new PublicKey(tokenAddress);
      
      try {
        // 方法1: 获取parsed account info
        const tokenInfo = await connection.getParsedAccountInfo(tokenMint);
        if (tokenInfo.value?.data && 'parsed' in tokenInfo.value.data) {
          const parsedData = tokenInfo.value.data.parsed;
          if (parsedData.info?.symbol) {
            const symbol = parsedData.info.symbol;
            this.tokenSymbolCache.set(tokenAddress, symbol);
            console.log(`✅ RPC Symbol获取成功: ${symbol} for ${tokenAddress.slice(0, 8)}...`);
            return symbol;
          }
        }

        // 方法2: 尝试获取token metadata account (Metaplex格式)
        // Token metadata通常存储在metaplex程序中
        const METADATA_PROGRAM_ID = new PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s');
        
        // 计算metadata account地址
        const [metadataAddress] = PublicKey.findProgramAddressSync(
          [
            Buffer.from('metadata'),
            METADATA_PROGRAM_ID.toBuffer(),
            tokenMint.toBuffer(),
          ],
          METADATA_PROGRAM_ID
        );

        const metadataAccount = await queryConnection.getAccountInfo(metadataAddress);
        if (metadataAccount) {
          // 简单解析metadata（仅获取symbol字段）
          const data = metadataAccount.data;
          if (data.length > 100) {
            // Metaplex metadata格式解析
            try {
              // 跳过前面的固定字段，查找symbol
              let offset = 1 + 32 + 32; // 跳过discriminator + update_authority + mint
              
              // 读取name长度和内容
              const nameLength = data.readUInt32LE(offset);
              offset += 4 + nameLength;
              
              // 读取symbol长度和内容
              const symbolLength = data.readUInt32LE(offset);
              offset += 4;
              
              if (symbolLength > 0 && symbolLength < 20) {
                const symbol = data.slice(offset, offset + symbolLength).toString('utf8').replace(/\0/g, '');
                if (symbol.length > 0) {
                  this.tokenSymbolCache.set(tokenAddress, symbol);
                  console.log(`✅ Metadata Symbol获取成功: ${symbol} for ${tokenAddress.slice(0, 8)}...`);
                  return symbol;
                }
              }
            } catch (parseError) {
              console.log(`⚠️ Metadata解析失败: ${parseError}`);
            }
          }
        }

        console.log(`⚠️ RPC未找到Token Symbol`);
      } catch (rpcError) {
        console.log(`⚠️ RPC获取失败: ${rpcError}`);
      }

      // 🔥 异步启动Symbol获取，不阻塞交易
      this.getTokenSymbolAsync(tokenAddress);

      // 立即返回fallback symbol，不等待异步获取结果
      const fallbackSymbol = tokenAddress.slice(0, 8);
      this.tokenSymbolCache.set(tokenAddress, fallbackSymbol);
      console.log(`⚠️ 使用fallback symbol: ${fallbackSymbol} (异步获取中...)`);
      return fallbackSymbol;

    } catch (error) {
      console.error(`❌ 获取Token Symbol失败: ${error}`);
      const fallbackSymbol = tokenAddress.slice(0, 8);
      this.tokenSymbolCache.set(tokenAddress, fallbackSymbol);
      return fallbackSymbol;
    }
  }

  /**
   * 🔥 格式化Token显示信息 - 包含Symbol和完整地址
   */
  private async formatTokenDisplay(tokenAddress: string): Promise<string> {
    const symbol = await this.getTokenSymbol(tokenAddress);
    const shortAddress = tokenAddress.slice(0, 8) + '...' + tokenAddress.slice(-8);
    
    // 如果symbol就是地址片段，只显示完整地址
    if (symbol === tokenAddress.slice(0, 8)) {
      return `${shortAddress}`;
    }
    
    // 显示 Symbol (地址)
    return `${symbol} (${shortAddress})`;
  }

  /**
   * 🔥 异步获取Token Symbol，不阻塞主流程
   */
  private async getTokenSymbolAsync(tokenAddress: string): Promise<void> {
    try {
      console.log(`🔄 异步后台获取Token Symbol: ${tokenAddress.slice(0, 8)}...`);
      
      // 在后台尝试更多的获取方式
      const tokenMint = new PublicKey(tokenAddress);
      
      // 可以添加更多的获取源，比如Jupiter API、CoinGecko等
      // 这里暂时只用一个简单的重试机制
      setTimeout(async () => {
        try {
          const tokenInfo = await connection.getParsedAccountInfo(tokenMint);
          if (tokenInfo.value?.data && 'parsed' in tokenInfo.value.data) {
            const parsedData = tokenInfo.value.data.parsed;
            if (parsedData.info?.symbol) {
              const symbol = parsedData.info.symbol;
              this.tokenSymbolCache.set(tokenAddress, symbol);
              console.log(`✅ 异步获取到真实Symbol: ${symbol} for ${tokenAddress.slice(0, 8)}...`);
            }
          }
        } catch (error) {
          console.log(`⚠️ 异步Symbol获取失败: ${error}`);
        }
      }, 5000); // 5秒后重试
      
    } catch (error) {
      console.log(`⚠️ 异步Symbol获取启动失败: ${error}`);
    }
  }

  /**
   * 🔥 新增：发送交易到Jito
   */
  private async sendToJito(serializedTx: string): Promise<string> {
    console.log(`📦 发送交易到Jito`);

    for (let attempt = 0; attempt < 3; attempt++) {
      console.log(`🔄 尝试 ${attempt + 1}/3: ${JITO_BLOCK_ENGINE_URL}`);
      
      try {
        const response = await axios.post(`${JITO_BLOCK_ENGINE_URL}/api/v1/bundles`, {
          jsonrpc: "2.0",
          id: 1,
          method: "sendBundle",
          params: [[serializedTx], { encoding: "base64" }]
        }, {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000
        });

        if (response.data.error) {
          console.log(`❌ Jito错误: ${response.data.error.message}`);
          if (attempt === 2) {
            throw new Error(`Jito error: ${response.data.error.message}`);
          }
          continue;
        }

        console.log(`✅ 交易发送成功: ${response.data.result}`);
        return response.data.result;
        
      } catch (error: any) {
        if (error.response?.status === 429) {
          console.log(`⏳ 速率限制，等待2秒后重试...`);
          if (attempt < 2) {
            await new Promise(resolve => setTimeout(resolve, 2000));
            continue;
          }
        }
        
        if (attempt === 2) {
          console.error(`❌ 发送失败`);
          throw error;
        }
      }
    }
    
    throw new Error("Jito端点不可用");
  }

    /**
   * 🔥 创建Pool数据 - 使用存储的池子信息，无需RPC调用
   */
  private async createPoolDataFromCache(tokenMint: PublicKey): Promise<PoolData> {
    console.log(`📋 创建Pool数据 (缓存模式): ${tokenMint.toBase58().slice(0, 8)}...`);
    
    try {
      const tokenAddress = tokenMint.toBase58();
      
      // 🔥 第一优先级：全局Pool缓存
      if (this.globalPoolCache.has(tokenAddress)) {
        console.log(`✅ 使用全局Pool缓存 ${tokenAddress.slice(0, 8)}...`);
        const cachedData = this.globalPoolCache.get(tokenAddress)!;
         
         // 如果需要计算vault ATA，现在计算
        if (cachedData.needsVaultAta) {
           console.log(`🔗 计算缺失的Vault ATA...`);
          cachedData.vaultAta = await getAssociatedTokenAddress(WSOL_TOKEN_ACCOUNT, cachedData.vaultAuthority, true);
          cachedData.needsVaultAta = false;
          console.log(`✅ Vault ATA计算完成: ${cachedData.vaultAta.toBase58()}`);
         }
         
        console.log(`📊 使用全局缓存Pool信息`);
        
        return cachedData;
      }
      
      // 🔥 第二优先级：token实例中的pool数据
      const instance = this.tokenInstances.get(tokenAddress);
      if (instance && instance.realPoolData) {
        console.log(`✅ 使用实例Pool数据 ${tokenAddress.slice(0, 8)}...`);
        const realData = instance.realPoolData;
        
        // 同时保存到全局缓存
        this.globalPoolCache.set(tokenAddress, realData);
         
         return realData;
       }
       
       // 如果有存储的池子信息，使用缓存
       if (instance && instance.poolData) {
        console.log(`✅ 使用备用Pool数据 ${tokenAddress.slice(0, 8)}...`);
         return instance.poolData;
       }
      
      // 🔥 如果没有从GRPC提取到pool数据，暂时跳过交易
      console.log(`⚠️ 无法创建Pool数据: 缺少从GRPC提取的真实pool信息`);
      console.log(`   原因: 该token还没有被目标钱包交易过，无法从GRPC数据中提取pool信息`);
      console.log(`   策略: 跳过本次交易，等待目标钱包对该token进行buy/sell操作`);
      console.log(`   说明: 每个token都有独特的pool，必须从实际交易中提取真实的pool地址`);
      throw new Error(`Token ${tokenAddress.slice(0, 8)}... 缺少pool信息，等待目标钱包交易`);
      
    } catch (error) {
      console.error(`❌ 创建Pool数据失败 ${tokenMint.toBase58().slice(0, 8)}...:`, error);
      throw error;
    }
  }

  /**
   * 🔥 从池子账户数据中动态获取coinCreator
   */
  private async fetchCoinCreatorFromPool(poolAddress: PublicKey): Promise<PublicKey> {
    console.log(`🔍 从池子账户获取Coin Creator: ${poolAddress.toBase58().slice(0, 8)}...`);
    
    const poolAccountInfo = await queryConnection.getAccountInfo(poolAddress);
    if (!poolAccountInfo) {
      throw new Error(`无法获取池子账户信息: ${poolAddress.toBase58()}`);
    }
    
    if (poolAccountInfo.data.length < 67) {
      throw new Error(`池子账户数据长度不足: ${poolAccountInfo.data.length} < 67`);
    }
    
    // 从池子数据中提取coinCreator (offset 35-67)
    const coinCreatorBytes = poolAccountInfo.data.slice(35, 67);
    const coinCreator = new PublicKey(coinCreatorBytes);
    
    console.log(`✅ 动态获取Coin Creator成功: ${coinCreator.toBase58()}`);
    return coinCreator;
  }

  /**
   * 🔥 从GRPC交易数据中提取并存储完整Pool信息 - 保存到全局缓存
   */
  private async extractPoolDataFromTransaction(tokenMint: PublicKey, parsedTxn: any): Promise<void> {
    const tokenAddress = tokenMint.toBase58();
    
    // 🔥 检查全局缓存是否已经有pool信息
    if (this.globalPoolCache.has(tokenAddress)) {
      return;
    }
    
    // 检查token实例是否已经提取过
    const instance = this.tokenInstances.get(tokenAddress);
    if (instance && (instance.poolExtracted || instance.realPoolData)) {
      return;
    }
    
    try {
      // 🔥 简化：直接从指令中提取pool信息，不限制指令类型
      if (!parsedTxn || !parsedTxn.instructions || !Array.isArray(parsedTxn.instructions)) {
        return;
      }
      
      for (let i = 0; i < parsedTxn.instructions.length; i++) {
        const ix = parsedTxn.instructions[i];
        
        try {
          // 检查是否是pump.fun AMM指令
          if (!ix || !ix.programId || !ix.accounts || !Array.isArray(ix.accounts)) {
            continue;
          }
          
          const isPumpFunInstruction = ix.programId === PUMP_FUN_AMM_PROGRAM_ID.toBase58() || 
                                     ix.programId.toString() === PUMP_FUN_AMM_PROGRAM_ID.toBase58();
          
          if (!isPumpFunInstruction) {
            continue;
          }
          
          // 🔥 简化：直接提取所有相关账户，不管指令类型
          const accountMap = new Map<string, string>();
          
          for (const account of ix.accounts) {
            if (account && account.name && account.pubkey) {
              // 🔥 确保pubkey是完整的字符串
              const fullPubkey = account.pubkey.toString();
              accountMap.set(account.name, fullPubkey);
            }
          }
          
          // 提取关键账户
          const poolAddress = accountMap.get('pool');
          const mintAddress = accountMap.get('base_mint');
          const poolBaseTokenAccount = accountMap.get('pool_base_token_account');
          const poolQuoteTokenAccount = accountMap.get('pool_quote_token_account');
          
          // 🔥 新增：尝试从事件数据中提取coinCreator
          let coinCreator: string | undefined;
          if (parsedTxn.events && Array.isArray(parsedTxn.events)) {
            for (const event of parsedTxn.events) {
              if (event.data && event.data.creator) {
                coinCreator = event.data.creator;
                break;
              }
            }
          }
          
          // 🔥 尝试从Remaining账户中获取vault信息
          let vaultAta = accountMap.get('vault_ata');
          let vaultAuthority = accountMap.get('vault_authority');
          
          // 如果没有找到vault信息，尝试从Remaining账户中获取
          if (!vaultAta || !vaultAuthority) {
            for (const [name, address] of accountMap.entries()) {
              if (name.startsWith('Remaining')) {
                // 这里可以根据需要添加更多的逻辑来识别vault账户
                if (!vaultAta && name === 'Remaining 0') {
                  vaultAta = address;
                }
                if (!vaultAuthority && name === 'Remaining 1') {
                  vaultAuthority = address;
                }
              }
            }
          }
          
          // 🔥 验证：mint地址匹配且有基本pool信息
          if (mintAddress === tokenAddress && poolAddress && poolBaseTokenAccount && poolQuoteTokenAccount) {
            try {
              // 🔥 安全地创建PublicKey对象，添加验证
              const validateAndCreatePublicKey = (address: string, name: string): PublicKey => {
                if (!address) {
                  throw new Error(`${name} 地址为空`);
                }
                
                // 🔥 检查地址长度，Solana地址通常是32-44字符
                if (address.length < 32) {
                  throw new Error(`${name} 地址太短: ${address} (长度: ${address.length})`);
                }
                
                // 🔥 检查地址是否包含无效字符
                const base58Regex = /^[1-9A-HJ-NP-Za-km-z]+$/;
                if (!base58Regex.test(address)) {
                  // 尝试清理地址
                  const cleanedAddress = address.replace(/[^1-9A-HJ-NP-Za-km-z]/g, '');
                  if (cleanedAddress.length >= 32) {
                    address = cleanedAddress;
                  }
                }
                
                try {
                  // 🔥 直接尝试创建PublicKey，不管长度是多少
                  const publicKey = new PublicKey(address);
                  return publicKey;
                } catch (error) {
                  // 🔥 对于vault地址，如果无效就抛出错误，不使用默认值
                  if (name.includes('Vault')) {
                    throw new Error(`${name} 地址无效且无法修复: "${address}" - ${error}`);
                  }
                  
                  // 🔥 对于非vault地址，如果创建失败，抛出错误
                  throw new Error(`${name} 地址无效: "${address}" - ${error}`);
                }
              };
              
              // 🔥 创建Pool数据并保存到全局缓存
              // 先创建核心地址（这些是必需的）
              const poolAddressPubkey = validateAndCreatePublicKey(poolAddress, 'Pool');
              const mintAddressPubkey = validateAndCreatePublicKey(mintAddress, 'Mint');
              const poolBaseTokenAccountPubkey = validateAndCreatePublicKey(poolBaseTokenAccount, 'Base Token Account');
              const poolQuoteTokenAccountPubkey = validateAndCreatePublicKey(poolQuoteTokenAccount, 'Quote Token Account');
              
              // 🔥 动态获取coinCreator - 从池子账户数据中提取
              let coinCreatorPubkey: PublicKey;
              
              // 首先尝试从事件中获取
              if (coinCreator) {
                try {
                  coinCreatorPubkey = validateAndCreatePublicKey(coinCreator, 'Coin Creator');
                } catch (error) {
                  coinCreatorPubkey = await this.fetchCoinCreatorFromPool(poolAddressPubkey);
                }
              } else {
                // 从池子账户数据中动态获取coinCreator
                coinCreatorPubkey = await this.fetchCoinCreatorFromPool(poolAddressPubkey);
              }

              // 🔥 修复：获取真实的池子余额，不使用硬编码值
              let realBalances: { tokenBalance: number, solBalance: number };
              
              // 首先尝试从事件数据中提取
              const eventBalances = this.extractRealPoolBalancesFromEvents(parsedTxn);
              if (eventBalances && eventBalances.tokenBalance && eventBalances.solBalance) {
                realBalances = {
                  tokenBalance: eventBalances.tokenBalance,
                  solBalance: eventBalances.solBalance
                };
                console.log(`✅ 使用事件中的真实池子余额`);
              } else {
                // 如果事件中没有，通过RPC获取真实余额
                console.log(`🔍 事件中无池子余额，通过RPC获取...`);
                const tempPoolData = {
                  poolAddress: poolAddressPubkey,
                  mintAddress: mintAddressPubkey,
                  coinCreator: coinCreatorPubkey,
                  poolBaseTokenAccount: poolBaseTokenAccountPubkey,
                  poolQuoteTokenAccount: poolQuoteTokenAccountPubkey,
                  vaultAuthority: new PublicKey('11111111111111111111111111111111'), // 临时值
                  vaultAta: new PublicKey('11111111111111111111111111111111'), // 临时值
                  tokenBalance: 0,
                  solBalance: 0
                };
                
                realBalances = await this.fetchRealPoolBalances(tempPoolData);
              }

              const corePoolData = {
                poolAddress: poolAddressPubkey,
                mintAddress: mintAddressPubkey,
                coinCreator: coinCreatorPubkey,
                poolBaseTokenAccount: poolBaseTokenAccountPubkey,
                poolQuoteTokenAccount: poolQuoteTokenAccountPubkey,
                tokenBalance: realBalances.tokenBalance,
                solBalance: realBalances.solBalance
              };
              
              console.log(`✅ 核心Pool数据创建成功`);
              
              // 然后尝试添加vault信息（这些可以失败）
              let finalVaultAuthority: PublicKey;
              let finalVaultAta: PublicKey;
              let needsVaultAta = true;
              
              if (vaultAuthority && vaultAta) {
                finalVaultAuthority = validateAndCreatePublicKey(vaultAuthority, 'Vault Authority');
                finalVaultAta = validateAndCreatePublicKey(vaultAta, 'Vault ATA');
                needsVaultAta = false;
              } else {
                // 通过coinCreator计算vault信息
                const [calculatedVaultAuthority] = PublicKey.findProgramAddressSync(
                  [Buffer.from("creator_vault"), coinCreatorPubkey.toBuffer()],
                  PUMP_FUN_AMM_PROGRAM_ID
                );
                finalVaultAuthority = calculatedVaultAuthority;
                finalVaultAta = await getAssociatedTokenAddress(WSOL_TOKEN_ACCOUNT, calculatedVaultAuthority, true);
                needsVaultAta = false;
              }
              
              const poolData: PoolData = {
                ...corePoolData,
                vaultAuthority: finalVaultAuthority,
                vaultAta: finalVaultAta,
                needsVaultAta
              };
              
              // 🔥 保存到全局缓存
              this.globalPoolCache.set(tokenAddress, poolData);
              
              // 🔥 如果token实例存在，也保存到实例中
              if (instance) {
                instance.realPoolData = poolData;
                instance.poolExtracted = true;
                
                // 更新池子余额估算
                this.updatePoolBalanceFromEvents(instance, parsedTxn);
              }
              
              return;
              
            } catch (validationError) {
              console.error(`❌ Pool数据验证失败 ${tokenAddress.slice(0, 8)}...:`, validationError);
              console.error(`   错误详情: ${validationError instanceof Error ? validationError.message : String(validationError)}`);
              // 继续处理下一个指令
            }
          } else {
            if (mintAddress !== tokenAddress) {
              console.log(`⚠️ Mint地址不匹配: 期望 ${tokenAddress.slice(0, 8)}..., 实际 ${(mintAddress || 'N/A').slice(0, 8)}...`);
            } else {
              console.log(`⚠️ 缺少必需的pool账户信息`);
            }
          }
          
        } catch (ixError) {
          console.error(`❌ 处理指令 [${i}] 时出错:`, ixError);
          continue;
        }
      }
      
      console.log(`⚠️ 未能从任何指令中提取到有效的Pool信息`);
      
    } catch (error) {
      console.error(`❌ 提取Pool信息时出错:`, error);
      console.error(`   错误详情: ${error instanceof Error ? error.message : String(error)}`);
      if (error instanceof Error && error.stack) {
        console.error(`   错误堆栈:`, error.stack.split('\n').slice(0, 3).join('\n'));
      }
    }
  }

  /**
   * 🔥 修复：从事件数据中更新真实池子余额 - 不再使用估算
   */
  private updatePoolBalanceFromEvents(instance: TokenTradingInstance, parsedTxn: any): void {
    try {
      if (!instance.realPoolData) {
        return;
      }
      
      // 尝试从事件中获取真实的池子余额
      const realBalances = this.extractRealPoolBalancesFromEvents(parsedTxn);
      if (realBalances && realBalances.tokenBalance && realBalances.solBalance) {
        // 使用事件中的真实余额更新
        instance.realPoolData.tokenBalance = realBalances.tokenBalance;
        instance.realPoolData.solBalance = realBalances.solBalance;
      }
    } catch (error) {
      // Silently continue
    }
  }

  /**
   * 🔥 新增：计算滑点后的代币数量 - 买入使用买入滑点
   */
  private calculateTokensWithSlippage(solAmount: number, poolData: PoolData): { expectedTokens: bigint, minTokens: bigint } {
    const tokensPerSol = poolData.tokenBalance / poolData.solBalance;
    const expectedTokens = BigInt(Math.floor(solAmount * tokensPerSol * 1_000_000)); // 6 decimals
    const minTokens = BigInt(Math.floor(Number(expectedTokens) * (100 - BUY_SLIPPAGE_PERCENT) / 100));
    
    return { expectedTokens, minTokens };
  }

  /**
   * 🔥 新增：创建买入指令
   */
  private async createBuyInstruction(
    poolData: PoolData, 
    user: PublicKey
  ): Promise<TransactionInstruction[]> {
    const userBaseTokenAccount = await getAssociatedTokenAddress(poolData.mintAddress, user);
    const userQuoteTokenAccount = await getAssociatedTokenAddress(WSOL_TOKEN_ACCOUNT, user);
    
    const maxQuoteAmountIn = BigInt(Math.floor(TRADE_AMOUNT_SOL * LAMPORTS_PER_SOL));
    const { minTokens } = this.calculateTokensWithSlippage(TRADE_AMOUNT_SOL, poolData);
    
    const instructions: TransactionInstruction[] = [];
    
    // 添加计算预算和优先费
    instructions.push(
      ComputeBudgetProgram.setComputeUnitPrice({ microLamports: Math.floor(PRIORITY_FEE * LAMPORTS_PER_SOL) }),
      ComputeBudgetProgram.setComputeUnitLimit({ units: 1000000 })
    );
    
    // 创建代币账户（如果需要）
    instructions.push(
      createAssociatedTokenAccountIdempotentInstruction(
        user,
        userBaseTokenAccount,
        user,
        poolData.mintAddress
      )
    );
    
    // 🔥 修复：创建WSOL账户
    instructions.push(
      createAssociatedTokenAccountIdempotentInstruction(
        user,
        userQuoteTokenAccount,
        user,
        WSOL_TOKEN_ACCOUNT
      )
    );

    // 🔥 修复：将SOL包装成WSOL - 这是关键步骤！
    instructions.push(
      SystemProgram.transfer({
        fromPubkey: user,
        toPubkey: userQuoteTokenAccount,
        lamports: maxQuoteAmountIn,
      })
    );

    // 🔥 修复：同步WSOL账户
    instructions.push(
      createSyncNativeInstruction(userQuoteTokenAccount)
    );

    // 🔥 修复：使用成功版本的账户顺序 (来自optimized-trading-from-grpc.ts)
    const accounts = [
      { pubkey: poolData.poolAddress, isSigner: false, isWritable: false },
      { pubkey: user, isSigner: true, isWritable: true },
      { pubkey: GLOBAL_CONFIG, isSigner: false, isWritable: false },
      { pubkey: poolData.mintAddress, isSigner: false, isWritable: false },
      { pubkey: WSOL_TOKEN_ACCOUNT, isSigner: false, isWritable: false },
      { pubkey: userBaseTokenAccount, isSigner: false, isWritable: true },
      { pubkey: userQuoteTokenAccount, isSigner: false, isWritable: true },
      { pubkey: poolData.poolBaseTokenAccount, isSigner: false, isWritable: true },
      { pubkey: poolData.poolQuoteTokenAccount, isSigner: false, isWritable: true },
      { pubkey: PROTOCOL_FEE_RECIPIENT, isSigner: false, isWritable: false },
      { pubkey: PROTOCOL_FEE_RECIPIENT_TOKEN_ACCOUNT, isSigner: false, isWritable: true },
      { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
      { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
      { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
      { pubkey: ASSOCIATED_TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
      { pubkey: EVENT_AUTHORITY, isSigner: false, isWritable: false },
      { pubkey: PUMP_FUN_AMM_PROGRAM_ID, isSigner: false, isWritable: false },
      { pubkey: poolData.vaultAta, isSigner: false, isWritable: true },
      { pubkey: poolData.vaultAuthority, isSigner: false, isWritable: false },
    ];

    // 打包指令数据
    const data = Buffer.alloc(24);
    data.set(BUY_DISCRIMINATOR, 0);
    data.writeBigUInt64LE(minTokens, 8);
    data.writeBigUInt64LE(maxQuoteAmountIn, 16);

    const buyInstruction = new TransactionInstruction({
      keys: accounts,
      programId: PUMP_FUN_AMM_PROGRAM_ID,
      data: data,
    });

    instructions.push(buyInstruction);
    
    // 🔥 只在使用Jito时添加tip
    if (USE_JITO) {
      const tipAccount = new PublicKey(JITO_TIP_ACCOUNTS[Math.floor(Math.random() * JITO_TIP_ACCOUNTS.length)]);
      const tipInstruction = SystemProgram.transfer({
        fromPubkey: user,
        toPubkey: tipAccount,
        lamports: Math.floor(TIP_AMOUNT * LAMPORTS_PER_SOL),
      });
      
      instructions.push(tipInstruction);
    }
    
    return instructions;
  }

  /**
   * 🔥 新增：计算卖出滑点后的最少SOL数量
   */
  private calculateMinSolWithSlippage(tokenAmount: number, poolData: PoolData): bigint {
    const solPerToken = poolData.solBalance / poolData.tokenBalance;
    const expectedSol = tokenAmount * solPerToken;
    const minSol = expectedSol * (100 - SELL_SLIPPAGE_PERCENT) / 100;
    const minSolLamports = BigInt(Math.floor(minSol * LAMPORTS_PER_SOL));
    
    console.log(`🔍 卖出滑点计算详情:`);
    console.log(`   Token数量: ${tokenAmount.toFixed(6)}`);
    console.log(`   池子SOL余额: ${poolData.solBalance.toFixed(6)}`);
    console.log(`   池子Token余额: ${poolData.tokenBalance.toFixed(6)}`);
    console.log(`   每Token价格: ${solPerToken.toFixed(8)} SOL`);
    console.log(`   预期获得SOL: ${expectedSol.toFixed(6)}`);
    console.log(`   滑点百分比: ${SELL_SLIPPAGE_PERCENT}%`);
    console.log(`   滑点后最小SOL: ${minSol.toFixed(6)}`);
    console.log(`   Lamports值: ${minSolLamports.toString()}`);
    
    return minSolLamports;
  }

  /**
   * 🔥 新增：创建卖出指令
   */
  private async createSellInstruction(
    poolData: PoolData,
    user: PublicKey,
    tokenAmount: bigint
  ): Promise<TransactionInstruction[]> {
    const userBaseTokenAccount = await getAssociatedTokenAddress(poolData.mintAddress, user);
    const userQuoteTokenAccount = await getAssociatedTokenAddress(WSOL_TOKEN_ACCOUNT, user);
    
    // 🔥 使用卖出滑点计算最少获得的SOL
    const tokenAmountNumber = Number(tokenAmount) / 1_000_000; // 转换为实际token数量
    const minQuoteAmountOut = this.calculateMinSolWithSlippage(tokenAmountNumber, poolData);
    

    
    const instructions: TransactionInstruction[] = [];
    
    // 添加计算预算和优先费
    instructions.push(
      ComputeBudgetProgram.setComputeUnitPrice({ microLamports: Math.floor(PRIORITY_FEE * LAMPORTS_PER_SOL) }),
      ComputeBudgetProgram.setComputeUnitLimit({ units: 1000000 })
    );
    
    // 🔥 修复：创建WSOL账户来接收卖出的SOL
    instructions.push(
      createAssociatedTokenAccountIdempotentInstruction(
        user,
        userQuoteTokenAccount,
        user,
        WSOL_TOKEN_ACCOUNT
      )
    );
    
    // 🔥 修复：使用成功版本的账户顺序 (来自optimized-trading-from-grpc.ts)
    const accounts = [
      { pubkey: poolData.poolAddress, isSigner: false, isWritable: false },
      { pubkey: user, isSigner: true, isWritable: true },
      { pubkey: GLOBAL_CONFIG, isSigner: false, isWritable: false },
      { pubkey: poolData.mintAddress, isSigner: false, isWritable: false },
      { pubkey: WSOL_TOKEN_ACCOUNT, isSigner: false, isWritable: false },
      { pubkey: userBaseTokenAccount, isSigner: false, isWritable: true },
      { pubkey: userQuoteTokenAccount, isSigner: false, isWritable: true },
      { pubkey: poolData.poolBaseTokenAccount, isSigner: false, isWritable: true },
      { pubkey: poolData.poolQuoteTokenAccount, isSigner: false, isWritable: true },
      { pubkey: PROTOCOL_FEE_RECIPIENT, isSigner: false, isWritable: false },
      { pubkey: PROTOCOL_FEE_RECIPIENT_TOKEN_ACCOUNT, isSigner: false, isWritable: true },
      { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
      { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
      { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
      { pubkey: ASSOCIATED_TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },
      { pubkey: EVENT_AUTHORITY, isSigner: false, isWritable: false },
      { pubkey: PUMP_FUN_AMM_PROGRAM_ID, isSigner: false, isWritable: false },
      { pubkey: poolData.vaultAta, isSigner: false, isWritable: true },
      { pubkey: poolData.vaultAuthority, isSigner: false, isWritable: false },
    ];

    // 打包指令数据
    const data = Buffer.alloc(24);
    data.set(SELL_DISCRIMINATOR, 0);
    data.writeBigUInt64LE(tokenAmount, 8);
    data.writeBigUInt64LE(minQuoteAmountOut, 16);

    const sellInstruction = new TransactionInstruction({
      keys: accounts,
      programId: PUMP_FUN_AMM_PROGRAM_ID,
      data: data,
    });

    instructions.push(sellInstruction);
    
    // 🔥 修复：卖出后将WSOL同步为原生SOL
    instructions.push(
      createSyncNativeInstruction(userQuoteTokenAccount)
    );
    
    // 🔥 新增：关闭WSOL账户，将余额转回SOL
    instructions.push(
      createCloseAccountInstruction(
        userQuoteTokenAccount,
        user,
        user
      )
    );
    
    // 🔥 只在使用Jito时添加tip
    if (USE_JITO) {
      const tipAccount = new PublicKey(JITO_TIP_ACCOUNTS[Math.floor(Math.random() * JITO_TIP_ACCOUNTS.length)]);
      const tipInstruction = SystemProgram.transfer({
        fromPubkey: user,
        toPubkey: tipAccount,
        lamports: Math.floor(TIP_AMOUNT * LAMPORTS_PER_SOL),
      });
      
      instructions.push(tipInstruction);
    }
    
    return instructions;
  }

  /**
   * 🔥 修复：执行真实买入交易 - 改进价格计算和确认机制
   */
  private async executeRealBuy(tokenAddress: string, instance: TokenTradingInstance, buyPrediction: number): Promise<RealTradeResult> {
    try {
      // 🔥 修复：优先从全局缓存获取池子数据，然后从实例获取
      let poolData = this.globalPoolCache.get(tokenAddress) || instance.realPoolData || instance.poolData;
      
      // 如果仍然没有池子数据，尝试创建
      if (!poolData) {
        console.log(`🔧 没有缓存的池子数据，尝试重新创建...`);
        try {
          const tokenMint = new PublicKey(tokenAddress);
          poolData = await this.createPoolDataFromCache(tokenMint);
          // 更新实例数据
          instance.realPoolData = poolData;
          // 更新全局缓存
          this.globalPoolCache.set(tokenAddress, poolData);
        } catch (error) {
          console.error(`❌ 创建池子数据失败:`, error);
        return {
          success: false,
            error: '无法获取池子数据'
          };
        }
      }

      // 🔥 新增：买入前获取最新的池子余额
      try {
        console.log(`🔄 买入前获取最新池子余额...`);
        const latestBalances = await this.fetchRealPoolBalances(poolData);
        poolData.tokenBalance = latestBalances.tokenBalance;
        poolData.solBalance = latestBalances.solBalance;
        console.log(`✅ 已更新为最新池子余额`);
      } catch (balanceError) {
        console.log(`⚠️ 获取最新余额失败，使用缓存数据:`, balanceError);
      }

      // 使用全局钱包变量
      const solAmount = this.tradingConfig.tradeAmountSol;
      
      // 💨 获取基础价格（基于池子比率）
      const basePrice = poolData.solBalance / poolData.tokenBalance;
      const baseTokenAmount = solAmount / basePrice;
      
      // 🔥 新价格计算逻辑：移除市场波动，添加手续费
      // 大额交易会对价格产生更大影响，小额交易影响较小
      const poolTotalValue = poolData.solBalance + (poolData.tokenBalance * basePrice);
      const tradeImpactRatio = solAmount / poolTotalValue;
      
      // 价格影响：大额交易会导致价格上涨
      const priceImpact = Math.min(tradeImpactRatio * 0.5, 0.02); // 最大2%价格影响
      
      // 滑点损失：买入时通常价格会更高
      const slippageLoss = Math.random() * 0.01; // 0% 到 +1%滑点损失
      
      // 手续费：0.3%
      const tradingFee = 0.003; // 0.3%
      
      // 计算真实执行价格（买入时价格上涨）
      const totalPriceChange = priceImpact + slippageLoss + tradingFee;
      const estimatedExecutionPrice = basePrice * (1 + totalPriceChange);
      const estimatedTokensReceived = solAmount / estimatedExecutionPrice;
      
      console.log(`💰 买入价格计算详情:`);
      console.log(`   池子基础价格: ${basePrice.toFixed(8)} SOL/token`);
      console.log(`   价格影响: ${(priceImpact * 100).toFixed(3)}% (上涨)`);
      console.log(`   滑点损失: ${(slippageLoss * 100).toFixed(3)}%`);
      console.log(`   手续费: ${(tradingFee * 100).toFixed(1)}%`);
      console.log(`   总价格变化: ${(totalPriceChange * 100).toFixed(3)}%`);
      console.log(`   预估执行价格: ${estimatedExecutionPrice.toFixed(8)} SOL/token`);
      console.log(`   预期获得Token: ${estimatedTokensReceived.toFixed(6)} 个`);
      
      // 计算滑点保护的最小Token数量
      const { expectedTokens, minTokens } = this.calculateTokensWithSlippage(solAmount, poolData);
      
      // 创建买入指令
      const buyInstructions = await this.createBuyInstruction(poolData, wallet.publicKey);
      
      // 创建交易
      const messageV0 = new TransactionMessage({
          payerKey: wallet.publicKey,
        recentBlockhash: this.currentBlockhash,
        instructions: buyInstructions,
      }).compileToV0Message();

      const transaction = new VersionedTransaction(messageV0);
      transaction.sign([wallet]);
      
      // 🔥 添加到pending状态 - 在发送交易前立即添加
      const signature = Buffer.from(transaction.serialize()).toString('base64').slice(0, 32); // 临时签名
      instance.pendingTransactions.set(signature, {
        type: 'buy',
        timestamp: new Date(),
        signature: signature,
        confirmed: false
      });

      let actualSignature: string;
      
      // 🔥 根据USE_JITO配置选择发送方式
      if (USE_JITO) {
        const serializedTx = Buffer.from(transaction.serialize()).toString('base64');
        actualSignature = await this.sendToJito(serializedTx);
        console.log(`📤 买入交易已通过Jito发送: ${actualSignature}`);
      } else {
        // 直接通过RPC发送
        actualSignature = await connection.sendTransaction(transaction, {
          skipPreflight: true,
          maxRetries: 3
        });
        console.log(`📤 买入交易已通过RPC发送: ${actualSignature}`);
      }

      // 更新实际签名
      instance.pendingTransactions.delete(signature);
      instance.pendingTransactions.set(actualSignature, {
        type: 'buy',
        timestamp: new Date(),
        signature: actualSignature,
        confirmed: false
      });

      // 🔥 返回pending状态，不立即创建持仓记录
      console.log(`🚀 买入交易发送成功，等待异步确认...`);
      
      // 🔥 创建Promise来等待异步确认完成
      return new Promise<RealTradeResult>((resolve) => {
        // 🔥 添加总体超时机制 - 最多120秒后强制失败
        const TOTAL_TIMEOUT = 120000; // 120秒
        let resolved = false;
        
        const safeResolve = (result: RealTradeResult) => {
          if (!resolved) {
            resolved = true;
            clearTimeout(totalTimeout);
            resolve(result);
          }
        };
        
        const totalTimeout = setTimeout(() => {
          if (!resolved) {
            console.error(`❌ 买入交易确认总超时 (${TOTAL_TIMEOUT/1000}秒)，强制失败: ${actualSignature}`);
                  instance.pendingTransactions.delete(actualSignature);
                  safeResolve({
                    success: false,
                    error: `交易确认超时 (${TOTAL_TIMEOUT/1000}秒)`
                  });
          }
        }, TOTAL_TIMEOUT);
        
        // 🔥 异步处理交易确认，获取真实执行价格
        setTimeout(async () => {
          try {
            console.log(`⏳ 开始异步确认买入交易: ${actualSignature}`);
            const MAX_CONFIRMATION_RETRIES = 3;
            let confirmationAttempt = 0;
            let confirmed = false;
            
            while (confirmationAttempt < MAX_CONFIRMATION_RETRIES && !confirmed) {
              confirmationAttempt++;
              try {
                const confirmation = await queryConnection.confirmTransaction(actualSignature, 'confirmed');
                
                if (!confirmation.value.err) {
                  confirmed = true;
                  console.log(`✅ 买入交易异步确认成功: ${actualSignature}`);
                  
                  // 更新确认状态
                  const pendingTx = instance.pendingTransactions.get(actualSignature);
                  if (pendingTx) {
                    pendingTx.confirmed = true;
                  }
                  
                  // 🔥 添加1秒延迟等待链上数据同步
                  console.log(`⏱️ 等待2秒钟让链上数据同步...`);
                  await new Promise(resolve => setTimeout(resolve, 2000));
                  
                  // 🔥 查询真实持仓数量
                  const realBalance = await this.getRealTokenBalance(tokenAddress);
                  
                  // 🔥 获取确认后的真实交易详情
                  let actualTokensReceived = realBalance;
                  let actualSolSpent = solAmount;
                  let realExecutionPrice = estimatedExecutionPrice;
                  
                  try {
                    const transaction = await queryConnection.getTransaction(actualSignature, {
                      commitment: 'confirmed',
                      maxSupportedTransactionVersion: 0
                    });
                    
                    if (transaction) {
                      // 分析Token转账详情
                      const extractedTokens = this.extractTokenAmountFromTransaction(transaction, tokenAddress, wallet.publicKey);
                      const extractedSol = this.extractSolSpentFromTransaction(transaction, wallet.publicKey);
                      
                      if (extractedTokens > 0 && extractedSol > 0) {
                        actualTokensReceived = extractedTokens;
                        actualSolSpent = extractedSol;
                        
                        const gasCost = PRIORITY_FEE + (USE_JITO ? TIP_AMOUNT : 0);
                        const netSolSpent = actualSolSpent - gasCost;
                        
                        if (netSolSpent > 0) {
                          realExecutionPrice = netSolSpent / actualTokensReceived;
                        }
                      }
                    }
                  } catch (txError) {
                    console.log(`⚠️ 获取交易详情失败，使用查询到的真实持仓:`, txError);
                  }
                  
                  console.log(`📊 买入交易确认完成，真实数据:`);
                  console.log(`   实际获得Token: ${actualTokensReceived.toFixed(6)} 个`);
                  console.log(`   实际花费SOL: ${actualSolSpent.toFixed(6)} SOL`);
                  console.log(`   真实执行价格: ${realExecutionPrice.toFixed(8)} SOL/token`);
                  
                    // 🔥 创建持仓记录，使用真实数据
                    instance.currentHolding = {
                    amount: actualTokensReceived,
                      buyPrice: realExecutionPrice,
                      buyTime: new Date(),
                      buySolAmount: actualSolSpent,
                      buyGasFee: PRIORITY_FEE + (USE_JITO ? TIP_AMOUNT : 0),
                      buyPlatformFee: actualSolSpent * 0.003,
                      totalBuyCost: actualSolSpent + (PRIORITY_FEE + (USE_JITO ? TIP_AMOUNT : 0)),
                      bundleId: actualSignature
                    };
                    
                    // 更新统计
                  instance.stats.currentPosition = actualTokensReceived;
                    instance.stats.totalTrades++;
                    instance.lastTradeTime = new Date();
                    
                    // 🔥 记录到仓位管理器
                  this.positionManager.openPosition(tokenAddress, actualTokensReceived, realExecutionPrice);
                    
                    // 记录真实价格到历史数据
                  this.recordRealPrice(tokenAddress, actualSolSpent, actualTokensReceived, 'buy', 'own_trade');
                    
                    // 记录交易历史
                    instance.tradingHistory.push({
                      type: 'buy',
                      timestamp: new Date(),
                    tokenAmount: actualTokensReceived,
                      solAmount: actualSolSpent,
                      price: realExecutionPrice,
                      prediction: buyPrediction, // 固定值，避免作用域问题
                      bundleId: actualSignature
                    });
                    
                    // 🔥 计算真实滑点
                    let actualSlippage = 0.01; // 默认滑点
                    
                    // 如果有预期价格和实际价格，计算真实滑点
                    if (estimatedExecutionPrice > 0 && realExecutionPrice > 0) {
                      actualSlippage = Math.abs((realExecutionPrice - estimatedExecutionPrice) / estimatedExecutionPrice);
                      // 限制滑点显示范围在0-10%之间
                      actualSlippage = Math.min(Math.max(actualSlippage, 0), 0.1);
                    }
                    
                    console.log(`📊 真实滑点计算:`);
                    console.log(`   预期执行价格: ${estimatedExecutionPrice.toFixed(8)} SOL/token`);
                    console.log(`   实际执行价格: ${realExecutionPrice.toFixed(8)} SOL/token`);
                    console.log(`   计算滑点: ${(actualSlippage * 100).toFixed(2)}%`);
                    
                    // 🔥 立即发送Telegram通知 - 只使用真实链上数据
                    console.log(`📱 准备发送Telegram通知，最终数据:`);
                    console.log(`   Token数量: ${actualTokensReceived.toFixed(6)} 个`);
                    console.log(`   SOL花费: ${actualSolSpent.toFixed(6)} SOL`);
                    console.log(`   执行价格: ${realExecutionPrice.toFixed(8)} SOL/token`);
                    console.log(`   滑点: ${(actualSlippage * 100).toFixed(2)}%`);
                    
                    // 🔥 只使用真实获取的数据，不使用任何估算值
                    if (actualTokensReceived <= 0) {
                      console.log(`⚠️ 警告：未能从链上获取到真实Token数量，显示为0`);
                      actualTokensReceived = 0;
                    }
                      
                    // 🔥 移除即时买入通知 - 现在统一通过GRPC确认后发送通知
                    // 这样可以避免重复通知，并确保使用真实的交易数据
                    console.log(`⏳ 买入交易已确认，等待GRPC处理后发送通知...`);
                    
                    // 🔥 返回成功结果
                    safeResolve({
                      success: true,
                      bundleId: actualSignature,
                      actualPrice: realExecutionPrice,
                    actualAmount: actualTokensReceived,
                      gasFee: PRIORITY_FEE + (USE_JITO ? TIP_AMOUNT : 0)
                    });
                  
                } else {
                  console.error(`❌ 买入交易确认失败 (尝试${confirmationAttempt}):`, confirmation.value.err);
                }
              } catch (confirmError) {
                console.error(`❌ 买入确认尝试 ${confirmationAttempt} 失败:`, confirmError);
                
                if (confirmationAttempt === MAX_CONFIRMATION_RETRIES) {
                  console.log(`❌ 买入交易确认最终失败，清理pending状态`);
                  instance.pendingTransactions.delete(actualSignature);
                  
                  // 🔥 返回失败结果
                      safeResolve({
                        success: false,
                        error: '交易确认失败'
                      });
                } else {
                  await new Promise(resolve => setTimeout(resolve, 2000));
                }
              }
            }
            
          } catch (error) {
            console.error(`❌ 异步确认过程错误:`, error);
                instance.pendingTransactions.delete(actualSignature);
            
            // 🔥 返回失败结果
            safeResolve({
              success: false,
              error: error instanceof Error ? error.message : String(error)
            });
          }
        }, 0);
      });
      
    } catch (error) {
      console.error(`❌ 真实买入执行错误: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 🔥 修复：执行真实卖出交易 - 改进价格计算和确认机制
   */
  private async executeRealSell(tokenAddress: string, tokenAmount: number, sellPrediction: number): Promise<RealTradeResult> {
    try {
      // 🔥 安全检查：确保tokenAmount大于0
      if (tokenAmount <= 0) {
        console.error(`❌ 卖出数量不能为0或负数: ${tokenAmount}`);
        return {
          success: false,
          error: `卖出数量无效: ${tokenAmount}`
        };
      }
      
      console.log(`🔍 准备卖出 ${tokenAmount.toFixed(6)} 个Token...`);

      // 🔥 修复：尝试获取池子数据，如果没有就创建
      let poolData = this.globalPoolCache.get(tokenAddress);
      
      if (!poolData) {
        console.log(`🔧 卖出时没有缓存的池子数据，尝试重新创建...`);
        try {
          const tokenMint = new PublicKey(tokenAddress);
          poolData = await this.createPoolDataFromCache(tokenMint);
          // 更新全局缓存
          this.globalPoolCache.set(tokenAddress, poolData);
        } catch (error) {
          console.error(`❌ 卖出时创建池子数据失败:`, error);
          return {
            success: false,
            error: '无法获取池子数据进行卖出'
          };
        }
      }

      // 使用全局钱包变量
      const actualTokenAmount = tokenAmount;
      const tokenAmountBigInt = BigInt(Math.floor(actualTokenAmount * (10 ** 6)));

      // 💨 获取基础价格（基于池子比率）
      const basePrice = poolData.solBalance / poolData.tokenBalance;
      const baseSolAmount = actualTokenAmount * basePrice;
      
      // 🔥 新价格计算逻辑：移除市场波动，添加手续费
      // 大额交易会对价格产生更大影响，小额交易影响较小
      const poolTotalValue = poolData.solBalance + (poolData.tokenBalance * basePrice);
      const tradeImpactRatio = baseSolAmount / poolTotalValue;
      
      // 价格影响：大额交易会导致价格下滑
      const priceImpact = Math.min(tradeImpactRatio * 0.5, 0.02); // 最大2%价格影响
      
      // 滑点损失：卖出时通常价格会更低
      const slippageLoss = Math.random() * 0.01; // 0% 到 1%滑点损失
      
      // 手续费：0.3%
      const tradingFee = 0.003; // 0.3%
      
      // 计算真实执行价格（卖出时价格下跌）
      const totalPriceChange = priceImpact + slippageLoss + tradingFee;
      const estimatedExecutionPrice = basePrice * (1 - totalPriceChange);
      const estimatedSolReceived = actualTokenAmount * estimatedExecutionPrice;
      
      console.log(`💰 卖出价格计算详情:`);
      console.log(`   池子基础价格: ${basePrice.toFixed(8)} SOL/token`);
      console.log(`   价格影响: ${(priceImpact * 100).toFixed(3)}% (下跌)`);
      console.log(`   滑点损失: ${(slippageLoss * 100).toFixed(3)}%`);
      console.log(`   手续费: ${(tradingFee * 100).toFixed(1)}%`);
      console.log(`   总价格变化: ${(totalPriceChange * 100).toFixed(3)}%`);
      console.log(`   预估执行价格: ${estimatedExecutionPrice.toFixed(8)} SOL/token`);
      console.log(`   预期收到SOL: ${estimatedSolReceived.toFixed(6)} SOL`);

      // 创建卖出指令
      const sellInstructions = await this.createSellInstruction(poolData, wallet.publicKey, tokenAmountBigInt);

      // 创建交易
      const messageV0 = new TransactionMessage({
        payerKey: wallet.publicKey,
        recentBlockhash: this.currentBlockhash,
        instructions: sellInstructions,
      }).compileToV0Message();

      const transaction = new VersionedTransaction(messageV0);
      transaction.sign([wallet]);

      let signature: string;
      
      // 🔥 根据USE_JITO配置选择发送方式
      if (USE_JITO) {
        const serializedTx = Buffer.from(transaction.serialize()).toString('base64');
        signature = await this.sendToJito(serializedTx);
        console.log(`📤 卖出交易已通过Jito发送: ${signature}`);
      } else {
        // 直接通过RPC发送
        signature = await connection.sendTransaction(transaction, {
          skipPreflight: true,
          maxRetries: 3
        });
        console.log(`📤 卖出交易已通过RPC发送: ${signature}`);
      }

      // 🔥 获取token实例并添加pending记录
      const instance = this.tokenInstances.get(tokenAddress);
      if (instance) {
        instance.pendingTransactions.set(signature, {
          type: 'sell',
          timestamp: new Date(),
          signature: signature,
          confirmed: false
        });
      }

      // 🔥 返回Promise等待异步确认完成
      console.log(`🚀 卖出交易发送成功，等待异步确认...`);
      
      // 🔥 创建Promise来等待异步确认完成
      return new Promise((resolve) => {
        // 🔥 异步处理交易确认，获取真实执行价格和更新仓位
        setTimeout(async () => {
          try {
            console.log(`⏳ 开始异步确认卖出交易: ${signature}`);
            const MAX_CONFIRMATION_RETRIES = 3;
            let confirmationAttempt = 0;
            let confirmed = false;
            
            while (confirmationAttempt < MAX_CONFIRMATION_RETRIES && !confirmed) {
              confirmationAttempt++;
              try {
                const confirmation = await queryConnection.confirmTransaction(signature, 'confirmed');
                
                if (!confirmation.value.err) {
                  confirmed = true;
                  console.log(`✅ 卖出交易异步确认成功: ${signature}`);
                  
                  let actualSolReceived: number = 0; 
                  let realExecutionPrice: number = 0;  
                  let actualTokensSold: number = actualTokenAmount; 
                  let transactionDetailsFound = false;
                  
                  try {
                    const transaction = await queryConnection.getTransaction(signature, {
                      commitment: 'confirmed',
                      maxSupportedTransactionVersion: 0
                    });
                    
                    if (transaction) {
                      transactionDetailsFound = true;
                      console.log(`🔍 分析卖出交易详情: ${signature}`);
                      
                      const extractedSol = this.extractSolReceivedFromSellTransaction(transaction, wallet.publicKey);
                      const extractedTokens = this.extractTokenAmountFromTransaction(transaction, tokenAddress, wallet.publicKey);
                      
                      console.log(`📊 交易数据提取结果 (原始预估SOL: ${estimatedSolReceived.toFixed(6)}):`);
                      console.log(`   从交易中提取SOL: ${extractedSol.toFixed(6)} SOL`);
                      console.log(`   从交易中提取Tokens: ${extractedTokens.toFixed(6)} tokens`);

                      if (extractedSol > 0.0000001) { // Use small threshold
                        actualSolReceived = extractedSol;
                        console.log(`✅ 使用从交易中提取的真实SOL收入: ${actualSolReceived.toFixed(6)} SOL`);
                      } else {
                        console.error(`🆘 未能从交易 ${signature} 中提取到有效的正数SOL收入 (提取到: ${extractedSol}). 实际SOL收入将记为0.`);
                        actualSolReceived = 0; // Explicitly set to 0 if not extracted positively
                      }
                      
                      if (extractedTokens > 0.0000001) { // Use small threshold
                        actualTokensSold = extractedTokens;
                        console.log(`✅ 使用从交易中提取的真实Token卖出量: ${actualTokensSold.toFixed(6)} tokens`);
                      } else {
                        console.warn(`⚠️ 未能从交易 ${signature} 中提取到有效的Token卖出量 (提取到: ${extractedTokens}), 将使用预估卖出量: ${actualTokenAmount.toFixed(6)}`);
                        actualTokensSold = actualTokenAmount; 
                      }
                      
                      if (actualSolReceived > 0.0000001 && actualTokensSold > 0.0000001) {
                        realExecutionPrice = actualSolReceived / actualTokensSold;
                        console.log(`✅ 根据提取/确认的数据计算的真实执行价格: ${realExecutionPrice.toFixed(8)} SOL/token`);
                      } else {
                        console.error(`❌ 无法计算真实执行价格: 实际SOL收入 (${actualSolReceived.toFixed(8)}) 或实际Token卖出 (${actualTokensSold.toFixed(8)}) 无效.`);
                        console.log(`   由于无法确认有效收入/卖出量，真实执行价格将基于预估价格: ${estimatedExecutionPrice.toFixed(8)}`);
                        realExecutionPrice = estimatedExecutionPrice; 
                      }
                    } else {
                      console.error(`🆘 无法获取交易详情 (${signature}). 实际SOL收入和真实执行价格将无法确认，记为0或预估.`);
                      actualSolReceived = 0; // Cannot determine, so 0
                      realExecutionPrice = estimatedExecutionPrice; // No basis for real price, use estimate as placeholder
                      actualTokensSold = actualTokenAmount;
                    }
                  } catch (txError) {
                    console.error(`❌ 获取或解析交易详情失败 (${signature}):`, txError);
                    console.error(`🆘 由于获取交易详情失败，实际SOL收入和真实执行价格将无法确认，记为0或预估.`);
                    actualSolReceived = 0; // Cannot determine, so 0
                    realExecutionPrice = estimatedExecutionPrice; // No basis for real price, use estimate as placeholder
                    actualTokensSold = actualTokenAmount;
                  }
                  
                  console.log(`📊 卖出交易确认完成，最终使用数据 (NO FALLBACK FOR SOL RECEIVED):`);
                  console.log(`   卖出数量: ${actualTokensSold.toFixed(6)} 个Token`);
                  console.log(`   实际收到SOL: ${actualSolReceived.toFixed(6)} SOL`);
                  console.log(`   真实执行价格: ${realExecutionPrice.toFixed(8)} SOL/token`);
                  
                  // 🔥 添加1秒延迟等待链上数据同步，然后验证余额
                  console.log(`⏱️ 等待1秒钟让链上数据同步...`);
                  await new Promise(resolve => setTimeout(resolve, 1000));
                  
                  // 🔥 验证卖出后的余额
                  const remainingBalance = await this.getRealTokenBalance(tokenAddress);
                  console.log(`🔍 卖出后剩余余额: ${remainingBalance.toFixed(6)} 个Token`);
                  
                  // 如果还有剩余余额，说明没有完全卖出
                  if (remainingBalance > 0.001) {
                    console.log(`⚠️ 警告：卖出后仍有${remainingBalance.toFixed(6)}个Token剩余`);
                  }
                  
                  // 🔥 获取持仓实例并更新仓位
                  const sellInstance = this.tokenInstances.get(tokenAddress);
                  if (sellInstance && sellInstance.currentHolding) {
                    // 计算收益
                    const profit = actualSolReceived - sellInstance.currentHolding.buySolAmount;
                    const profitPercentage = ((actualSolReceived / sellInstance.currentHolding.buySolAmount - 1) * 100);
                    const holdingTimeMs = Date.now() - sellInstance.currentHolding.buyTime.getTime();
                    
                    console.log(`💰 收益计算:`);
                    console.log(`   净收益: ${profit > 0 ? '+' : ''}${profit.toFixed(6)} SOL (${profitPercentage > 0 ? '+' : ''}${profitPercentage.toFixed(2)}%)`);
                    console.log(`   持仓时间: ${Math.floor(holdingTimeMs / 60000)}分${Math.floor((holdingTimeMs % 60000) / 1000)}秒`);
                    
                    // 🔥 记录到风险管理器
                    this.riskManager.recordTrade(profit, TIP_AMOUNT + PRIORITY_FEE);
                    
                    // 🔥 记录到仓位管理器
                    this.positionManager.closePosition(tokenAddress, realExecutionPrice, profit);
                    
                    // 记录交易历史
                    sellInstance.tradingHistory.push({
                      type: 'sell',
                      timestamp: new Date(),
                      tokenAmount: actualTokensSold,
                      solAmount: actualSolReceived,
                      price: realExecutionPrice,
                      prediction: sellPrediction, // 固定值，避免作用域问题
                      bundleId: signature
                    });
                    
                    // 🔥 清空持仓记录
                    const originalHolding = { ...sellInstance.currentHolding }; // 保存原始持仓信息用于通知
                    sellInstance.currentHolding = null;
                    sellInstance.stats.currentPosition = 0;
                    sellInstance.stats.successfulTrades++;
                    sellInstance.stats.totalPnL += profit;
                    sellInstance.lastTradeTime = new Date();
                    sellInstance.lastConfirmedSellTime = new Date();
                    
                    // 🔥 计算真实滑点
                    let actualSlippage = 0.01; // 默认滑点
                    
                    // 如果有预期价格和实际价格，计算真实滑点
                    if (estimatedExecutionPrice > 0 && realExecutionPrice > 0) {
                      actualSlippage = Math.abs((realExecutionPrice - estimatedExecutionPrice) / estimatedExecutionPrice);
                      // 限制滑点显示范围在0-10%之间
                      actualSlippage = Math.min(Math.max(actualSlippage, 0), 0.1);
                    }
                    
                    console.log(`📊 卖出真实滑点计算:`);
                    console.log(`   预期执行价格: ${estimatedExecutionPrice.toFixed(8)} SOL/token`);
                    console.log(`   实际执行价格: ${realExecutionPrice.toFixed(8)} SOL/token`);
                    console.log(`   计算滑点: ${(actualSlippage * 100).toFixed(2)}%`);
                    
                    // 🔥 移除即时卖出通知 - 现在统一通过GRPC确认后发送通知
                    // 这样可以避免重复通知，并确保使用真实的交易数据
                    console.log(`⏳ 卖出交易已确认，等待GRPC处理后发送通知...`);
                    
                    // 🔥 返回成功结果
                    resolve({
                      success: true,
                      bundleId: signature,
                      actualPrice: realExecutionPrice,
                      actualAmount: actualSolReceived,
                      gasFee: PRIORITY_FEE + (USE_JITO ? TIP_AMOUNT : 0)
                    });
                    
                  } else {
                    console.error(`❌ 未找到持仓实例或持仓记录`);
                    resolve({
                      success: false,
                      error: '未找到持仓记录'
                    });
                  }
                  
                } else {
                  console.error(`❌ 卖出交易确认失败 (尝试${confirmationAttempt}):`, confirmation.value.err);
                }
              } catch (confirmError) {
                console.error(`❌ 卖出确认尝试 ${confirmationAttempt} 失败:`, confirmError);
                
                if (confirmationAttempt === MAX_CONFIRMATION_RETRIES) {
                  console.log(`❌ 卖出交易确认最终失败，保持当前仓位不变`);
                  
                  // 🔥 返回失败结果
                  resolve({
                    success: false,
                    error: '交易确认失败'
                  });
                } else {
                  await new Promise(resolve => setTimeout(resolve, 2000));
                }
              }
            }
            
          } catch (error) {
            console.error(`❌ 异步确认过程错误:`, error);
            
            // 🔥 返回失败结果
            resolve({
              success: false,
              error: error instanceof Error ? error.message : String(error)
            });
          }
        }, 0);
      });
      
    } catch (error) {
      console.error(`❌ 真实卖出执行错误: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // =============================================================================
  // TradingDataProvider 接口实现
  // =============================================================================

  /**
   * 获取当前活跃的Token列表
   */
  public getActiveTokens(): string[] {
    return this.subscriptionManager.getActiveTokens();
  }

  /**
   * 获取每个Token的交易统计
   */
  public getTokenStats(): Array<{
    tokenAddress: string;
    totalTrades: number;
    totalPnL: number;
    transactionCount: number;
    winRate: number;
  }> {
    const tokenStats: Array<{
      tokenAddress: string;
      totalTrades: number;
      totalPnL: number;
      transactionCount: number;
      winRate: number;
    }> = [];

    for (const [tokenAddress, instance] of this.tokenInstances) {
      const sellTrades = instance.tradingHistory.filter(trade => trade.type === 'sell');
      const winningTrades = sellTrades.filter(trade => {
        const buyTrade = instance.tradingHistory
          .reverse()
          .find(t => t.type === 'buy' && t.timestamp < trade.timestamp);
        if (buyTrade) {
          return trade.solAmount > buyTrade.solAmount;
        }
        return false;
      });

      const winRate = sellTrades.length > 0 ? winningTrades.length / sellTrades.length : 0;
      const tokenTransactions = this.subscriptionManager.getWalletTransactionHistory(24)
        .filter(tx => tx.tokenAddress === tokenAddress);

      tokenStats.push({
        tokenAddress,
        totalTrades: instance.stats.totalTrades,
        totalPnL: instance.stats.totalPnL,
        transactionCount: tokenTransactions.length,
        winRate
      });
    }

    return tokenStats;
  }

  /**
   * 获取当前所有持仓信息
   */
  public getCurrentPositions(): Array<{
    tokenAddress: string;
    position: number;
    pnl: number;
    buyPrice: number;
    currentPrice: number;
    holdingTime: number;
  }> {
    const positions: Array<{
      tokenAddress: string;
      position: number;
      pnl: number;
      buyPrice: number;
      currentPrice: number;
      holdingTime: number;
    }> = [];

    for (const [tokenAddress, instance] of this.tokenInstances) {
      if (instance.currentHolding && instance.currentHolding.amount > 0) {
        // 🔥 修复：使用真实的当前价格而不是简化假设
        const currentPrice = this.getLastTradePrice(tokenAddress) || instance.currentHolding.buyPrice;
        const currentValue = instance.currentHolding.amount * currentPrice;
        const unrealizedPnL = currentValue - instance.currentHolding.buySolAmount;
        const holdingTimeMs = Date.now() - instance.currentHolding.buyTime.getTime();
        const holdingTimeMinutes = Math.floor(holdingTimeMs / (1000 * 60));

        positions.push({
          tokenAddress,
          position: instance.currentHolding.amount,
          pnl: unrealizedPnL,
          buyPrice: instance.currentHolding.buyPrice,
          currentPrice,
          holdingTime: holdingTimeMinutes
        });
      }
    }

    return positions;
  }

  /**
   * 🔥 获取卖出缓冲队列信息
   */
  public getSellOnlyBufferInfo(): Array<{
    tokenAddress: string;
    reason: string;
    addedTime: Date;
    bufferDurationMinutes: number;
    hasPosition: boolean;
  }> {
    return Array.from(this.sellOnlyBufferQueue.entries()).map(([tokenAddress, info]) => {
      const instance = this.tokenInstances.get(tokenAddress);
      const hasPosition = instance?.currentHolding?.amount && instance.currentHolding.amount > 0;
      const bufferDurationMinutes = (Date.now() - info.addedTime.getTime()) / (1000 * 60);
      
      return {
        tokenAddress,
        reason: info.reason,
        addedTime: info.addedTime,
        bufferDurationMinutes,
        hasPosition: Boolean(hasPosition)
      };
    });
  }

  /**
   * 启动系统
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ Real Trading系统已在运行');
      return;
    }

    this.isRunning = true;
    
    // 🔥 新增：测试Telegram连接
    console.log('📱 =====================================');
    console.log('📱 测试Telegram连接...');
    
    try {
      const telegramConnected = await this.telegramNotifier.testConnection();
      
      if (telegramConnected) {
        console.log('✅ Telegram连接成功！开始发送启动通知...');
        await this.telegramNotifier.notifySystem(
          `🚀 Real Trading系统启动\n💰 真实交易模式: ✅\n🎯 目标钱包: ${TARGET_WALLET.slice(0, 8)}...\n💼 每笔交易: ${this.tradingConfig.tradeAmountSol} SOL\n🤖 AI阈值: 买入≥${(this.tradingConfig.buyThreshold*100).toFixed(1)}% | 卖出≥${(this.tradingConfig.sellThreshold*100).toFixed(1)}%`
        );
        console.log('✅ Telegram启动通知已发送');
      } else {
        console.log('❌ Telegram连接失败，将跳过所有通知');
      }
    } catch (error) {
      console.error('❌ Telegram连接测试出错:', error);
    }
    
    console.log('📱 =====================================\n');

    // 启动Telegram Webhook服务器
    console.log('📱 启动Telegram Webhook服务器...');
    await this.telegramNotifier.startWebhook();
    
    // 🔥 启动核心组件
    this.subscriptionManager = new DynamicSubscriptionManager(TARGET_WALLET);
    this.subscriptionManager.start();
    
    // 🔥 新增：启动时检查并创建持仓token实例
    const activePositions = this.positionManager.getActivePositions();
    if (activePositions.length > 0) {
      console.log(`🔍 启动时发现 ${activePositions.length} 个持仓，正在创建对应实例...`);
      for (const position of activePositions) {
        try {
          const tokenDisplay = await this.formatTokenDisplay(position.tokenAddress);
          let instance = this.tokenInstances.get(position.tokenAddress);
          
          if (!instance) {
            console.log(`🆕 为持仓token创建实例: ${tokenDisplay}`);
            instance = await this.createTokenInstance(position.tokenAddress);
            this.tokenInstances.set(position.tokenAddress, instance);
            await this.initializeTokenPredictorData(position.tokenAddress, instance);
          }
          
          // 确保实例激活
          instance.isActive = true;
          
          // 🔥 新增：同步实例的currentHolding信息
          if (!instance.currentHolding && position.amount > 0) {
            console.log(`🔄 同步持仓信息到实例: ${tokenDisplay}`);
            instance.currentHolding = {
              amount: position.amount,
              buyPrice: position.entryPrice,
              buyTime: position.timestamp,
              buySolAmount: position.amount * position.entryPrice,
              buyGasFee: 0, // 历史持仓无法确定gas费用
              buyPlatformFee: position.amount * position.entryPrice * 0.003,
              totalBuyCost: position.amount * position.entryPrice,
              bundleId: `historical_${position.id}`
            };
            
            // 更新统计信息
            instance.stats.currentPosition = position.amount;
            console.log(`✅ 已同步持仓信息: ${position.amount.toFixed(6)} tokens @ ${position.entryPrice.toFixed(8)} SOL/token`);
          }
          
          console.log(`✅ 持仓token实例已就绪: ${tokenDisplay} (数量: ${position.amount.toFixed(6)})`);
        } catch (error) {
          console.error(`❌ 创建持仓token实例失败 ${position.tokenAddress.slice(0, 8)}...:`, error);
        }
      }
    }
    
    // 🔥 新增：反向检查 - 检查实例中有持仓但PositionManager中没有记录的情况
    console.log(`🔍 检查实例持仓与PositionManager的同步状态...`);
    for (const [tokenAddress, instance] of this.tokenInstances) {
      if (instance.currentHolding && instance.currentHolding.amount > 0) {
        const managerPosition = this.positionManager.getPosition(tokenAddress);
        if (!managerPosition) {
          const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
          console.log(`🔄 发现实例持仓但PositionManager无记录，正在同步: ${tokenDisplay}`);
          try {
            const position = this.positionManager.openPosition(
              tokenAddress,
              instance.currentHolding.amount,
              instance.currentHolding.buyPrice
            );
            console.log(`✅ 已同步到PositionManager: ${position.id} (${instance.currentHolding.amount.toFixed(6)} tokens)`);
          } catch (error) {
            console.error(`❌ 同步到PositionManager失败:`, error);
          }
        }
      }
    }
    
    // 🔥 新增：定期检查持仓token订阅状态
    const positionSubscriptionTimer = setInterval(async () => {
      try {
        const activePositions = this.positionManager.getActivePositions();
        console.log(`🔍 定期检查持仓token订阅状态 (${activePositions.length}个持仓)`);
        
        for (const position of activePositions) {
          const isSubscribed = this.currentTokenSubscriptions.has(position.tokenAddress);
          const isInDynamic = this.subscriptionManager.getActiveTokens().includes(position.tokenAddress);
          const instance = this.tokenInstances.get(position.tokenAddress);
          const tokenDisplay = await this.formatTokenDisplay(position.tokenAddress);
          
          console.log(`📊 ${tokenDisplay}:`);
          console.log(`   💼 持仓: ${position.amount.toFixed(6)} tokens`);
          console.log(`   📺 已订阅: ${isSubscribed ? '✅' : '❌'}`);
          console.log(`   🔄 动态监控: ${isInDynamic ? '✅' : '❌'}`);
          console.log(`   🤖 实例存在: ${instance ? '✅' : '❌'}`);
          console.log(`   🎯 实例活跃: ${instance?.isActive ? '✅' : '❌'}`);
          
          // 如果持仓token没有被订阅，强制触发订阅更新
          if (!isSubscribed) {
            console.warn(`⚠️ 持仓token未被订阅，强制更新订阅...`);
            await this.updateTokenSubscriptions();
          }
          
          // 如果实例不存在或不活跃，重新激活
          if (!instance || !instance.isActive) {
            console.warn(`⚠️ 持仓token实例状态异常，重新激活...`);
            try {
              let newInstance = instance;
              if (!newInstance) {
                newInstance = await this.createTokenInstance(position.tokenAddress);
                this.tokenInstances.set(position.tokenAddress, newInstance);
              }
              newInstance.isActive = true;
              console.log(`✅ 已重新激活token实例: ${tokenDisplay}`);
            } catch (error) {
              console.error(`❌ 重新激活实例失败:`, error);
            }
          }
        }
        
        // 检查是否有空订阅（订阅了但没有持仓的token）
        const subscribedButNoPosition = Array.from(this.currentTokenSubscriptions).filter(
          token => token !== TARGET_WALLET && !activePositions.some(pos => pos.tokenAddress === token)
        );
        
        if (subscribedButNoPosition.length > 0) {
          console.log(`🧹 发现${subscribedButNoPosition.length}个无持仓的订阅token，检查是否需要清理`);
        }
        
      } catch (error) {
        console.error(`❌ 定期检查持仓订阅状态失败:`, error);
      }
    }, 120000); // 每2分钟检查一次
    
    // 将timer保存以便清理
    if (!this.subscriptionUpdateTimer) {
      this.subscriptionUpdateTimer = positionSubscriptionTimer;
    }

    // 启动状态监控
    this.startStatusMonitoring();
    
    console.log('🚀 开始连接到Yellowstone gRPC流...');
    console.log(`🎯 目标钱包: ${TARGET_WALLET}`);
    
    // 初始化blockhash更新
    this.blockhashUpdateTimer = setInterval(() => {
      this.updateBlockhash().catch(error => {
        console.error('更新blockhash失败:', error);
      });
    }, 30000); // 每30秒更新一次
    await this.updateBlockhash();
    
    // 创建GRPC客户端
    this.grpcClient = new Client(REAL_TRADING_CONFIG.grpc?.endpoint, REAL_TRADING_CONFIG.grpc?.token, undefined);
    
    // 启动定期更新订阅
    this.subscriptionUpdateTimer = setInterval(() => {
      this.updateTokenList();
      this.printStatus();
    }, UPDATE_INTERVAL_MS);

    // 启动缓冲队列管理定时器
    setInterval(() => {
      this.manageSellOnlyBufferQueue();
    }, 120000);

    // 开始处理流数据
    await this.handleStream(this.grpcClient, this.createInitialSubscribeRequest());
  }

  /**
   * 创建初始订阅请求
   */
  private createInitialSubscribeRequest(): SubscribeRequest {
    return {
      accounts: {},
      slots: {},
      transactions: {
        pumpFun: {
          vote: false,
          failed: false,
          signature: undefined,
          accountInclude: [TARGET_WALLET],
          accountExclude: [],
          accountRequired: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()],
        },
      },
      transactionsStatus: {},
      entry: {},
      blocks: {},
      blocksMeta: {},
      accountsDataSlice: [],
      ping: undefined,
      commitment: CommitmentLevel.PROCESSED,
    };
  }

  /**
   * 更新token列表
   */
  private updateTokenList(): void {
    const now = new Date();
    const windowMs = WINDOW_HOURS * 60 * 60 * 1000;
    let removedCount = 0;

    for (const [tokenAddress, activity] of this.tokenActivities) {
      if (now.getTime() - activity.lastSeen.getTime() > windowMs) {
        this.tokenActivities.delete(tokenAddress);
        this.currentTokenSubscriptions.delete(tokenAddress);
        
        const instance = this.tokenInstances.get(tokenAddress);
        if (instance) {
          instance.isActive = false;
          this.tokenInstances.delete(tokenAddress);
        }
        
        removedCount++;
      }
    }

    if (removedCount > 0) {
      console.log(`\n🗑️ 移除了 ${removedCount} 个过期token`);
      this.stats.subscriptionUpdates++;
    }
  }

  /**
   * 打印状态信息
   */
  private printStatus(): void {
    const activeTokens = this.subscriptionManager.getActiveTokens();
    
    // 🔥 修复：计算自己的统计数据，不依赖TradingBot
    let totalBalance = 0;
    let activePositions = 0;
    let totalPnL = 0;
    let totalTrades = 0;
    
    for (const [tokenAddress, instance] of this.tokenInstances) {
      if (instance.currentHolding && instance.currentHolding.amount > 0) {
        activePositions++;
        // 🔥 修复：使用真实的当前价值而不是买入成本
        const currentPrice = this.getLastTradePrice(tokenAddress) || instance.currentHolding.buyPrice;
        const currentValue = instance.currentHolding.amount * currentPrice;
        totalBalance += currentValue;
      }
      totalPnL += instance.stats.totalPnL;
      totalTrades += instance.stats.totalTrades;
    }
    
    console.log(`\n📊 系统状态 [${new Date().toLocaleTimeString()}]:`);
    console.log(`   🎯 目标钱包: ${TARGET_WALLET.slice(0, 12)}...`);
    console.log(`   💼 交易钱包: ${wallet.publicKey.toBase58().slice(0, 12)}...`);
    console.log(`   🪙 活跃Token: ${activeTokens.length} 个`);
    console.log(`   🔄 Blockhash: ${this.currentBlockhash.slice(0, 8)}... (${Math.floor((Date.now() - this.lastBlockhashUpdate.getTime()) / 1000)}秒前)`);
    console.log(`   💰 活跃仓位: ${activePositions}/${this.tradingConfig.maxPositions} | 总交易: ${totalTrades} 笔`);
    console.log(`   📈 总盈亏: ${totalPnL.toFixed(4)} SOL`);
    console.log(`   📋 监控队列: ${this.tokenMonitoringQueue.length}/${MAX_MONITORED_TOKENS} 个token`);
    console.log(`   🔒 仓位限制: ${activePositions >= this.tradingConfig.maxPositions ? '已达上限，停止新监控' : '可接受新token'}`);
    
    // 🔥 新增：输出详细持仓情况
    this.printCurrentPositions();
  }

  /**
   * 停止系统
   */
  public async stop(): Promise<void> {
    this.isRunning = false;
    
    console.log('🛑 停止Real Trading系统...');
    
    // 停止Blockhash更新定时器
    if (this.blockhashUpdateTimer) {
      clearInterval(this.blockhashUpdateTimer);
      console.log('🔄 Blockhash更新定时器已停止');
    }
    
    // 🔥 停止状态监控
    if (this.statusMonitorTimer) {
      clearInterval(this.statusMonitorTimer);
      console.log('📊 状态监控定时器已停止');
    }
    
    // 清理所有持仓
    await this.liquidateAllPositions();
    
    if (this.subscriptionUpdateTimer) {
      clearInterval(this.subscriptionUpdateTimer);
    }
    if (this.currentStream) {
      this.currentStream.end();
    }
    
    // 发送最终统计
    const finalStats = this.getFinalStats();
    await this.telegramNotifier.notifyDailyStats(finalStats);
    
    // 停止子系统
    // 🔥 修复：不调用TradingBot.stop()，因为已禁用
    // await this.tradingBot.stop();
    this.subscriptionManager.stop();
    await this.telegramNotifier.stopWebhook();
    
    // 清理所有token实例
    for (const instance of this.tokenInstances.values()) {
      instance.isActive = false;
    }
    
    console.log('🛑 真实交易管理器已停止');
  }

  /**
   * 清理所有持仓
   */
  private async liquidateAllPositions(): Promise<void> {
    console.log('💼 正在清理所有持仓...');
    
    let totalPositions = 0;
    let successfulLiquidations = 0;
    let totalPnL = 0;

    for (const [tokenAddress, instance] of this.tokenInstances) {
      if (instance.currentHolding && instance.currentHolding.amount > 0) {
        totalPositions++;
        console.log(`💸 清仓Token: ${tokenAddress.slice(0, 8)}... (持仓: ${instance.currentHolding.amount.toFixed(2)})`);
        
        try {
          const result = await this.executeRealSell(tokenAddress, instance.currentHolding.amount, 0);
          
          if (result.success && result.actualAmount > 0 && result.actualPrice > 0) {
            const solReceived = result.actualAmount || 0;
            const pnl = solReceived - instance.currentHolding.buySolAmount;
            
            totalPnL += pnl;
            successfulLiquidations++;
            
            console.log(`✅ 清仓成功: ${solReceived.toFixed(4)} SOL (盈亏: ${pnl > 0 ? '+' : ''}${pnl.toFixed(4)} SOL)`);
            
            // 发送Telegram清仓通知
            await this.telegramNotifier.notifyLiquidation(
              tokenAddress,
              '系统停止清仓',
              instance.currentHolding.amount,
              solReceived,
              pnl
            );
            
            // 更新状态
            instance.stats.currentPosition = 0;
            instance.stats.totalTrades++;
            instance.stats.totalPnL += pnl;
            
            if (pnl > 0) {
              instance.stats.successfulTrades++;
            }
            
            // 清空持仓
            instance.currentHolding = null;
            
          } else {
            console.log(`❌ 清仓失败: ${result.error}`);
          }
          
        } catch (error) {
          console.error(`❌ 清仓错误 ${tokenAddress.slice(0, 8)}...:`, error);
        }
      }
    }

    if (totalPositions > 0) {
      console.log(`\n📊 清仓完成:`);
      console.log(`   💼 总持仓数: ${totalPositions}`);
      console.log(`   ✅ 成功清仓: ${successfulLiquidations}`);
      console.log(`   💰 清仓总盈亏: ${totalPnL > 0 ? '+' : ''}${totalPnL.toFixed(4)} SOL`);
      
      await this.telegramNotifier.notifySystem(
        `🛑 系统停止 - 最终清仓统计\n💼 总持仓: ${totalPositions}\n✅ 成功清仓: ${successfulLiquidations}\n💰 总盈亏: ${totalPnL > 0 ? '+' : ''}${totalPnL.toFixed(4)} SOL`
      );
    } else {
      console.log('✅ 无持仓需要清理');
    }
  }

  /**
   * 获取最终统计数据
   */
  private getFinalStats() {
    let totalOperations = 0;
    let completedTradePairs = 0;
    let winningTrades = 0;
    let losingTrades = 0;
    let totalProfit = 0;
    
    for (const [tokenAddress, instance] of this.tokenInstances) {
      const tokenTotalOps = instance.tradingHistory.length;
      totalOperations += tokenTotalOps;
      totalProfit += instance.stats.totalPnL;
      
      const buyTrades = instance.tradingHistory.filter(trade => trade.type === 'buy');
      const sellTrades = instance.tradingHistory.filter(trade => trade.type === 'sell');
      
      for (const sellTrade of sellTrades) {
        const correspondingBuyTrade = buyTrades
          .filter(buyTrade => buyTrade.timestamp < sellTrade.timestamp)
          .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];
        
        if (correspondingBuyTrade) {
          completedTradePairs++;
          const tradeProfit = sellTrade.solAmount - correspondingBuyTrade.solAmount;
          
          if (tradeProfit > 0) {
            winningTrades++;
          } else if (tradeProfit < 0) {
            losingTrades++;
          }
        }
      }
    }
    
    const winRate = completedTradePairs > 0 ? (winningTrades / completedTradePairs) * 100 : 0;
    const activeTokens = this.tokenInstances.size;
    
    return {
      totalTrades: completedTradePairs,
      totalOperations,
      winningTrades,
      losingTrades,
      totalProfit,
      winRate,
      activeTokens
    };
  }

  /**
   * 管理缓冲队列
   */
  private async manageSellOnlyBufferQueue(): Promise<void> {
    if (this.sellOnlyBufferQueue.size === 0) return;

    console.log(`🔍 检查缓冲队列状态 (${this.sellOnlyBufferQueue.size} 个待清仓token)`);
    
    for (const [tokenAddress, bufferInfo] of this.sellOnlyBufferQueue.entries()) {
      const instance = this.tokenInstances.get(tokenAddress);
      const hasPosition = instance && instance.currentHolding && instance.currentHolding.amount > 0;
      
      if (!hasPosition) {
        console.log(`✅ Token ${tokenAddress.slice(0, 8)}... 持仓已清空，完全移除`);
        await this.completelyRemoveToken(tokenAddress, '缓冲队列-持仓已清空');
      } else {
        bufferInfo.lastCheckTime = new Date();
        console.log(`⏳ Token ${tokenAddress.slice(0, 8)}... 仍有持仓，继续等待卖出`);
      }
    }
  }

  /**
   * 完全移除Token
   */
  private async completelyRemoveToken(tokenAddress: string, reason: string): Promise<void> {
    console.log(`🗑️ 完全移除Token: ${tokenAddress.slice(0, 8)}... (${reason})`);
    
    const instance = this.tokenInstances.get(tokenAddress);
    if (instance) {
      instance.isActive = false;
      this.tokenInstances.delete(tokenAddress);
    }
    
    this.currentTokenSubscriptions.delete(tokenAddress);
    
    const queueIndex = this.tokenMonitoringQueue.findIndex(item => item.address === tokenAddress);
    if (queueIndex !== -1) {
      this.tokenMonitoringQueue.splice(queueIndex, 1);
    }
    
    this.sellOnlyBufferQueue.delete(tokenAddress);
    
    console.log(`✅ Token ${tokenAddress.slice(0, 8)}... 已完全移除`);
  }

  /**
   * 🔥 新增：清理过期token（价格数据过期的token）
   * 如果有持仓，先尝试卖出；清除所有相关历史数据；从监控列表中移除
   */
  private async cleanupStaleToken(tokenAddress: string, reason: string): Promise<void> {
    console.log(`🧹 开始清理过期Token: ${tokenAddress.slice(0, 8)}... (${reason})`);
    
    const instance = this.tokenInstances.get(tokenAddress);
    if (!instance) {
      console.log(`   ⚠️ Token实例不存在，直接清理基础数据`);
      await this.clearTokenHistoricalData(tokenAddress);
      return;
    }

    const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
    
    // 🔥 第一步：检查并处理持仓
    const hasPosition = instance.currentHolding && instance.currentHolding.amount > 0;
    if (hasPosition) {
      console.log(`   💼 检测到持仓，尝试紧急卖出: ${instance.currentHolding!.amount.toFixed(6)} 个`);
      
      try {
        // 设置交易状态，防止其他交易干扰
        instance.isTrading = true;
        
        // 尝试紧急卖出
        const sellResult = await this.executeRealSell(tokenAddress, instance.currentHolding!.amount, 0);
        
        if (sellResult.success && sellResult.bundleId && sellResult.actualPrice && sellResult.actualAmount) {
          const profit = sellResult.actualAmount - instance.currentHolding!.buySolAmount;
          const profitPercentage = ((sellResult.actualAmount / instance.currentHolding!.buySolAmount - 1) * 100);
          
          console.log(`   ✅ 紧急卖出成功:`);
          console.log(`     📦 卖出Token: ${instance.currentHolding!.amount.toFixed(6)} 个`);
          console.log(`     💵 收到SOL: ${sellResult.actualAmount.toFixed(4)} SOL`);
          console.log(`     📈 净盈亏: ${profit > 0 ? '+' : ''}${profit.toFixed(4)} SOL (${profit > 0 ? '+' : ''}${profitPercentage.toFixed(2)}%)`);
          
          // 记录到风险管理器和仓位管理器
          this.riskManager.recordTrade(profit, PRIORITY_FEE + (USE_JITO ? TIP_AMOUNT : 0));
          this.positionManager.closePosition(tokenAddress, sellResult.actualPrice, profit);
          
          // 记录交易历史
          instance.tradingHistory.push({
            type: 'sell',
            timestamp: new Date(),
            tokenAmount: instance.currentHolding!.amount,
            solAmount: sellResult.actualAmount,
            price: sellResult.actualPrice,
            prediction: 0, // 紧急卖出不基于AI预测
            bundleId: sellResult.bundleId
          });
          
          // 更新统计
          instance.stats.currentPosition = 0;
          instance.stats.totalTrades++;
          instance.stats.totalPnL += profit;
          
          if (profit > 0) {
            instance.stats.successfulTrades++;
          }
          
          // 发送Telegram通知
          await this.telegramNotifier.notifySystem(
            `🧹 紧急清理卖出\n\n` +
            `🪙 Token: ${tokenDisplay}\n` +
            `📦 卖出Token: ${instance.currentHolding!.amount.toFixed(6)} 个\n` +
            `💵 收到SOL: ${sellResult.actualAmount.toFixed(4)} SOL\n` +
            `💲 卖出价格: $${(sellResult.actualPrice * 1000000).toFixed(8)}/token\n` +
            `🛑 触发原因: ${reason}\n` +
            `💰 盈亏: ${profit > 0 ? '+' : ''}${profit.toFixed(4)} SOL (${profit > 0 ? '+' : ''}${profitPercentage.toFixed(2)}%)\n` +
            `🤖 AI预测: 0.0% (紧急清理卖出)\n` +
            `⏰ 时间: ${new Date().toLocaleString()}\n` +
            `✅ 状态: 已确认并清理完成`
          );
          
          // 清空持仓
          instance.currentHolding = null;
          
        } else {
          console.log(`   ❌ 紧急卖出失败: ${sellResult.error}`);
          console.log(`   ⚠️ 将保留持仓记录，但清理其他数据`);
          
          // 即使卖出失败，也发送通知
          await this.telegramNotifier.notifySystem(
            `⚠️ 清理过程中卖出失败\n\n` +
            `🪙 Token: ${tokenDisplay}\n` +
            `📦 持仓Token: ${instance.currentHolding!.amount.toFixed(6)} 个\n` +
            `🛑 清理原因: ${reason}\n` +
            `❌ 卖出失败: ${sellResult.error}\n` +
            `⚠️ 建议手动检查该持仓\n` +
            `⏰ 时间: ${new Date().toLocaleString()}`
          );
        }
        
      } catch (error) {
        console.error(`   ❌ 紧急卖出过程中发生错误:`, error);
        
        // 发送错误通知
        await this.telegramNotifier.notifySystem(
          `🚨 清理过程中发生错误\n\n` +
          `🪙 Token: ${tokenDisplay}\n` +
          `📦 持仓Token: ${instance.currentHolding!.amount.toFixed(6)} 个\n` +
          `🛑 清理原因: ${reason}\n` +
          `❌ 错误: ${error}\n` +
          `⚠️ 需要手动检查该持仓\n` +
          `⏰ 时间: ${new Date().toLocaleString()}`
        );
        
      } finally {
        instance.isTrading = false;
      }
    } else {
      console.log(`   ✅ 无持仓，直接进行数据清理`);
    }

    // 🔥 第二步：清理所有历史数据
    await this.clearTokenHistoricalData(tokenAddress);
    
    // 🔥 第三步：完全移除token
    await this.completelyRemoveToken(tokenAddress, reason);
    
    console.log(`🧹 Token ${tokenAddress.slice(0, 8)}... 清理完成`);
  }

  /**
   * 🔥 新增：清理token的所有历史数据
   */
  private async clearTokenHistoricalData(tokenAddress: string): Promise<void> {
    console.log(`   🗑️ 清理历史数据: ${tokenAddress.slice(0, 8)}...`);
    
    // 清理价格数据
    const priceDataCount = this.realPriceData.get(tokenAddress)?.length || 0;
    this.realPriceData.delete(tokenAddress);
    console.log(`     📊 清理价格记录: ${priceDataCount} 条`);
    
    // 清理价格历史
    const priceHistoryExists = this.tokenPriceHistory.has(tokenAddress);
    this.tokenPriceHistory.delete(tokenAddress);
    if (priceHistoryExists) {
      console.log(`     📈 清理价格历史记录`);
    }
    
    // 清理池子缓存
    const poolCacheExists = this.globalPoolCache.has(tokenAddress);
    this.globalPoolCache.delete(tokenAddress);
    if (poolCacheExists) {
      console.log(`     🏊 清理池子缓存`);
    }
    
    // 清理token符号缓存
    const symbolExists = this.tokenSymbolCache.has(tokenAddress);
    this.tokenSymbolCache.delete(tokenAddress);
    if (symbolExists) {
      console.log(`     🏷️ 清理符号缓存`);
    }
    
    // 清理token活动记录
    const activityExists = this.tokenActivities.has(tokenAddress);
    this.tokenActivities.delete(tokenAddress);
    if (activityExists) {
      console.log(`     📋 清理活动记录`);
    }
    
    // 清理预测锁定
    const lockExists = this.predictionLocks.has(tokenAddress);
    this.predictionLocks.delete(tokenAddress);
    if (lockExists) {
      console.log(`     🔒 清理预测锁定`);
    }
    
    // 清理最后预测时间
    const lastPredictionExists = this.lastPredictionTime.has(tokenAddress);
    this.lastPredictionTime.delete(tokenAddress);
    if (lastPredictionExists) {
      console.log(`     ⏰ 清理预测时间记录`);
    }
    
    console.log(`   ✅ 历史数据清理完成`);
  }

  // =============================================================================
  // 🔥 新增：专业交易系统辅助方法
  // =============================================================================

  /**
   * 格式化风险统计信息
   */
  private formatRiskStats(): string {
    const stats = this.riskManager.getStats();
    const posStats = this.positionManager.getPositionStats();
    
    return [
      `💰 当前余额: ${stats.currentBalance.toFixed(4)} SOL`,
      `📊 总交易: ${stats.totalTrades} 笔 (胜率: ${(stats.winRate * 100).toFixed(1)}%)`,
      `📈 总盈亏: ${stats.totalPnL > 0 ? '+' : ''}${stats.totalPnL.toFixed(4)} SOL`,
      `📉 连续亏损: ${stats.consecutiveLosses} 笔`,
      `🌅 日内盈亏: ${stats.dailyPnL > 0 ? '+' : ''}${stats.dailyPnL.toFixed(4)} SOL`,
      `💼 活跃仓位: ${posStats.activePositions}/${this.tradingConfig.maxPositions}`,
      `📊 最大回撤: ${(stats.maxDrawdown * 100).toFixed(2)}%`
    ].join('\n');
  }

  /**
   * 获取最近成交价格
   */
  private getLastTradePrice(tokenAddress: string): number | undefined {
    // 🔥 修复：优先使用最近的实时价格数据
    const realPrices = this.realPriceData.get(tokenAddress);
    if (realPrices && realPrices.length > 0) {
      // 只使用最近5分钟内的价格数据
      const now = Date.now();
      const recentPrices = realPrices.filter(p => (now - p.timestamp.getTime()) < 5 * 60 * 1000);
      
      if (recentPrices.length > 0) {
        const latestPrice = recentPrices[recentPrices.length - 1];
        console.log(`📊 使用实时价格: ${latestPrice.price.toFixed(8)} (${Math.floor((now - latestPrice.timestamp.getTime()) / 1000)}秒前)`);
        return latestPrice.price;
      }
    }
    
    // 🔥 如果没有最近的实时价格，暂时不触发止损
    console.log(`⚠️ 无最近价格数据，跳过止损检查 ${tokenAddress.slice(0, 8)}...`);
    return undefined;
  }

  /**
   * 执行止损卖出
   */
  private async executeStopLoss(tokenAddress: string, instance: TokenTradingInstance, currentPrice: number): Promise<void> {
    if (!instance.currentHolding) return;

    console.log(`🔴 执行止损卖出 ${tokenAddress.slice(0, 8)}...`);
    
    instance.isTrading = true;
    
    try {
      const result = await this.executeRealSell(tokenAddress, instance.currentHolding.amount, 0);
      
      if (result.success && result.bundleId && result.actualPrice && result.actualAmount) {
        const profit = result.actualAmount - instance.currentHolding.buySolAmount;
        const profitPercentage = ((result.actualAmount / instance.currentHolding.buySolAmount - 1) * 100);
        const holdingTimeMs = Date.now() - instance.currentHolding.buyTime.getTime();
        
        // 🔥 记录到风险管理器
        this.riskManager.recordTrade(profit, PRIORITY_FEE + (USE_JITO ? TIP_AMOUNT : 0));
        
        // 🔥 记录到仓位管理器
        this.positionManager.closePosition(tokenAddress, result.actualPrice, profit);
        
        // 记录交易历史
        instance.tradingHistory.push({
          type: 'sell',
          timestamp: new Date(),
          tokenAmount: instance.currentHolding.amount,
          solAmount: result.actualAmount,
          price: result.actualPrice,
          prediction: 0, // 止损不是基于AI预测
          bundleId: result.bundleId
        });
        
        const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
        console.log(`✅ 止损卖出成功 ${tokenDisplay}:`);
        console.log(`   📦 卖出Token: ${instance.currentHolding.amount.toFixed(2)} 个`);
        console.log(`   💵 收入SOL: ${result.actualAmount.toFixed(4)} SOL`);
        console.log(`   📈 净盈亏: ${profit > 0 ? '+' : ''}${profit.toFixed(4)} SOL (${profit > 0 ? '+' : ''}${profitPercentage.toFixed(2)}%)`);
        console.log(`   🔴 止损原因: 价格跌破止损线`);
        
        // 发送Telegram通知
        await this.telegramNotifier.notifySystem(
          `🔴 止损卖出执行\n\n` +
          `🪙 Token: ${tokenDisplay}\n` +
          `📦 卖出Token: ${instance.currentHolding.amount.toFixed(2)} 个\n` +
          `💵 收到SOL: ${result.actualAmount.toFixed(4)} SOL\n` +
          `💲 卖出价格: $${(result.actualPrice * 1000000).toFixed(8)}/token\n` +
          `🛑 触发原因: 价格跌破止损线\n` +
          `💰 盈亏: ${profit > 0 ? '+' : ''}${profit.toFixed(4)} SOL (${profit > 0 ? '+' : ''}${profitPercentage.toFixed(2)}%)\n` +
          `⏱️ 持仓时间: ${Math.floor(holdingTimeMs / (1000 * 60))}分钟\n` +
          `🤖 AI预测: 0.0% (止损强制卖出)\n` +
          `⏰ 时间: ${new Date().toLocaleString()}\n` +
          // `📋 交易签名: ${result.bundleId}\n` +
          `✅ 状态: 已确认`
        );
        
        // 更新统计
        instance.stats.currentPosition = 0;
        instance.stats.totalTrades++;
        instance.stats.totalPnL += profit;
        
        if (profit > 0) {
          instance.stats.successfulTrades++;
        }
        
        // 清空持仓
        instance.currentHolding = null;
        
        // 移除token
        await this.removeTokenFromMonitoring(tokenAddress, '止损卖出完成');
        
      } else {
        const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
        console.log(`❌ 止损卖出失败 ${tokenDisplay}: ${result.error}`);
      }
      
    } catch (error) {
      const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
      console.error(`❌ 止损卖出错误 ${tokenDisplay}:`, error);
    } finally {
      instance.isTrading = false;
    }
  }

  /**
   * 🔥 启动状态监控
   */
  private startStatusMonitoring(): void {
    const interval = this.tradingConfig.statsUpdateIntervalSeconds * 1000;
    
    this.statusMonitorTimer = setInterval(() => {
      if (!this.isRunning) return;
      
      const riskStats = this.riskManager.getStats();
      const posStats = this.positionManager.getPositionStats();
      
      console.log('\n📊 ===== 实时交易统计 =====');
      console.log(`🛡️ 风险管理:`);
      console.log(`   余额: ${riskStats.currentBalance.toFixed(4)} SOL`);
      console.log(`   总交易: ${riskStats.totalTrades} 笔`);
      console.log(`   胜率: ${(riskStats.winRate * 100).toFixed(1)}%`);
      console.log(`   连续亏损: ${riskStats.consecutiveLosses} 笔`);
      console.log(`   日内盈亏: ${riskStats.dailyPnL > 0 ? '+' : ''}${riskStats.dailyPnL.toFixed(4)} SOL`);
      console.log(`   交易状态: ${riskStats.tradingPaused ? '🔴 已暂停' : '✅ 正常'}`);
      
      console.log(`💼 仓位管理:`);
      console.log(`   活跃仓位: ${posStats.activePositions}/${this.tradingConfig.maxPositions}`);
      console.log(`   总仓位: ${posStats.totalPositions} 个`);
      console.log(`   仓位胜率: ${(posStats.winRate * 100).toFixed(1)}%`);
      console.log(`   已实现盈亏: ${posStats.totalRealizedPnL > 0 ? '+' : ''}${posStats.totalRealizedPnL.toFixed(4)} SOL`);
      
      console.log(`🪙 Token监控:`);
      console.log(`   监控队列: ${this.tokenMonitoringQueue.length}/${MAX_MONITORED_TOKENS}`);
      console.log(`   缓冲队列: ${this.sellOnlyBufferQueue.size} 个`);
      console.log(`   🔥 全局Pool缓存: ${this.globalPoolCache.size} 个`);
      
      // 🔥 显示Pool缓存详情
      if (this.globalPoolCache.size > 0) {
        console.log(`📋 Pool缓存详情:`);
        for (const [tokenAddress, poolData] of this.globalPoolCache.entries()) {
          // 使用同步方式显示，避免async问题
          const symbol = this.tokenSymbolCache.get(tokenAddress) || tokenAddress.slice(0, 8);
          const shortAddress = tokenAddress.slice(0, 8) + '...' + tokenAddress.slice(-8);
          const tokenDisplay = symbol === tokenAddress.slice(0, 8) ? shortAddress : `${symbol} (${shortAddress})`;
          console.log(`   ${tokenDisplay} -> Pool: ${poolData.poolAddress.toBase58().slice(0, 8)}...`);
        }
      }
      
      console.log('========================\n');
      
      // 🔥 自动检查并清理卡住的交易状态
      this.checkAndCleanStuckTrades();
      
    }, interval);
    
    console.log(`📊 状态监控已启动 (间隔: ${this.tradingConfig.statsUpdateIntervalSeconds}秒)`);
  }

  /**
   * 🔥 新增：检查系统存储的SOL余额是否足够执行买入
   */
  private checkSufficientSolBalance(): { canBuy: boolean; currentBalance: number; reason?: string } {
    const riskStats = this.riskManager.getStats();
    const currentSolBalance = riskStats.currentBalance;
    const minimumRequired = 0.002; // 最小SOL余额要求
    
    console.log(`💰 SOL余额检查:`);
    console.log(`   当前余额: ${currentSolBalance.toFixed(6)} SOL`);
    console.log(`   最小要求: ${minimumRequired.toFixed(6)} SOL`);
    console.log(`   检查结果: ${currentSolBalance >= minimumRequired ? '✅ 通过' : '❌ 不足'}`);
    
    if (currentSolBalance < minimumRequired) {
      return {
        canBuy: false,
        currentBalance: currentSolBalance,
        reason: `SOL余额不足 (当前: ${currentSolBalance.toFixed(6)} SOL < 要求: ${minimumRequired.toFixed(6)} SOL)`
      };
    }
    
    return {
      canBuy: true,
      currentBalance: currentSolBalance
    };
  }

  /**
   * 🔥 新增：买入成功后5秒钟通过RPC更新钱包余额和持仓数据
   */
  private schedulePostBuyBalanceUpdate(tokenAddress: string, buyResult: RealTradeResult): void {
    console.log(`⏰ 计划5秒后更新钱包余额和持仓数据...`);
    
    setTimeout(async () => {
      try {
        console.log(`🔄 开始执行买入后余额和持仓更新...`);
        
        // 1. 更新SOL余额
        const updatedSolBalance = await getWalletSolBalance();
        console.log(`💰 更新后SOL余额: ${updatedSolBalance.toFixed(6)} SOL`);
        
        // 更新风险管理器中的余额
        const riskStats = this.riskManager.getStats();
        const balanceDifference = updatedSolBalance - riskStats.currentBalance;
        console.log(`📊 余额变化: ${balanceDifference > 0 ? '+' : ''}${balanceDifference.toFixed(6)} SOL`);
        
        // 如果余额变化明显（超过0.001 SOL），手动更新风险管理器余额
        if (Math.abs(balanceDifference) > 0.001) {
          console.log(`🔄 同步风险管理器余额数据...`);
          // 手动更新风险管理器的内部余额状态
          const riskStats = this.riskManager.getStats();
          riskStats.currentBalance = updatedSolBalance;
        }
        
        // 2. 更新Token持仓数据
        const instance = this.tokenInstances.get(tokenAddress);
        if (instance) {
          console.log(`🪙 更新Token持仓数据: ${tokenAddress.slice(0, 8)}...`);
          
          // 获取真实Token余额
          const realTokenBalance = await this.getRealTokenBalance(tokenAddress);
          console.log(`📦 真实Token余额: ${realTokenBalance.toFixed(6)} 个`);
          
          // 更新实例持仓数据
          if (instance.currentHolding && realTokenBalance > 0) {
            const originalAmount = instance.currentHolding.amount;
            instance.currentHolding.amount = realTokenBalance;
            instance.stats.currentPosition = realTokenBalance;
            
            console.log(`✅ 持仓数量已更新: ${originalAmount.toFixed(6)} -> ${realTokenBalance.toFixed(6)} 个`);
            
            // 同步到PositionManager
            const position = this.positionManager.getPosition(tokenAddress);
            if (position) {
              position.amount = realTokenBalance;
              console.log(`🔄 已同步到PositionManager`);
            }
          } else if (realTokenBalance > 0 && !instance.currentHolding) {
            console.log(`⚠️ 检测到Token余额但无持仓记录，可能存在数据不一致`);
          }
          
          // 3. 获取并显示完整的Token持仓列表
          console.log(`📋 查询所有Token持仓...`);
          await this.updateAllTokenBalances();
          
        } else {
          console.log(`⚠️ 未找到Token实例，跳过持仓更新`);
        }
        
        console.log(`✅ 买入后余额和持仓更新完成`);
        
      } catch (error) {
        console.error(`❌ 买入后余额更新失败:`, error);
      }
    }, 5000); // 5秒后执行
  }

  /**
   * 🔥 新增：更新所有Token持仓余额
   */
  private async updateAllTokenBalances(): Promise<void> {
    try {
      console.log(`🔄 开始更新所有Token持仓余额...`);
      
      const activeTokens = Array.from(this.tokenInstances.keys());
      const balanceUpdates: Array<{address: string, balance: number, symbol: string}> = [];
      
      for (const tokenAddress of activeTokens) {
        try {
          const balance = await this.getRealTokenBalance(tokenAddress);
          const symbol = this.tokenSymbolCache.get(tokenAddress) || tokenAddress.slice(0, 8);
          
          if (balance > 0.000001) { // 只记录有意义的余额
            balanceUpdates.push({
              address: tokenAddress,
              balance: balance,
              symbol: symbol
            });
          }
        } catch (error) {
          console.log(`⚠️ 获取Token余额失败 ${tokenAddress.slice(0, 8)}: ${error}`);
        }
      }
      
      if (balanceUpdates.length > 0) {
        console.log(`📊 当前Token持仓概览:`);
        balanceUpdates.forEach((token, index) => {
          console.log(`   ${index + 1}. ${token.symbol}: ${token.balance.toFixed(6)} 个`);
        });
      } else {
        console.log(`📊 当前没有检测到Token持仓`);
      }
      
    } catch (error) {
      console.error(`❌ 更新Token持仓余额失败:`, error);
    }
  }

  /**
   * 🔥 检测网络相关错误
   */
  private isNetworkRelatedError(error: any): boolean {
    if (!error) return false;
    
    const networkErrorCodes = [
      'UND_ERR_CONNECT_TIMEOUT',
      'ECONNABORTED',
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      'ECONNRESET',
      'EPIPE',
      'EHOSTUNREACH',
      'ENETUNREACH',
      'GRPC_ERROR'
    ];
    
    const networkErrorMessages = [
      'fetch failed',
      'connect timeout',
      'connection timeout',
      'network error',
      'connection refused',
      'host unreachable',
      'network unreachable',
      'grpc error',
      'stream error',
      'connection lost'
    ];
    
    // 检查错误代码
    if (error.code && networkErrorCodes.includes(error.code)) {
      return true;
    }
    
    // 检查错误消息
    const errorMessage = (error.message || '').toLowerCase();
    if (networkErrorMessages.some(msg => errorMessage.includes(msg))) {
      return true;
    }
    
    // 检查嵌套的错误
    if (error.cause && this.isNetworkRelatedError(error.cause)) {
      return true;
    }
    
    return false;
  }

  /**
   * 处理GRPC流
   */
  private async handleStream(client: Client, args: SubscribeRequest): Promise<void> {
    const maxRetries = 3;
    let currentRetry = 0;
    let consecutiveErrors = 0;
    const MAX_CONSECUTIVE_ERRORS = 5;
    
    while (this.isRunning && currentRetry < maxRetries) {
      let stream: any = null;
      let statusInterval: NodeJS.Timeout | null = null;
      let streamTimeout: NodeJS.Timeout | null = null;
      let connectTimeout: NodeJS.Timeout | null = null;
      
      try {
        console.log(`🔗 尝试建立GRPC流连接... (尝试 ${currentRetry + 1}/${maxRetries})`);
        
        // 🔥 添加连接超时保护
        connectTimeout = setTimeout(() => {
          console.log('⏰ GRPC连接超时 (30秒)');
          if (stream) {
            try {
              stream.destroy();
            } catch (destroyError) {
              console.log('强制关闭连接时发生错误:', destroyError);
            }
          }
        }, 30000);
        
        stream = await client.subscribe();
        
        if (connectTimeout) {
          clearTimeout(connectTimeout);
          connectTimeout = null;
        }
        
        this.currentStream = stream;
        console.log(`✅ GRPC流连接已建立`);
        currentRetry = 0; // 重置重试计数
        consecutiveErrors = 0; // 重置连续错误计数

        const streamClosed = new Promise<void>((resolve, reject) => {
          let hasResolved = false;
          
          const resolveOnce = (reason?: string) => {
            if (!hasResolved) {
              hasResolved = true;
              console.log(`🔚 GRPC流关闭: ${reason || '正常结束'}`);
              
              // 清理所有定时器
              if (statusInterval) clearInterval(statusInterval);
              if (streamTimeout) clearTimeout(streamTimeout);
              if (connectTimeout) clearTimeout(connectTimeout);
              
              resolve();
            }
          };
          
          const rejectOnce = (error: any) => {
            if (!hasResolved) {
              hasResolved = true;
              console.log(`❌ GRPC流错误:`, error?.message || error);
              
              // 清理所有定时器
              if (statusInterval) clearInterval(statusInterval);
              if (streamTimeout) clearTimeout(streamTimeout);
              if (connectTimeout) clearTimeout(connectTimeout);
              
              // 检查是否是网络相关错误
              if (this.isNetworkRelatedError(error)) {
                console.log('🔄 检测到网络错误，将尝试重连...');
                consecutiveErrors++;
                if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
                  console.log(`❌ 连续错误达到上限 (${MAX_CONSECUTIVE_ERRORS}次)，延长等待时间`);
                  setTimeout(() => resolve(), 30000); // 30秒后重试
                } else {
                  resolve(); // 作为正常结束处理，触发重连
                }
              } else {
                reject(error);
              }
            }
          };

          // 🔥 增强的错误处理
          stream.on("error", (error: any) => {
            console.log("❌ GRPC Stream ERROR:", error?.message || error);
            rejectOnce(error);
            
            // 🔥 安全关闭流
            try {
              if (stream && typeof stream.end === 'function') {
                stream.end();
              }
            } catch (endError) {
              console.log('流关闭时发生错误:', endError);
            }
          });
          
          stream.on("end", () => {
            resolveOnce('流结束');
          });
          
          stream.on("close", () => {
            resolveOnce('流关闭');
          });
          
          // 🔥 添加流超时保护（5分钟无数据触发重连）
          const resetStreamTimeout = () => {
            if (streamTimeout) clearTimeout(streamTimeout);
            streamTimeout = setTimeout(() => {
              if (!hasResolved) {
                console.log('⏰ GRPC流超时 (5分钟无数据)');
                resolveOnce('流超时');
                try {
                  if (stream && typeof stream.destroy === 'function') {
                    stream.destroy();
                  }
                } catch (destroyError) {
                  console.log('强制关闭流时发生错误:', destroyError);
                }
              }
            }, 300000); // 5分钟超时
          };
          
          resetStreamTimeout(); // 初始设置超时
        });

        let lastReceivedTxTime = 0;
        let totalDataReceived = 0;
        let connectionStartTime = Date.now();
        
        // 🔥 Enhanced status display with comprehensive error handling
        statusInterval = setInterval(() => {
          if (!this.isRunning) {
            if (statusInterval) clearInterval(statusInterval);
            return;
          }
          
          try {
            const now = Date.now();
            const secondsSinceLastTx = (now - lastReceivedTxTime) / 1000;
            const secondsSinceStart = (now - connectionStartTime) / 1000;
            
            console.log(`\n📊 GRPC连接状态 (${Math.floor(secondsSinceStart / 60)}分${Math.floor(secondsSinceStart % 60)}秒):`);
            console.log(`   📡 连接状态: ${this.currentStream ? '✅ 已连接' : '❌ 未连接'}`);
            console.log(`   📦 数据包总数: ${totalDataReceived}`);
            console.log(`   🔄 重试次数: ${currentRetry}/${maxRetries}`);
            console.log(`   ⚠️ 连续错误: ${consecutiveErrors}/${MAX_CONSECUTIVE_ERRORS}`);
            
            if (lastReceivedTxTime > 0) {
              console.log(`   📡 上次交易: ${secondsSinceLastTx.toFixed(0)} 秒前`);
              
              // 🔥 检查是否长时间无数据
              if (secondsSinceLastTx > 180) { // 3分钟无数据
                console.log('⚠️ 长时间无交易数据，连接可能有问题');
              }
            } else {
              console.log(`   📡 交易状态: ⚠️ 无交易数据`);
            }
            
          } catch (statusError) {
            console.error('状态显示错误:', statusError);
          }
          
        }, STATUS_UPDATE_INTERVAL);

        // 🔥 Enhanced data processing with comprehensive error handling
        stream.on("data", async (data: any) => {
          if (!this.isRunning) return;
          
          try {
            totalDataReceived++;
            
            // 重置流超时计时器（收到数据说明连接正常）
            if (streamTimeout) {
              clearTimeout(streamTimeout);
              streamTimeout = setTimeout(() => {
                console.log('⏰ GRPC流超时 (5分钟无数据)');
                try {
                  if (stream && typeof stream.destroy === 'function') {
                    stream.destroy();
                  }
                } catch (destroyError) {
                  console.log('强制关闭流时发生错误:', destroyError);
                }
              }, 300000);
            }
            
            if (data?.transaction) {
              lastReceivedTxTime = Date.now();
              
              // 🔥 尝试提取真实的交易时间戳
              let transactionTimestamp = new Date();
              try {
                // 从 data.transaction 中提取时间戳信息
                if (data.transaction.meta && data.transaction.meta.blockTime) {
                  transactionTimestamp = new Date(data.transaction.meta.blockTime * 1000);
                } else if (data.transaction.blockTime) {
                  transactionTimestamp = new Date(data.transaction.blockTime * 1000);
                } else if (data.slot) {
                  // 如果没有blockTime，使用slot来估算时间（每个slot约400ms）
                  const estimatedTime = Date.now() - (data.slot * 0.4);
                  transactionTimestamp = new Date(estimatedTime);
                }
              } catch (timestampError) {
                console.warn('提取交易时间戳失败，使用当前时间:', timestampError);
                transactionTimestamp = new Date();
              }
              
              const txn = TXN_FORMATTER.formTransactionFromJson(data.transaction, transactionTimestamp.getTime());
              const parsedTxn = this.decodePumpFunTxn(txn);
              if (!parsedTxn) {
                return;
              }

              // 检查交易是否涉及目标钱包或监控的token
              const isTargetWalletInvolved = await this.isWalletInvolvedInTransaction(txn, TARGET_WALLET);
              const isMyWalletInvolved = await this.isWalletInvolvedInTransaction(txn, MY_WALLET);
              const accountKeys = txn.transaction.message.staticAccountKeys?.map(key => key.toBase58()) || [];
              const monitoredTokensInvolved = Array.from(this.currentTokenSubscriptions).filter(token => 
                accountKeys.includes(token)
              );
              
              // 🔥 优先处理自己钱包的交易（获取真实交易数据）
              if (isMyWalletInvolved) {
                await this.processMyWalletTransaction(txn, data, parsedTxn, transactionTimestamp);
                return;
              }
              
              // 如果涉及监控的token但不涉及目标钱包，也要记录
              if (monitoredTokensInvolved.length > 0 && !isTargetWalletInvolved) {
                await this.processMonitoredTokenTransaction(txn, data, monitoredTokensInvolved, parsedTxn, transactionTimestamp);
                return;
              }
              
              if (!isTargetWalletInvolved) return;

              // 处理目标钱包交易
              await this.processTargetWalletTransaction(txn, data, parsedTxn, transactionTimestamp);
            }
            
          } catch (dataError) {
            console.error('数据处理错误:', dataError);
            
            // 如果是网络错误，不要影响流的稳定性
            if (!this.isNetworkRelatedError(dataError)) {
              consecutiveErrors++;
              if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
                console.log(`❌ 数据处理连续错误达到上限，重启流`);
                try {
                  if (stream && typeof stream.destroy === 'function') {
                    stream.destroy();
                  }
                } catch (destroyError) {
                  console.log('强制关闭流时发生错误:', destroyError);
                }
              }
            }
          }
        });

        // 发送订阅请求
        try {
          await new Promise<void>((resolve, reject) => {
            stream.write(args, (err: any) => {
              if (err === null || err === undefined) {
                console.log('✅ 订阅请求已发送');
                resolve();
              } else {
                console.error('❌ 发送订阅请求失败:', err);
                reject(err);
              }
            });
          });
        } catch (writeError) {
          console.error('❌ 写入订阅请求失败:', writeError);
          throw writeError;
        }

        // 等待流关闭或错误
        await streamClosed;
        
        // 正常结束，等待一下再重连
        if (this.isRunning) {
          console.log('🔄 流已关闭，2秒后重连...');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
      } catch (error) {
        console.error(`❌ GRPC流处理失败 (尝试 ${currentRetry + 1}/${maxRetries}):`, error);
        
        // 清理所有定时器和资源
        if (statusInterval) clearInterval(statusInterval);
        if (streamTimeout) clearTimeout(streamTimeout);
        if (connectTimeout) clearTimeout(connectTimeout);
        
        // 尝试安全关闭流
        if (stream) {
          try {
            if (typeof stream.end === 'function') {
              stream.end();
            }
            if (typeof stream.destroy === 'function') {
              stream.destroy();
            }
          } catch (closeError) {
            console.log('关闭流时发生错误:', closeError);
          }
        }
        
        currentRetry++;
        
        if (currentRetry >= maxRetries) {
          console.error('❌ 所有GRPC重试都失败了');
          
          // 如果是网络错误，不要完全失败，而是等待更长时间后重试
          if (this.isNetworkRelatedError(error)) {
            console.log('🔄 网络错误，等待30秒后重新开始...');
            await new Promise(resolve => setTimeout(resolve, 30000));
            currentRetry = 0; // 重置重试计数，继续尝试
            continue;
          } else {
            throw error; // 非网络错误，抛出异常
          }
        }
        
        // 计算退避延迟（指数退避，但有上限）
        const delay = Math.min(1000 * Math.pow(2, currentRetry - 1), 15000);
        console.log(`⏳ 等待 ${delay/1000} 秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      } finally {
        // 确保清理所有资源
        if (statusInterval) clearInterval(statusInterval);
        if (streamTimeout) clearTimeout(streamTimeout);
        if (connectTimeout) clearTimeout(connectTimeout);
        this.currentStream = null;
      }
    }
    
    console.log('❌ GRPC流处理完全失败，需要外层重启');
  }

  /**
   * 🔥 处理监控Token的其他钱包交易
   */
  private async processMonitoredTokenTransaction(
    txn: any, 
    data: any, 
    monitoredTokensInvolved: string[], 
    parsedTxn: any,
    transactionTimestamp?: Date
  ): Promise<void> {
    // This function is complex, so we process one token at a time from the list.
    for (const tokenAddress of monitoredTokensInvolved) {
      const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
      
      try {
        // 1. Update general activity stats
        if (!this.tokenActivities.has(tokenAddress)) {
          this.tokenActivities.set(tokenAddress, {
            address: tokenAddress,
            firstSeen: new Date(),
            lastSeen: new Date(),
            transactionCount: 0,
            actions: []
          });
        }
        const activity = this.tokenActivities.get(tokenAddress)!;
        activity.lastSeen = new Date();
        activity.transactionCount++;

        // 2. Extract detailed info from the on-chain transaction
        const extractedInfo = await this.extractTokenInfoFromTransaction(parsedTxn, false);
        
        let transactionProcessed = false;
        if (extractedInfo && extractedInfo.tokens) {
          for (const tokenInfo of extractedInfo.tokens) {
            // We only care about the token we are currently processing in this loop iteration.
            if (tokenInfo.address !== tokenAddress) {
              continue;
            }

            // 🔥 **交易额过滤**: 暂时禁用以进行调试
          /*
          if (!tokenInfo.solAmount || tokenInfo.solAmount < MIN_TRANSACTION_SOL) {
            if (process.env.DEBUG_TRANSACTION_FILTERING === 'true') {
              console.log(`🚫 [Monitor] 过滤小额交易: ${tokenInfo.solAmount?.toFixed(6) || 'N/A'} SOL < ${MIN_TRANSACTION_SOL} SOL for ${tokenDisplay}`);
            }
            continue; // Skip this transaction
          }
          */  
            
            // We need both SOL and token amount to process further.
            if (tokenInfo.solAmount && tokenInfo.tokenAmount) {
              transactionProcessed = true;
              
              // 3. Record transaction to central history via SubscriptionManager
              await this.subscriptionManager.processWalletTransaction(
                txn.transaction.signatures[0] || 'unknown_signature',
                new Date(),
                txn.slot || 0,
                tokenAddress,
                tokenInfo.action, // 'buy' | 'sell' | 'unknown'
                tokenInfo.solAmount,
                tokenInfo.usdAmount || 0,
                'market_participant' // Generic market participant
              );
              
              // 4. Record for general activity tracking
              activity.actions.push({
                type: tokenInfo.action as 'buy' | 'sell',
                amount: tokenInfo.tokenAmount,
                timestamp: new Date(),
                price: tokenInfo.solAmount / tokenInfo.tokenAmount
              });
              if (activity.actions.length > 100) activity.actions.shift();

              // 5. Record for real-time price tracking
              // 🔥 确保新token也能记录价格数据（即使不在监控列表中）
              this.recordRealPrice(
                tokenAddress,
                tokenInfo.solAmount,
                tokenInfo.tokenAmount,
                tokenInfo.action as 'buy' | 'sell',
                'stream',
                transactionTimestamp
              );
              
              if (process.env.DEBUG_TRANSACTION_LOGGING === 'true') {
                console.log(`📈 [Monitor] 记录有效交易: ${tokenInfo.action} for ${tokenDisplay}, ${tokenInfo.solAmount.toFixed(4)} SOL`);
              }
            }
          }
        }
        
        // 7. Trigger AI prediction if a valid transaction was processed for this token
        if (transactionProcessed) {
          try {
            await this.makePredictionForToken(tokenAddress);
          } catch (error) {
            console.error(`❌ AI预测失败 ${tokenDisplay}:`, error);
          }
        }
        
      } catch (error) {
        console.error(`❌ 处理监控token交易数据失败 ${tokenDisplay}:`, error);
      }
    }
  }

  /**
   * 🔥 处理目标钱包交易
   */
  private async processTargetWalletTransaction(txn: any, data: any, parsedTxn: any, transactionTimestamp?: Date): Promise<void> {
    const targetSignature = txn.transaction.signatures[0] || 'unknown';
    const slot = data.transaction?.slot || 'unknown';
    const timestamp = new Date();

    if (process.env.DEBUG_TRANSACTION_PARSING === 'true') {
      console.log(`\n🎯 发现目标钱包交易:`);
      console.log(`   钱包: ${TARGET_WALLET.slice(0, 8)}...`);
      console.log(`   交易ID: ${targetSignature}`);
      console.log(`   时间: ${timestamp.toLocaleString()}`);
    }

    try {
          // 提取token信息
    const tokenInfo = await this.extractTokenInfoFromTransaction(parsedTxn, false);
      if (tokenInfo) {
        for (const token of tokenInfo.tokens) {
          // 过滤小额交易
          if (token.solAmount && token.solAmount < MIN_TRANSACTION_SOL) {
            const tokenDisplay = await this.formatTokenDisplay(token.address);
            console.log(`🚫 跳过小额交易Token: ${tokenDisplay} (${token.solAmount.toFixed(6)} SOL)`);
              continue;
            }

          const tokenDisplay = await this.formatTokenDisplay(token.address);
          console.log(`   🪙 Token: ${tokenDisplay} (${token.action})`);
          console.log(`   💰 金额: ${token.solAmount?.toFixed(4) || 'N/A'} SOL`);
          console.log(`   🔢 Token数量: ${token.tokenAmount?.toFixed(2) || 'N/A'} tokens`);
          
          // 记录真实价格数据
          if (token.solAmount && token.tokenAmount && token.tokenAmount > 0) {
            this.recordRealPrice(
              token.address,
              token.solAmount,
              token.tokenAmount,
              token.action as 'buy' | 'sell',
              'stream',
              transactionTimestamp
            );
          }
          
          // 如果是已监控的token，特别标记
          if (this.currentTokenSubscriptions.has(token.address)) {
            console.log(`   🔥 这是已监控的Token - 将更新交易历史并立即AI预测！`);
            
            // 🔥 检查是否是缓冲队列中的token
            if (this.sellOnlyBufferQueue.has(token.address)) {
              console.log(`📨 缓冲队列token收到数据: ${tokenDisplay} ${token.action} ${token.solAmount?.toFixed(4)}SOL`);
              console.log(`   缓冲队列大小: ${this.sellOnlyBufferQueue.size}`);
              console.log(`   将触发AI预测...`);
            }
          }
          
          // 记录钱包交易到动态订阅管理器
              await this.subscriptionManager.processWalletTransaction(
            targetSignature,
            timestamp,
            typeof slot === 'number' ? slot : 0,
            token.address,
            token.action,
            token.solAmount || 0,
            token.usdAmount || 0,
            TARGET_WALLET
          );

          // 🔥 从交易数据中提取并存储Pool信息
          const tokenMint = new PublicKey(token.address);
          await this.extractPoolDataFromTransaction(tokenMint, parsedTxn);
          
          // 🍃 目标钱包交易：仅收集token信息，不触发AI预测
          // AI预测统一由processMonitoredTokenTransaction负责，避免重复调用导致的并发问题
          console.log(`📊 目标钱包交易数据已收集: ${tokenDisplay} ${token.action} ${token.solAmount?.toFixed(4)}SOL`);
        }

        // 更新token订阅
        await this.updateTokenSubscriptions();
      }

      if (process.env.DEBUG_TRANSACTION_PARSING === 'true') {
        console.log(`   🔗 交易链接: https://translator.shyft.to/tx/${targetSignature}`);
        console.log('-'.repeat(80));
      }
    } catch (error) {
      console.error('处理目标钱包交易时发生错误:', error);
    }
  }

  /**
   * 🔥 改进版：解析PumpFun交易 - 与index.ts保持一致
   */
  private decodePumpFunTxn(tx: VersionedTransactionResponse) {
    if (tx.meta?.err) return null;

    // 🔥 修复：使用与index.ts完全相同的静默控制台逻辑
    const originalConsole = console;
    const silentConsole = {
      log: () => {},
      error: () => {},
      warn: () => {},
      info: () => {},
      debug: () => {}
    };
    
    // @ts-ignore - 前端环境不支持global，这里假设我们在Node环境
    global.console = silentConsole;
    
    try {
      const paredIxs = PUMP_FUN_IX_PARSER.parseTransactionData(
        tx.transaction.message,
        tx.meta.loadedAddresses
      );
      
      // 🔥 修复：过滤掉ComputeBudget程序的指令 - 与index.ts逻辑一致
      const filteredIxs = paredIxs.filter(ix => 
        // 只保留非ComputeBudget的指令
        !ix.programId.toString().includes('ComputeBudget')
      );

      const pumpFunIxs = filteredIxs.filter((ix) =>
        ix.programId.equals(PUMP_FUN_AMM_PROGRAM_ID)
      );

      if (pumpFunIxs.length === 0) return null;
      
      const events = PUMP_FUN_EVENT_PARSER.parseEvent(tx);
      const result = { instructions: pumpFunIxs, events };
      bnLayoutFormatter(result);
      
      // 🔥 调试信息已隐藏
      
      return result;
    } finally {
      // 恢复原始控制台
      // @ts-ignore
      global.console = originalConsole;
    }
  }

  /**
   * 检查钱包是否参与交易
   */
  private async isWalletInvolvedInTransaction(tx: VersionedTransactionResponse, wallet: string): Promise<boolean> {
    try {
      const accountKeys = tx.transaction.message.staticAccountKeys?.map(key => key.toBase58()) || [];
      return accountKeys.includes(wallet);
    } catch (error) {
      console.error('检查钱包参与交易时出错:', error);
      return false;
    }
  }

  /**
   * 🔥 改进版：从交易中提取token信息 - 参考index.ts的成功解析逻辑
   */
  private async extractTokenInfoFromTransaction(parsedTxn: any, isMyWallet: boolean = false): Promise<{ tokens: Array<{ address: string, action: 'buy' | 'sell' | 'unknown', solAmount?: number, tokenAmount?: number, usdAmount?: number }> } | null> {
    const tokens: Array<{ address:string, action: 'buy' | 'sell' | 'unknown', solAmount?: number, tokenAmount?: number, usdAmount?: number }> = [];

  if (process.env.DEBUG_TRANSACTION_PARSING === 'true') {
    console.log('--- DEBUG: Full Parsed Transaction ---');
    console.log(JSON.stringify(parsedTxn, null, 2));
    console.log('--- END DEBUG ---');
  }

    try {
      // 🔥 新增：先从指令中提取所有token地址（更可靠的方法）
      const allTokenAddresses = new Set<string>();
      const baseMints = new Set<string>();
      const quoteMints = new Set<string>();

      // 从指令中提取token地址
      if (parsedTxn.instructions && parsedTxn.instructions.length > 0) {
        for (const ix of parsedTxn.instructions) {
          if (process.env.DEBUG_TRANSACTION_PARSING === 'true') {
            console.log(`   指令: ${ix.name || '未知'}`);
          }
          
          if (ix.accounts) {
            // 查找base_mint
            const baseMintAccounts = ix.accounts.filter((a: any) => a.name === 'base_mint');
            for (const account of baseMintAccounts) {
              const pubkeyStr = String(account.pubkey);
              console.log(`   base_mint: ${pubkeyStr}`);
              baseMints.add(pubkeyStr);
              allTokenAddresses.add(pubkeyStr);
            }
            
            // 查找quote_mint
            const quoteMintAccounts = ix.accounts.filter((a: any) => a.name === 'quote_mint');
            for (const account of quoteMintAccounts) {
              const pubkeyStr = String(account.pubkey);
              console.log(`   quote_mint: ${pubkeyStr}`);
              quoteMints.add(pubkeyStr);
              allTokenAddresses.add(pubkeyStr);
            }

            // 查找其他可能的token相关账户
            const tokenAccounts = ix.accounts.filter((a: any) => 
              a.name && (
                a.name.includes('token') || 
                a.name.includes('mint') || 
                a.name.includes('_spl')
              ));
            
            for (const account of tokenAccounts) {
              if (account.name !== 'base_mint' && account.name !== 'quote_mint') {
                const pubkeyStr = String(account.pubkey);
                // console.log(`   其他token账户: ${account.name} = ${pubkeyStr}`);
                allTokenAddresses.add(pubkeyStr);
              }
            }
          }
        }
      }

      // 从事件中提取交易信息和验证token地址
      if (parsedTxn.events && Array.isArray(parsedTxn.events)) {
        console.log(`📋 解析事件 (${parsedTxn.events.length}个):`);
        
        for (const event of parsedTxn.events) {
          console.log(`   事件: ${event.name || '未知'}`);
          
          let action: 'buy' | 'sell' | 'unknown' = 'unknown';
          let solAmount = 0;
          let tokenAmount = 0;
          let tokenAddress = 'unknown';

          if (event.name === 'BuyEvent') {
            action = 'buy';

            // 🔥 修复SOL数据处理：PumpFun事件中的quote_amount_in通常是lamports
            const rawQuoteAmount = parseFloat(event.data.quote_amount_in || 0);
            solAmount = rawQuoteAmount / 1e9; // 统一按lamports处理
            console.log(`   💰 SOL处理: ${rawQuoteAmount} lamports -> ${solAmount.toFixed(6)} SOL`);

            // 🔥 修复Token数据：PumpFun token通常是6位小数，需要标准化
            const rawTokenAmount = parseFloat(event.data.base_amount_out || 0);
            tokenAmount = rawTokenAmount / 1e6; // 标准化为6位小数
            console.log(`   🪙 Token处理: ${rawTokenAmount} 原始 -> ${tokenAmount.toFixed(6)} tokens`);

            console.log(`   📈 BuyEvent: ${solAmount.toFixed(6)} SOL -> ${tokenAmount.toFixed(6)} tokens`);
            
          } else if (event.name === 'SellEvent') {
            action = 'sell';

            // 🔥 修复SOL数据处理：PumpFun事件中的quote_amount_out通常是lamports
            const rawQuoteAmount = parseFloat(event.data.quote_amount_out || 0);
            solAmount = rawQuoteAmount / 1e9; // 统一按lamports处理
            console.log(`   💰 SOL处理: ${rawQuoteAmount} lamports -> ${solAmount.toFixed(6)} SOL`);

            // 🔥 修复Token数据：PumpFun token通常是6位小数，需要标准化
            const rawTokenAmount = parseFloat(event.data.base_amount_in || 0);
            tokenAmount = rawTokenAmount / 1e6; // 标准化为6位小数
            console.log(`   🪙 Token处理: ${rawTokenAmount} 原始 -> ${tokenAmount.toFixed(6)} tokens`);

            console.log(`   📉 SellEvent: ${tokenAmount.toFixed(6)} tokens -> ${solAmount.toFixed(6)} SOL`);
          }

          // 🔥 改进：优先从事件数据获取token地址，然后从指令中验证
          if (event.data.base_mint) {
            tokenAddress = String(event.data.base_mint);
            console.log(`   事件base_mint: ${tokenAddress}`);
            baseMints.add(tokenAddress);
            allTokenAddresses.add(tokenAddress);
          }

          // 如果事件中没有token地址，从指令中的base_mint集合中取第一个
          if (tokenAddress === 'unknown' && baseMints.size > 0) {
            tokenAddress = Array.from(baseMints)[0];
            console.log(`   使用指令base_mint: ${tokenAddress}`);
          }

          // 🔥 修复：对于自己钱包的交易，不应用最小交易过滤
          if (solAmount < MIN_TRANSACTION_SOL && !isMyWallet) {
            console.log(`🚫 过滤小额交易: ${solAmount.toFixed(6)} SOL < ${MIN_TRANSACTION_SOL} SOL (${action})`);
            continue;
          }
          
          if (isMyWallet && solAmount < MIN_TRANSACTION_SOL) {
            console.log(`💰 自己钱包小额交易，跳过过滤: ${solAmount.toFixed(6)} SOL (${action})`);
          }

          // 只有当我们有有效的token地址和交易数据时才添加
          if (tokenAddress !== 'unknown' && solAmount > 0) {
            // 🔥 修复：使用真实的SOL价格而不是假设值
            let usdAmount = 0;
            try {
              usdAmount = await this.priceService.convertSolToUsd(solAmount);
              console.log(`💰 SOL价格转换: ${solAmount.toFixed(4)} SOL = $${usdAmount.toFixed(2)} USD`);
            } catch (error) {
              console.log(`⚠️ 无法获取实时SOL价格，使用缓存价格`);
              usdAmount = solAmount * this.priceService.getCachedSolPrice();
            }

            tokens.push({
              address: tokenAddress,
              action,
              solAmount,
              tokenAmount,
              usdAmount
            });
            
            console.log(`✅ 添加交易: ${String(tokenAddress).slice(0, 8)}... ${action} ${solAmount.toFixed(6)}SOL ${tokenAmount.toFixed(6)}tokens`);
          } else {
            console.log(`⚠️ 跳过无效交易: address=${tokenAddress}, solAmount=${solAmount}`);
          }
        }
      }

      // 🔥 新增：如果没有从事件中得到交易，但有token地址，创建基础记录
      if (tokens.length === 0 && allTokenAddresses.size > 0) {
        console.log(`⚠️ 事件中无有效交易，但发现token地址，创建基础记录...`);
        for (const tokenAddress of allTokenAddresses) {
          // 跳过SOL地址
          if (tokenAddress === 'So11111111111111111111111111111111111111112') {
            continue;
          }
          
          tokens.push({
            address: tokenAddress,
            action: 'unknown',
            solAmount: 0,
            tokenAmount: 0,
            usdAmount: 0
          });
          // console.log(`📝 基础记录: ${tokenAddress.slice(0, 8)}...`);
        }
      }

      console.log(`📊 解析结果: ${tokens.length} 个token交易`);
      return tokens.length > 0 ? { tokens } : null;
      
    } catch (error) {
      console.error('❌ 提取token信息时出错:', error);
      return null;
    }
  }

  /**
   * 处理token交易数据
   */
  private async processTokenTransaction(tokenAddress: string, transaction: any): Promise<void> {
    try {
      console.log(`📊 Token交易数据已记录: ${tokenAddress.slice(0, 8)}... ${transaction.action === 1 ? 'BUY' : 'SELL'} ${transaction.sol_amount.toFixed(4)} SOL`);
      
      // 🔥 关键修复：将市场交易数据传递给subscriptionManager
      const action = transaction.action === 1 ? 'buy' : 'sell';
      const timestamp = transaction.timestamp instanceof Date ? transaction.timestamp : new Date(transaction.timestamp);
      
              await this.subscriptionManager.processWalletTransaction(
        `market_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // 生成唯一的交易ID
        timestamp,
        transaction.block_number || 0,
                tokenAddress,
        action,
        transaction.sol_amount,
        transaction.usd_amount,
        transaction.wallet || 'market_participant'
      );
      
      console.log(`📈 市场交易数据已传递给subscriptionManager: ${tokenAddress.slice(0, 8)}... ${action} ${transaction.sol_amount.toFixed(4)} SOL`);
      
      // 🔥 关键修复：如果token实例存在，直接添加到预测器队列
      const instance = this.tokenInstances.get(tokenAddress);
      if (instance) {
        const featureData = {
          timestamp: timestamp,
          action: transaction.action,
          sol_amount: transaction.sol_amount,
          usd_amount: transaction.usd_amount,
          is_target_wallet: transaction.is_target_wallet || false,
          wallet: transaction.wallet || 'unknown',
          block_number: transaction.block_number || 0
        };
        
        // 添加到预测器队列
        // 🔥 旧预测器已移除，只使用V3
        await this.addTransactionToV4Predictors(instance, featureData);
        
        console.log(`📈 交易数据已添加到V3预测器: ${tokenAddress.slice(0, 8)}... ${transaction.action === 1 ? 'BUY' : 'SELL'} ${transaction.sol_amount.toFixed(4)} SOL`);
        
        // 更新最后处理时间
        if (timestamp > (instance.lastProcessedTransactionTime || new Date(0))) {
          instance.lastProcessedTransactionTime = timestamp;
        }
      }
      
    } catch (error) {
      console.error(`❌ 处理token交易数据失败 (${tokenAddress.slice(0, 8)}...):`, error);
    }
  }

  /**
   * 记录真实价格数据
   */
  /**
   * 🔥 统一的Token数量标准化函数
   */
  private normalizeTokenAmount(tokenAmount: number): number {
    // 🔥 修复：现在GRPC事件处理已经标准化了Token数量，这里只做安全检查
    // 如果数量看起来已经是标准化的（小于1000万），直接返回
    if (tokenAmount < 10_000_000) {
      if (process.env.DEBUG_PRICE_CALCULATION === 'true') {
        console.log(`✅ Token数量已标准化: ${tokenAmount.toFixed(6)} tokens`);
      }
      return tokenAmount;
    }

    // 如果数量仍然很大，说明可能是从其他地方（如交易解析）获得的原始数据
    let normalizedTokenAmount = tokenAmount;

    // 判断是否需要标准化（基于PumpFun的经验值）
    if (tokenAmount > 1_000_000_000_000) {
      normalizedTokenAmount = tokenAmount / 1_000_000; // 除以10^6
      if (process.env.DEBUG_PRICE_CALCULATION === 'true') {
        console.log(`🔧 Token数量标准化: ${tokenAmount.toExponential(3)} -> ${normalizedTokenAmount.toFixed(6)} (除以10^6)`);
      }
    }
    else if (tokenAmount > 10_000_000_000) {
      normalizedTokenAmount = tokenAmount / 1_000_000;
      if (process.env.DEBUG_PRICE_CALCULATION === 'true') {
        console.log(`🔧 Token数量调整: ${tokenAmount.toExponential(3)} -> ${normalizedTokenAmount.toFixed(6)} (除以10^6)`);
      }
    }

    // 防止过度标准化导致数值过小
    if (normalizedTokenAmount < 0.000001) {
      if (process.env.DEBUG_PRICE_CALCULATION === 'true') {
        console.log(`⚠️ Token数量过小，可能标准化过度: ${normalizedTokenAmount}`);
      }
      // 如果标准化后数值过小，使用较少的调整
      normalizedTokenAmount = tokenAmount / 1000; // 只除以10^3
    }

    return normalizedTokenAmount;
  }

  private recordRealPrice(
    tokenAddress: string, 
    solAmount: number, 
    tokenAmount: number, 
    action: 'buy' | 'sell',
    source: 'stream' | 'own_trade' = 'stream',
    transactionTimestamp?: Date
  ): void {
    // 🔥 增强验证：检查输入数据的有效性
    if (tokenAmount <= 0) {
      console.warn(`❌ Token数量无效，跳过价格记录: ${tokenAmount} (SOL: ${solAmount})`);
      return;
    }

    if (solAmount <= 0) {
      console.warn(`❌ SOL数量无效，跳过价格记录: ${solAmount} (Token: ${tokenAmount})`);
      return;
    }

    // 🔥 修复：使用统一的标准化函数
    const normalizedTokenAmount = this.normalizeTokenAmount(tokenAmount);
    
    // 添加额外的验证
    if (normalizedTokenAmount <= 0) {
      console.warn(`❌ 标准化后Token数量无效: ${normalizedTokenAmount} (原始: ${tokenAmount})`);
      return;
    }
    
    const price = solAmount / normalizedTokenAmount;
    
    // 增强的价格调试信息
    if (process.env.DEBUG_PRICE_CALCULATION === 'true') {
      console.log(`💰 价格计算详情:`);
      console.log(`   SOL数量: ${solAmount}`);
      console.log(`   Token原始数量: ${tokenAmount.toExponential(3)}`);
      console.log(`   Token标准化数量: ${normalizedTokenAmount.toFixed(6)}`);
      console.log(`   计算价格: ${price.toExponential(6)} SOL/token`);
      console.log(`   动作: ${action}, 来源: ${source}`);
    }
    
    // 🔥 检测异常价格（调整阈值）
    if (price < 0.000000001 || price > 100) {
      console.warn(`⚠️ 异常价格检测: ${price.toExponential(6)} SOL/token`);
      console.warn(`   SOL: ${solAmount}, Token原始: ${tokenAmount.toExponential(3)}, Token标准化: ${normalizedTokenAmount.toFixed(6)}`);
      console.warn(`   可能原因: Token数量计算错误或SOL数量异常`);
      // 暂时仍然记录，但标记为可疑
    }

    // 🔥 检测重复价格
    if (!this.realPriceData.has(tokenAddress)) {
      this.realPriceData.set(tokenAddress, []);
    }

    const records = this.realPriceData.get(tokenAddress)!;
    
    // 检查最近的价格记录
    if (records.length > 0) {
      const lastPrice = records[records.length - 1].price;
      if (Math.abs(price - lastPrice) < 0.0000000001) {
        console.warn(`🔄 检测到相同价格: ${price.toFixed(12)} = ${lastPrice.toFixed(12)}`);
        console.warn(`   当前: SOL=${solAmount}, Token=${tokenAmount}`);
        console.warn(`   上次: SOL=${records[records.length - 1].solAmount}, Token=${records[records.length - 1].tokenAmount}`);
      }
    }

    const priceRecord = {
      price,
      timestamp: transactionTimestamp || new Date(), // 🔥 使用交易实际时间戳，如果没有则使用当前时间
      solAmount,
      tokenAmount: normalizedTokenAmount, // 使用标准化后的Token数量
      action,
      source
    };

    records.push(priceRecord);

    // 🔥 增加价格记录容量以支持历史数据处理
    if (records.length > 500) {
      records.shift();
    }

    if (process.env.DEBUG_PRICE_RECORDING === 'true') {
      console.log(`📊 记录真实价格: ${tokenAddress.slice(0, 8)}... ${action} ${price.toFixed(10)} SOL/token (SOL: ${solAmount.toFixed(6)}, Token: ${normalizedTokenAmount.toFixed(6)})`);
    };
  }

  /**
   * 更新token订阅
   */
  private async updateTokenSubscriptions(): Promise<void> {
    // 动态订阅token与持仓token联合，确保持仓token持续订阅
    const dynamicTokens = this.subscriptionManager.getActiveTokens();
    const openPositionTokens = this.positionManager.getActivePositions().map(pos => pos.tokenAddress);
    const newTokenSet = new Set<string>([...dynamicTokens, ...openPositionTokens]);

    const tokensToAdd = Array.from(newTokenSet).filter(token => !this.currentTokenSubscriptions.has(token));
    const tokensToRemove = Array.from(this.currentTokenSubscriptions).filter(token => !newTokenSet.has(token));

    if (tokensToAdd.length === 0 && tokensToRemove.length === 0) {
      return;
    }

    console.log(`🔄 更新token订阅: +${tokensToAdd.length} -${tokensToRemove.length}`);
    
    this.currentTokenSubscriptions = new Set(newTokenSet);
    
    if (this.currentStream && (tokensToAdd.length > 0 || tokensToRemove.length > 0)) {
      console.log(`📤 通过stream.write()动态更新订阅...`);
      
      const allIncludeAddresses = [
        TARGET_WALLET,
        MY_WALLET,  // 添加这一行
        ...Array.from(this.currentTokenSubscriptions)
      ];
      
      const updatedSubscription = {
        accounts: {},
        slots: {},
        transactions: {
          pumpFun_updated: {
            vote: false,
            failed: false,
            signature: undefined,
            accountInclude: allIncludeAddresses,
            accountExclude: [],
            accountRequired: [PUMP_FUN_AMM_PROGRAM_ID.toBase58()],
          },
        },
        transactionsStatus: {},
        entry: {},
        blocks: {},
        blocksMeta: {},
        accountsDataSlice: [],
        ping: undefined,
        commitment: CommitmentLevel.PROCESSED,
      };

      this.currentStream.write(updatedSubscription, (err: any) => {
        if (err === null || err === undefined) {
          console.log(`✅ 动态订阅更新成功!`);
          this.stats.subscriptionUpdates++;
        } else {
          console.error(`❌ 动态订阅更新失败:`, err);
        }
      });
    }
  }

  /**
   * 🔥 AI预测和交易决策 - 核心逻辑
   */
  private async makePredictionForToken(tokenAddress: string): Promise<void> {
    // 🔥 添加token级别的预测锁，防止并发调用
    if (this.predictionLocks.has(tokenAddress)) {
      if (process.env.DEBUG_PREDICTION_LOCKS === 'true') {
      console.log(`🔒 Token预测已在进行中，跳过重复调用: ${tokenAddress.slice(0, 8)}...`);
      }
      return;
    }

    // 🔥 新增：预测频率限制，避免过于频繁的预测
    const minPredictionInterval = 500; // 500毫秒最小间隔
    const lastPredictionTime = this.lastPredictionTime.get(tokenAddress);
    if (lastPredictionTime && (Date.now() - lastPredictionTime.getTime()) < minPredictionInterval) {
      if (process.env.DEBUG_PREDICTION_LOCKS === 'true') {
        const remainingTime = Math.ceil(minPredictionInterval - (Date.now() - lastPredictionTime.getTime()));
        console.log(`⏰ Token预测频率限制: ${tokenAddress.slice(0, 8)}... (还需等待${remainingTime}ms)`);
      }
      return;
    }
    
    // 设置预测锁
    this.predictionLocks.add(tokenAddress);
    const predictionStartTime = performance.now(); // 使用高精度时间
    
    // 🔥 性能监控变量声明（移到try块外）
    let instanceTime = 0, displayTime = 0, dataTime = 0, queueCheckTime = 0, 
        positionCheckTime = 0, totalAiTime = 0, tradeDecisionTime = 0;
    
    try {
      
      // 🔥 性能监控：获取或创建token实例
      const instanceStart = performance.now();
      let instance = this.tokenInstances.get(tokenAddress);
      
      if (!instance) {
        const createStart = performance.now();
        await this.manageTokenMonitoringQueue(tokenAddress);
        const manageQueueTime = performance.now() - createStart;
        
        const createInstanceStart = performance.now();
        instance = await this.createTokenInstance(tokenAddress);
        
        
        const createInstanceTime = performance.now() - createInstanceStart;
        
        this.tokenInstances.set(tokenAddress, instance);
        const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
        console.log(`🆕 创建Token交易实例: ${tokenDisplay}`);
        
        const initStart = performance.now();
        await this.initializeTokenPredictorData(tokenAddress, instance);
        const initTime = performance.now() - initStart;
        
        console.log(`⏱️ 实例创建性能:`);
        console.log(`   📋 队列管理: ${manageQueueTime.toFixed(3)}ms`);
        console.log(`   🏗️ 实例创建: ${createInstanceTime.toFixed(3)}ms`);
        console.log(`   🔄 数据初始化: ${initTime.toFixed(3)}ms`);
      }
      
              instanceTime = performance.now() - instanceStart;
      
      if (process.env.DEBUG_PREDICTION_PERFORMANCE === 'true') {
        console.log(`   🏭 实例准备总耗时: ${instanceTime.toFixed(3)}ms`);
      }

      if (!instance.isActive) return;

      // 🔥 性能监控：Token显示信息获取
      const displayStart = performance.now();
      const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
      displayTime = performance.now() - displayStart;
      
      if (process.env.DEBUG_PREDICTION_PERFORMANCE === 'true') {
        console.log(`   📝 Token显示获取: ${displayTime.toFixed(3)}ms`);
      }
      
      // 🔥 性能监控：获取新的交易数据
      const dataStart = performance.now();
      const newTransactions = this.getNewTransactionsForToken(tokenAddress, instance);
      dataTime = performance.now() - dataStart;
      
      if (process.env.DEBUG_PREDICTION_PERFORMANCE === 'true') {
      console.log(`📋 获取新交易数据 ${tokenDisplay}:`);
      console.log(`   📊 新交易数量: ${newTransactions.length}`);
      console.log(`   ⏰ 上次处理时间: ${instance.lastProcessedTransactionTime || '从未处理'}`);
        console.log(`   ⏱️ 数据获取耗时: ${dataTime.toFixed(3)}ms`);
      }
      
      if (newTransactions.length > 0) {
        console.log(`   ✅ 开始添加${newTransactions.length}笔新交易到预测器...`);
        const addTransactionStart = performance.now();
        
        for (const tx of newTransactions) {
          const featureStart = performance.now();
          const featureData = {
            timestamp: tx.timestamp instanceof Date ? tx.timestamp : new Date(tx.timestamp),
            action: tx.action === 'buy' ? 1 : tx.action === 'sell' ? 0 : 2,
            sol_amount: tx.solAmount,
            usd_amount: tx.usdAmount,
            is_target_wallet: (tx.wallet || '').includes(TARGET_WALLET.slice(0, 8)),
            wallet: tx.wallet || 'unknown',
            block_number: tx.slot
          };
          const featureTime = performance.now() - featureStart;
          
          const addStart = performance.now();
          // 🔥 只添加到V4预测器 - 修复价格计算，使用SOL/Token比率
          // 尝试从realPriceData获取正确的价格（SOL per Token）
          let correctPrice: number | undefined = undefined;
          const tokenAddress = tx.tokenAddress;
          
          // 详细记录价格获取过程
          if (!tokenAddress) {
            console.warn(`   ❌ 跳过交易：tokenAddress 为空`);
            continue;
          }
          
          if (!this.realPriceData.has(tokenAddress)) {
            // 尝试从交易数据本身计算价格
            if (tx.solAmount > 0 && tx.tokenAmount > 0) {
              correctPrice = tx.solAmount / tx.tokenAmount;
              if (process.env.DEBUG_PRICE_MATCHING === 'true') {
                console.log(`   💡 实时交易使用内嵌价格: ${correctPrice.toFixed(10)} SOL/token`);
              }
            } else {
              if (process.env.DEBUG_PRICE_MATCHING === 'true') {
                console.warn(`   ❌ 跳过交易：realPriceData 中没有 token ${tokenAddress.slice(0, 8)}... 的价格记录`);
                console.warn(`   📊 当前 realPriceData 中共有 ${this.realPriceData.size} 个token的价格数据`);
              }
              continue;
            }
          } else {
            const priceHistory = this.realPriceData.get(tokenAddress)!;
            if (priceHistory.length === 0) {
              // 尝试从交易数据计算价格
              if (tx.solAmount > 0 && tx.tokenAmount > 0) {
                correctPrice = tx.solAmount / tx.tokenAmount;
                if (process.env.DEBUG_PRICE_MATCHING === 'true') {
                  console.log(`   💡 价格历史为空，使用交易内嵌价格: ${correctPrice.toFixed(10)} SOL/token`);
                }
              } else {
                if (process.env.DEBUG_PRICE_MATCHING === 'true') {
                  console.warn(`   ❌ 跳过交易：token ${tokenAddress}... 的价格历史为空且无法计算价格`);
                }
                continue;
              }
            } else {
              // 🔥 修复：查找最接近时间的价格数据
              const targetTime = featureData.timestamp.getTime();

              // 先按时间差排序，找到最接近的价格记录
              const sortedPrices = [...priceHistory].sort((a, b) =>
                Math.abs(a.timestamp.getTime() - targetTime) - Math.abs(b.timestamp.getTime() - targetTime)
              );

              const nearestPrice = sortedPrices[0];
              const timeDiff = Math.abs(nearestPrice.timestamp.getTime() - targetTime);

              // 🔥 扩展时间窗口以支持历史数据处理
              const timeWindowMs = 120000; // 2分钟窗口
              if (timeDiff > timeWindowMs) {
                console.warn(`   ❌ 跳过交易：token ${tokenAddress}... 在${timeWindowMs/1000}秒内没有价格数据`);
                console.warn(`   📅 交易时间: ${featureData.timestamp.toLocaleString()}`);
                console.warn(`   📅 最近价格时间: ${nearestPrice.timestamp.toLocaleString()}`);
                console.warn(`   ⏰ 时间差: ${(timeDiff / 1000).toFixed(1)}秒`);
                console.warn(`   📊 该token共有 ${priceHistory.length} 条价格记录`);

                // 🔥 额外调试信息：显示价格记录的时间范围
                if (priceHistory.length > 0) {
                  const oldestPrice = priceHistory[0];
                  const newestPrice = priceHistory[priceHistory.length - 1];
                  console.warn(`   📊 价格记录时间范围: ${oldestPrice.timestamp.toLocaleString()} ~ ${newestPrice.timestamp.toLocaleString()}`);
                }

                // 🔥 新增：触发token清理流程
                console.warn(`   🗑️ 触发清理流程：token ${tokenAddress.slice(0, 8)}... 价格数据过期`);
                await this.cleanupStaleToken(tokenAddress, '120秒内无价格数据');

                continue;
              }

              const closestPrice = nearestPrice;
              correctPrice = closestPrice.price;

              // 🔥 简化价格调试信息
              if (process.env.DEBUG_PRICE_MATCHING === 'true') {
                console.log(`   ✅ 使用实时价格数据: ${correctPrice.toFixed(10)} SOL/token`);
                console.log(`   📅 价格时间: ${closestPrice.timestamp.toLocaleString()}`);
                console.log(`   📅 交易时间: ${featureData.timestamp.toLocaleString()}`);
                console.log(`   ⏰ 时间差: ${(timeDiff / 1000).toFixed(1)}秒`);
                console.log(`   💰 原始数据: SOL=${closestPrice.solAmount.toFixed(6)}, Token=${closestPrice.tokenAmount.toFixed(6)}, 动作=${closestPrice.action}`);
              }
            }
          }
          
          // 🔥 修复USD金额计算
          let usdAmountForV4 = featureData.usd_amount || 0;
          if (usdAmountForV4 <= 0 && featureData.sol_amount > 0) {
            try {
              usdAmountForV4 = await this.priceService.convertSolToUsd(featureData.sol_amount);
            } catch (error) {
              usdAmountForV4 = featureData.sol_amount * this.priceService.getCachedSolPrice();
            }
          }

          const v4BuyTransactionData: BuyTransactionDataV4 = {
            timestamp: featureData.timestamp,
            transaction_type: featureData.action === 1 ? 'buy' : featureData.action === 0 ? 'sell' : 'buy',
            sol_amount: featureData.sol_amount,
            usd_amount: usdAmountForV4
          };
          
          const v4SellTransactionData: TransactionDataForSellV4 = {
            timestamp: featureData.timestamp,
            transaction_type: featureData.action === 1 ? 'buy' : featureData.action === 0 ? 'sell' : 'buy',
            sol_amount: featureData.sol_amount
          };
          
          instance.buyPredictorV4.addTransaction(v4BuyTransactionData);
          instance.sellPredictorV4.addTransaction(v4SellTransactionData);
          
          // 🔥 V4预测器都需要价格数据
          const v4PriceData: PriceDataV4 = {
            timestamp: featureData.timestamp,
            price: correctPrice || 0
          };
          instance.buyPredictorV4.addPriceData(v4PriceData);
          instance.sellPredictorV4.addPriceData(v4PriceData);
          const addTime = performance.now() - addStart;
          
          if (process.env.DEBUG_V4_TRANSACTIONS === 'true') {
            if (process.env.DEBUG_PREDICTION_PERFORMANCE === 'true') {
              console.log(`   📈 添加交易数据: ${tx.action} ${tx.solAmount}SOL (特征:${featureTime.toFixed(3)}ms, 添加:${addTime.toFixed(3)}ms)`);
            } else {
              console.log(`   📈 添加交易数据: ${tx.action} ${tx.solAmount}SOL @${featureData.timestamp.toLocaleTimeString()}`);
            }
          }
        }
        
        const totalAddTime = performance.now() - addTransactionStart;
        if (process.env.DEBUG_PREDICTION_PERFORMANCE === 'true') {
          console.log(`   ⏱️ 添加交易总耗时: ${totalAddTime.toFixed(3)}ms (平均: ${(totalAddTime / newTransactions.length).toFixed(3)}ms/笔)`);
        }
        
        if (newTransactions.length > 0) {
          const latestTransaction = newTransactions[newTransactions.length - 1];
          instance.lastProcessedTransactionTime = latestTransaction.timestamp;
          if (process.env.DEBUG_V4_TRANSACTIONS === 'true') {
            console.log(`   🕒 更新处理时间戳: ${latestTransaction.timestamp}`);
          }
        }
      } else {
        console.log(`   ⚠️ 没有新交易数据需要添加`);
        
        // 🔥 新增：检查是否需要强制初始化历史数据 (使用V4预测器)
        const buyQueueBefore = instance.buyPredictorV4.getQueueStatus();
        const sellQueueBefore = instance.sellPredictorV4.getQueueStatus();
        
        if (buyQueueBefore.currentSize === 0 || sellQueueBefore.currentSize === 0) {
          console.log(`   🔄 检测到V4预测器为空，尝试强制初始化历史数据...`);
          console.log(`   📊 V4预测器状态: 买入${buyQueueBefore.currentSize}笔, 卖出${sellQueueBefore.currentSize}笔`);
          
          // 强制重新初始化
          await this.initializeTokenPredictorData(tokenAddress, instance);
          console.log(`   ✅ 强制初始化完成`);
        }
      }

      // 🔥 性能监控：检查是否有足够的数据进行预测 (使用V4预测器)
      const queueCheckStart = performance.now();
      const buyQueueStatus = instance.buyPredictorV4.getQueueStatus();
      const sellQueueStatus = instance.sellPredictorV4.getQueueStatus();
      queueCheckTime = performance.now() - queueCheckStart;
      
      if (process.env.DEBUG_PREDICTION_PERFORMANCE === 'true') {
        console.log(`   📊 队列状态检查耗时: ${queueCheckTime.toFixed(3)}ms`);
      }
      if (process.env.DEBUG_V4_QUEUE_STATUS === 'true') {
        console.log(`   📈 买入队列: ${buyQueueStatus.currentSize}/${buyQueueStatus.maxSize} (可预测: ${buyQueueStatus.canPredict})`);
        console.log(`   📉 卖出队列: ${sellQueueStatus.currentSize}/${sellQueueStatus.maxSize} (可预测: ${sellQueueStatus.canPredict})`);
      }
      
      if (!buyQueueStatus.canPredict || !sellQueueStatus.canPredict) {
        console.log(`⚠️ Token ${tokenDisplay} 数据不足，跳过预测`);
      return;
    }

      // 🔥 性能监控：根据持仓状态决定预测类型
      const positionCheckStart = performance.now();
      const hasInstancePosition = instance.currentHolding !== null && instance.currentHolding.amount > 0;
      const hasManagerPosition = this.positionManager.getPosition(tokenAddress) !== undefined;
      const hasPosition = hasInstancePosition || hasManagerPosition;
      positionCheckTime = performance.now() - positionCheckStart;
      
      if (process.env.DEBUG_POSITION_CHECKS === 'true') {
        console.log(`💼 持仓状态检查 ${tokenDisplay} (${positionCheckTime.toFixed(3)}ms):`);
        console.log(`   📊 Instance持仓: ${hasInstancePosition ? '✅ 有' : '❌ 无'}`);
        console.log(`   📊 Manager持仓: ${hasManagerPosition ? '✅ 有' : '❌ 无'}`);
        console.log(`   📊 综合持仓: ${hasPosition ? '✅ 有持仓' : '❌ 无持仓'}`);
      }
      
      // 进行AI预测 - 根据持仓状态优化
      try {
        let buyPredictionResult = { probability: 0.0, prediction: false, confidence: 0.0, timestamp: new Date(), predictionTimeMs: 0 };
        let sellPredictionResult = { probability: 0.0, prediction: false, confidence: 0.0, timestamp: new Date(), predictionTimeMs: 0 };
        let buyPredictionTime = 0;
        let sellPredictionTime = 0;
        
        if (hasPosition) {
          // 有持仓：只做卖出预测
          console.log(`🔍 有持仓，执行卖出预测...`);
          
          // 🔥 启用卖出预测器的性能调试
          process.env.DEBUG_PREDICTION_PERFORMANCE = 'true';
          
          const sellPredictionStart = performance.now();
          // 🔥 使用V4预测器进行卖出预测
          const sellPredictionV4Result = await instance.sellPredictorV4.predictSell();
          sellPredictionResult = {
            probability: sellPredictionV4Result.probability,
            prediction: sellPredictionV4Result.prediction,
            confidence: sellPredictionV4Result.confidence,
            timestamp: sellPredictionV4Result.timestamp,
            predictionTimeMs: sellPredictionV4Result.predictionTimeMs
          };

          // 🔍 调试：检查极端概率值
          if (sellPredictionV4Result.probability > 0.9 || sellPredictionV4Result.probability < 0.1) {
            console.log(`⚠️ 检测到极端卖出概率: ${(sellPredictionV4Result.probability * 100).toFixed(3)}%`);
            console.log(`   📊 置信度: ${(sellPredictionV4Result.confidence * 100).toFixed(1)}%`);
            console.log(`   🕐 预测时间: ${sellPredictionV4Result.predictionTimeMs.toFixed(3)}ms`);
          }
          sellPredictionTime = performance.now() - sellPredictionStart;
        
        // 🔥 存储最近的卖出预测结果（probability已经是0-1的小数）
        instance.lastSellPrediction = sellPredictionResult.probability;
          
          // 🔥 重置调试标志
          delete process.env.DEBUG_PREDICTION_PERFORMANCE;
          
          console.log(`⏱️ AI预测性能 ${tokenDisplay} (仅卖出):`);
          console.log(`   📉 卖出预测: ${sellPredictionTime.toFixed(3)}ms (${(sellPredictionResult.probability * 100).toFixed(3)}%)`);
          console.log(`   📈 买入预测: 跳过 (有持仓)`);
          console.log(`   🎯 AI总耗时: ${sellPredictionTime.toFixed(3)}ms`);
        } else {
          // 无持仓：只做买入预测
          console.log(`🔍 无持仓，执行买入预测...`);
          const buyPredictionStart = performance.now();
          // 🔥 使用V4预测器进行买入预测
          const buyPredictionV4Result = await instance.buyPredictorV4.predictBuy();
          buyPredictionResult = {
            probability: buyPredictionV4Result.probability,
            prediction: buyPredictionV4Result.prediction,
            confidence: buyPredictionV4Result.confidence,
            timestamp: buyPredictionV4Result.timestamp,
            predictionTimeMs: buyPredictionV4Result.predictionTimeMs
          };

          // 🔍 调试：检查极端概率值
          if (buyPredictionV4Result.probability > 0.9 || buyPredictionV4Result.probability < 0.1) {
            console.log(`⚠️ 检测到极端买入概率: ${(buyPredictionV4Result.probability * 100).toFixed(3)}%`);
            console.log(`   📊 置信度: ${(buyPredictionV4Result.confidence * 100).toFixed(1)}%`);
            console.log(`   🕐 预测时间: ${buyPredictionV4Result.predictionTimeMs.toFixed(3)}ms`);
          }
          buyPredictionTime = performance.now() - buyPredictionStart;
          
          // 🔥 存储最近的买入预测结果（probability已经是0-1的小数）
          instance.lastBuyPrediction = buyPredictionResult.probability;
          
          console.log(`⏱️ AI预测性能 ${tokenDisplay} (仅买入):`);
          console.log(`   📈 买入预测: ${buyPredictionTime.toFixed(3)}ms (${(buyPredictionResult.probability * 100).toFixed(3)}%)`);
          console.log(`   📉 卖出预测: 跳过 (无持仓)`);
          console.log(`   🎯 AI总耗时: ${buyPredictionTime.toFixed(3)}ms`);
        }
        
        totalAiTime = buyPredictionTime + sellPredictionTime;
        
        if (totalAiTime > 1000) { // 超过1秒
          console.warn(`⚠️ AI预测耗时异常: ${totalAiTime.toFixed(3)}ms`);
        }
        
        // 🔥 执行真实交易决策
        const tradeDecisionStart = performance.now();
        await this.executeTradeDecision(tokenAddress, buyPredictionResult.probability, sellPredictionResult.probability, instance);
        tradeDecisionTime = performance.now() - tradeDecisionStart;
        
        console.log(`   🔄 交易决策耗时: ${tradeDecisionTime.toFixed(3)}ms`);
        
        if (tradeDecisionTime > 5000) { // 超过5秒
          console.warn(`⚠️ 交易决策耗时异常: ${tradeDecisionTime.toFixed(3)}ms`);
        }
        
        instance.lastPrediction = new Date();
        
      } catch (error) {
        console.error(`❌ AI预测错误 ${tokenDisplay}:`, error);
      }
    } finally {
      // 🔥 释放预测锁并记录总执行时间
      const totalExecutionTime = performance.now() - predictionStartTime;
      this.predictionLocks.delete(tokenAddress);
      this.lastPredictionTime.set(tokenAddress, new Date());
      
      console.log(`🔓 预测完成 ${tokenAddress.slice(0, 8)}... 总耗时: ${totalExecutionTime.toFixed(3)}ms`);
      
      // 🔥 详细性能分析（仅在调试模式下）
      if (process.env.DEBUG_PREDICTION_PERFORMANCE === 'true') {
        console.log(`📊 性能分析详情:`);
        console.log(`   🏭 实例准备: ${instanceTime?.toFixed(3) || '0.000'}ms`);
        console.log(`   📝 Token显示: ${displayTime?.toFixed(3) || '0.000'}ms`);
        console.log(`   📋 数据获取: ${dataTime?.toFixed(3) || '0.000'}ms`);
        console.log(`   📊 队列检查: ${queueCheckTime?.toFixed(3) || '0.000'}ms`);
        console.log(`   💼 持仓检查: ${positionCheckTime?.toFixed(3) || '0.000'}ms`);
        console.log(`   🤖 AI预测: ${totalAiTime?.toFixed(3) || '0.000'}ms`);
        console.log(`   🔄 交易决策: ${tradeDecisionTime?.toFixed(3) || '0.000'}ms`);
        
        const otherTime = totalExecutionTime - (instanceTime || 0) - (displayTime || 0) - 
                         (dataTime || 0) - (queueCheckTime || 0) - (positionCheckTime || 0) - 
                         (totalAiTime || 0) - (tradeDecisionTime || 0);
        console.log(`   ❓ 其他开销: ${otherTime.toFixed(3)}ms`);
      }
      
      if (totalExecutionTime > 10000) { // 超过10秒
        console.error(`🚨 预测执行时间严重异常: ${totalExecutionTime.toFixed(3)}ms`);
      } else if (totalExecutionTime > 1000) { // 超过1秒
        console.warn(`⚠️ 预测执行时间较长: ${totalExecutionTime.toFixed(3)}ms`);
      }
    }
  }

  /**
   * 🔥 新增：诊断价格数据和时间同步问题
   */
  public diagnosePriceDataIssues(tokenAddress?: string): void {
    console.log('\n🔍 ===== 价格数据诊断报告 =====');
    
    if (tokenAddress) {
      // 诊断特定token
      this.diagnoseSingleTokenPriceData(tokenAddress);
    } else {
      // 诊断所有token
      console.log(`📊 总计监控 ${this.realPriceData.size} 个token的价格数据`);
      
      for (const [address, priceHistory] of this.realPriceData.entries()) {
        console.log(`\n🪙 Token: ${address.slice(0, 8)}...`);
        this.diagnoseSingleTokenPriceData(address);
      }
    }
    
    console.log('\n🔍 ===== 诊断完成 =====\n');
  }
  
  private diagnoseSingleTokenPriceData(tokenAddress: string): void {
    const priceHistory = this.realPriceData.get(tokenAddress);
    if (!priceHistory) {
      console.log(`❌ 没有找到token ${tokenAddress.slice(0, 8)}... 的价格数据`);
      return;
    }
    
    if (priceHistory.length === 0) {
      console.log(`❌ Token ${tokenAddress.slice(0, 8)}... 的价格历史为空`);
      return;
    }
    
    // 分析价格数据
    const sortedPrices = [...priceHistory].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    const firstPrice = sortedPrices[0];
    const lastPrice = sortedPrices[sortedPrices.length - 1];
    const currentTime = new Date();
    
    console.log(`📈 价格记录统计:`);
    console.log(`   📊 总记录数: ${priceHistory.length}`);
    console.log(`   📅 最早记录: ${firstPrice.timestamp.toLocaleString()}`);
    console.log(`   📅 最新记录: ${lastPrice.timestamp.toLocaleString()}`);
    console.log(`   📅 当前时间: ${currentTime.toLocaleString()}`);
    console.log(`   ⏰ 最新记录距现在: ${((currentTime.getTime() - lastPrice.timestamp.getTime()) / 1000).toFixed(1)}秒`);
    
    // 分析价格范围
    const prices = priceHistory.map(p => p.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length;
    
    console.log(`💰 价格统计:`);
    console.log(`   🔻 最低价: ${minPrice.toFixed(12)} SOL/token`);
    console.log(`   🔺 最高价: ${maxPrice.toFixed(12)} SOL/token`);
    console.log(`   📊 平均价: ${avgPrice.toFixed(12)} SOL/token`);
    
    // 检查价格重复
    const uniquePrices = new Set(prices.map(p => p.toFixed(12)));
    console.log(`🔄 价格唯一性:`);
    console.log(`   🎯 唯一价格数: ${uniquePrices.size}/${prices.length}`);
    if (uniquePrices.size < prices.length) {
      console.log(`   ⚠️ 检测到重复价格！`);
    }
    
    // 检查时间分布
    const timeSpread = lastPrice.timestamp.getTime() - firstPrice.timestamp.getTime();
    console.log(`⏱️ 时间分布:`);
    console.log(`   📏 时间跨度: ${(timeSpread / 1000).toFixed(1)}秒`);
    console.log(`   📈 平均间隔: ${(timeSpread / (priceHistory.length - 1) / 1000).toFixed(2)}秒`);
    
    // 检查最近30秒内的数据
    const thirtySecondsAgo = currentTime.getTime() - 30000;
    const recentPrices = priceHistory.filter(p => p.timestamp.getTime() > thirtySecondsAgo);
    console.log(`🕐 最近30秒数据:`);
    console.log(`   📊 记录数: ${recentPrices.length}`);
    
    if (recentPrices.length > 0) {
      const newestInRange = recentPrices.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];
      console.log(`   📅 最新记录: ${newestInRange.timestamp.toLocaleString()}`);
      console.log(`   💰 最新价格: ${newestInRange.price.toFixed(12)} SOL/token`);
    } else {
      console.log(`   ❌ 最近30秒内没有价格数据`);
      
      // 找到最接近的价格记录
      const closest = priceHistory.sort((a, b) => 
        Math.abs(a.timestamp.getTime() - currentTime.getTime()) - 
        Math.abs(b.timestamp.getTime() - currentTime.getTime())
      )[0];
      
      const timeDiff = Math.abs(closest.timestamp.getTime() - currentTime.getTime());
      console.log(`   🔍 最接近的记录:`);
      console.log(`     📅 时间: ${closest.timestamp.toLocaleString()}`);
      console.log(`     ⏰ 时间差: ${(timeDiff / 1000).toFixed(1)}秒`);
      console.log(`     💰 价格: ${closest.price.toFixed(12)} SOL/token`);
    }
  }

// ... existing code ...
  private async executeTradeDecision(
    tokenAddress: string, 
    buyPrediction: number, 
    sellPrediction: number, 
    instance: TokenTradingInstance
  ): Promise<void> {
    // 🔥 格式化Token显示信息
    const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
    
    // 🔥 首先输出当前持仓情况
    this.printCurrentPositions();
    
    // 🔥 多重持仓检查 - 防止重复买入
    const hasInstancePosition = instance.currentHolding !== null && instance.currentHolding.amount > 0;
    const hasManagerPosition = this.positionManager.getPosition(tokenAddress) !== undefined;
    const hasPosition = hasInstancePosition || hasManagerPosition;

    // 🔥 新增：低交易量自动卖出检查（仅对持仓token生效）
    if (hasPosition && this.tradingConfig.lowVolumeAutoSell) {
      const recentTxCount = this.getRecentTransactionCount(
                tokenAddress,
        this.tradingConfig.lowVolumeCheckMinutes
      );
      
      if (recentTxCount < this.tradingConfig.lowVolumeThreshold) {
        console.log(`🔥 低交易量自动卖出触发 ${tokenDisplay}`);
        console.log(`   最近${this.tradingConfig.lowVolumeCheckMinutes}分钟交易量: ${recentTxCount} < ${this.tradingConfig.lowVolumeThreshold}`);
        console.log(`   🚨 强制执行卖出操作，无需AI预测确认`);
        
        // 🔥 获取卖出数量（优先从PositionManager）
        let lowVolumeSellAmount = 0;
        const managerPosition = this.positionManager.getPosition(tokenAddress);
        if (managerPosition && managerPosition.amount > 0) {
          lowVolumeSellAmount = managerPosition.amount;
        } else if (instance.currentHolding && instance.currentHolding.amount > 0) {
          lowVolumeSellAmount = instance.currentHolding.amount;
        }
        
        if (lowVolumeSellAmount > 0) {
          instance.isTrading = true;
          console.log(`🔒 设置交易锁: 低交易量强制卖出开始 ${tokenDisplay}`);
          console.log(`📊 卖出数量: ${lowVolumeSellAmount.toFixed(6)} tokens`);
          console.log(`📋 数据源: ${managerPosition ? 'PositionManager' : 'TokenInstance'}`);
          
          try {
            // 🔥 新增：检查必要的池子数据
            if (!instance.realPoolData && !instance.poolData) {
              console.log(`⚠️ 缺少池子数据，尝试从缓存获取...`);
              try {
                const tokenMint = new PublicKey(tokenAddress);
                instance.poolData = await this.createPoolDataFromCache(tokenMint);
                console.log(`✅ 从缓存获取池子数据成功`);
              } catch (poolError) {
                console.error(`❌ 获取池子数据失败:`, poolError);
                console.log(`⏳ 跳过本次低交易量卖出，等待池子数据准备`);
                return;
              }
            }
            
            console.log(`🔄 开始执行低交易量强制卖出...`);
            const result = await this.executeRealSell(tokenAddress, lowVolumeSellAmount, 0); // sellPrediction设为0表示强制卖出
            
            console.log(`📋 卖出结果:`, {
              success: result.success,
              actualAmount: result.actualAmount,
              actualPrice: result.actualPrice,
              error: result.error
            });
            
            if (result.success) {
              console.log(`✅ 低交易量强制卖出成功: ${result.actualAmount?.toFixed(4)} SOL`);
              
              // 发送Telegram通知
              await this.telegramNotifier.notifySystem(
                `🔥 低交易量自动卖出\n` +
                `Token: ${tokenDisplay}\n` +
                `原因: 最近${this.tradingConfig.lowVolumeCheckMinutes}分钟仅${recentTxCount}笔交易\n` +
                `卖出: ${lowVolumeSellAmount.toFixed(6)} tokens\n` +
                `收到: ${result.actualAmount?.toFixed(4)} SOL`
              );
              
              // 🔥 完整的状态清理和记录
              const profit = (result.actualAmount || 0) - (instance.currentHolding?.buySolAmount || 0);
              
              // 更新风险管理器
              this.riskManager.recordTrade(profit);
              
              // 关闭PositionManager中的仓位
              const closedPosition = this.positionManager.closePosition(tokenAddress, result.actualPrice || 0, profit);
              console.log(`📊 关闭仓位: ${closedPosition ? '✅ 成功' : '❌ 失败'}`);
              
              // 更新实例状态
              instance.stats.currentPosition = 0;
              instance.stats.totalTrades++;
              instance.stats.totalPnL += profit;
              if (profit > 0) {
                instance.stats.successfulTrades++;
              }
              instance.currentHolding = null;
              instance.lastConfirmedSellTime = new Date();
              
              // 记录交易历史
              instance.tradingHistory.push({
                type: 'sell',
                timestamp: new Date(),
                tokenAmount: lowVolumeSellAmount,
                solAmount: result.actualAmount || 0,
                price: result.actualPrice || 0,
                prediction: 0, // 强制卖出
                bundleId: result.bundleId
              });
              
              console.log(`✅ 低交易量卖出完成，准备移除监控...`);
              await this.removeTokenFromMonitoring(tokenAddress, '低交易量自动卖出完成');
              
            } else {
              console.log(`❌ 低交易量强制卖出失败: ${result.error}`);
              
              // 发送失败通知
              await this.telegramNotifier.notifySystem(
                `❌ 低交易量自动卖出失败\n` +
                `Token: ${tokenDisplay}\n` +
                `原因: ${result.error}\n` +
                `将继续监控该token`
              );
            }
            
          } catch (error) {
            console.error(`❌ 低交易量强制卖出错误:`, error);
            
            // 发送错误通知
            await this.telegramNotifier.notifySystem(
              `⚠️ 低交易量自动卖出异常\n` +
              `Token: ${tokenDisplay}\n` +
              `错误: ${error instanceof Error ? error.message : String(error)}`
            );
            
          } finally {
            instance.isTrading = false;
            console.log(`🔓 释放交易锁: 低交易量强制卖出结束 ${tokenDisplay}`);
          }
        } else {
          console.log(`⚠️ 低交易量检测到但卖出数量为0:`);
          console.log(`   PositionManager数量: ${managerPosition?.amount || 0}`);
          console.log(`   TokenInstance数量: ${instance.currentHolding?.amount || 0}`);
        }
        
        return; // 完成低交易量卖出后直接返回，不继续常规交易逻辑
      } else {
        console.log(`✅ [${tokenDisplay}] 交易量充足: ${recentTxCount} >= ${this.tradingConfig.lowVolumeThreshold} (最近${this.tradingConfig.lowVolumeCheckMinutes}分钟)`);

    // ��� 检查是否有未确认的卖出交易
    const hasPendingSell = Array.from(instance.pendingTransactions.values()).some(
      tx => tx.type === "sell" && !tx.confirmed
    );
    
    // ��� 检查是否有未确认的买入交易
    const hasPendingBuy = Array.from(instance.pendingTransactions.values()).some(
      tx => tx.type === "buy" && !tx.confirmed
    );

    if (process.env.DEBUG_TRADING_DECISIONS === "true") {
      console.log(`��� 交易决策分析 ${tokenDisplay}:`);
      console.log(`   ��� Instance持仓状态: ${hasInstancePosition ? "✅ 有持仓" : "❌ 无持仓"}`);
      console.log(`   ��� Manager持仓状态: ${hasManagerPosition ? "✅ 有持仓" : "❌ 无持仓"}`);
      console.log(`   ��� 综合持仓状态: ${hasPosition ? "✅ 有持仓" : "❌ 无持仓"}`);
      console.log(`   ��� Pending买入: ${hasPendingBuy ? "✅ 有未确认买入" : "❌ 无"}`);
      console.log(`   ��� Pending卖出: ${hasPendingSell ? "✅ 有未确认卖出" : "❌ 无"}`);
      if (hasInstancePosition && instance.currentHolding) {
        console.log(`   ��� 持仓数量: ${instance.currentHolding.amount.toFixed(2)} tokens`);
        console.log(`   ��� 买入价格: ${instance.currentHolding.buyPrice.toFixed(8)} SOL/token`);
        console.log(`   ��� 买入时间: ${instance.currentHolding.buyTime.toISOString()}`);
      }
    }
      }
    }
    
    // 🔥 检查交易锁状态
    if (instance.isTrading) {
      console.log(`🔒 交易锁定中，跳过交易决策 ${tokenDisplay}`);
      return;
    }
    
    // 🔥 额外的安全检查 - 防止在持仓状态下触发买入
    if (hasPosition && buyPrediction >= this.tradingConfig.buyThreshold) {
      console.log(`⚠️ 安全检查: 已有持仓但检测到买入信号，可能存在状态不一致`);
      console.log(`   buyPrediction: ${(buyPrediction*100).toFixed(1)}%`);
      console.log(`   买入阈值: ${(this.tradingConfig.buyThreshold*100).toFixed(1)}%`);
      console.log(`   🛡️ 阻止重复买入，等待卖出信号`);
      return; // 直接返回，避免重复买入
    }
    
    // 🔥 交易间隔检查 - 防止过快连续交易
    const timeSinceLastTrade = Date.now() - instance.lastTradeTime.getTime();
    const MIN_TRADE_INTERVAL = 1000; // 增加到15秒最小间隔
    if (timeSinceLastTrade < MIN_TRADE_INTERVAL) {
      console.log(`⏳ 交易间隔检查: 距离上次交易仅${Math.floor(timeSinceLastTrade/1000)}秒，需等待${Math.ceil((MIN_TRADE_INTERVAL - timeSinceLastTrade)/1000)}秒`);
      return;
    }
    
    // 🔥 风险管理检查
    const riskCheck = this.riskManager.canTrade();
    if (!riskCheck.allowed) {
      console.log(`🛡️ 风险管理阻止交易: ${riskCheck.reason}`);
      
      // 发送暂停通知
      await this.telegramNotifier.notifySystem(
        `🔴 交易已暂停\n原因: ${riskCheck.reason}\n\n📊 当前统计:\n${this.formatRiskStats()}`
      );
      return;
    }
    
    // 🔥 检查止损条件
    if (hasPosition && instance.currentHolding) {
      const currentPrice = this.getLastTradePrice(tokenAddress);
      const position = this.positionManager.getPosition(tokenAddress);

      console.log(`🔍 止损检查 ${tokenDisplay}:`);
      console.log(`   当前价格: ${currentPrice ? currentPrice.toFixed(8) : 'N/A'}`);
      console.log(`   买入价格: ${instance.currentHolding.buyPrice.toFixed(8)}`);
      console.log(`   止损价格: ${position?.stopLossPrice?.toFixed(8) || 'N/A'}`);
      console.log(`   PositionManager仓位: ${position ? '存在' : '不存在'}`);

      if (currentPrice) {
        const priceChange = ((currentPrice / instance.currentHolding.buyPrice - 1) * 100);
        console.log(`   价格变化: ${priceChange > 0 ? '+' : ''}${priceChange.toFixed(2)}%`);

        // 🔥 新增：20%固定止损规则
        const FIXED_STOP_LOSS_PERCENTAGE = -20; // 固定20%止损
        if (priceChange <= FIXED_STOP_LOSS_PERCENTAGE) {
          console.log(`🔴 触发20%固定止损卖出 ${tokenDisplay} (损失: ${priceChange.toFixed(2)}%)`);
          await this.executeStopLoss(tokenAddress, instance, currentPrice);
          return;
        }

        // 🔥 原有的动态止损检查（作为备用）
        if (position && this.positionManager.checkStopLoss(tokenAddress, currentPrice)) {
          console.log(`🔴 触发动态止损卖出 ${tokenDisplay} (配置: ${(this.tradingConfig.stopLossPercentage * 100).toFixed(1)}%)`);
          await this.executeStopLoss(tokenAddress, instance, currentPrice);
          return;
        }

        // 🔥 调试：显示为什么没有触发动态止损
        if (position && position.stopLossPrice) {
          const stopLossDistance = ((currentPrice - position.stopLossPrice) / position.stopLossPrice * 100);
          console.log(`   距离动态止损: ${stopLossDistance > 0 ? '+' : ''}${stopLossDistance.toFixed(2)}% (${stopLossDistance > 0 ? '安全' : '触发'})`);
        }

        console.log(`✅ 未触发止损，继续监控`);
      } else {
        console.log(`⚠️ 无法获取当前价格，跳过止损检查`);
      }

    }
    
    if (process.env.DEBUG_TRADING_DECISIONS === 'true') {
      console.log(`🔓 交易锁状态: 可以交易 ${tokenDisplay}`);
      console.log(`🛡️ 风险检查: ${riskCheck.reason}`);
    }
    
    // 🔥 真实买入逻辑 - 检查仓位限制和pending状态
    if (!hasPosition && buyPrediction >= this.tradingConfig.buyThreshold && this.positionManager.canOpenNewPosition()) {
      console.log(`📈 触发买入信号 (预测: ${(buyPrediction*100).toFixed(1)}%)`);
      
      // 🔥 新增：买入前SOL余额检查
      const balanceCheck = this.checkSufficientSolBalance();
      if (!balanceCheck.canBuy) {
        console.log(`🚫 买入被阻止: ${balanceCheck.reason}`);
        
        // 发送余额不足通知
        await this.telegramNotifier.notifySystem(
          `⚠️ 买入失败 - 余额不足\n` +
          `Token: ${tokenDisplay}\n` +
          `当前余额: ${balanceCheck.currentBalance.toFixed(6)} SOL\n` +
          `最小要求: 0.002 SOL\n` +
          `建议：检查钱包余额或调整交易金额`
        );
        return;
      }
      
      const buyRiskCheck = this.riskManager.canBuy();
      
      // 🔥 原子性检查：再次验证状态避免竞态条件
      if (instance.isTrading) {
        console.log(`🔒 买入过程中发现交易锁已被其他操作设置，取消买入`);
        return;
      }
      
      // 🔥 再次检查持仓状态 - 防止并发导致的状态变化
      const reCheckPosition = instance.currentHolding !== null && instance.currentHolding.amount > 0;
      const reCheckManagerPosition = this.positionManager.getPosition(tokenAddress) !== undefined;
      if (reCheckPosition || reCheckManagerPosition) {
        console.log(`🔒 买入过程中发现已有持仓，取消买入操作`);
        return;
      }
      
      instance.isTrading = true;
      console.log(`🔒 设置交易锁: 买入开始 ${tokenDisplay}`);
      
      try {
        const result = await this.executeRealBuy(tokenAddress, instance, buyPrediction);
        
        if (result.success && result.actualAmount > 0 && result.actualPrice > 0) {
          console.log(`✅ 买入交易已确认并记录完成`);
          console.log(`📦 获得Token: ${result.actualAmount.toFixed(2)} 个`);
          console.log(`💵 投入SOL: ${result.actualPrice ? (result.actualAmount * result.actualPrice).toFixed(4) : 'N/A'} SOL`);
          console.log(`💲 买入价格: ${result.actualPrice.toFixed(8)} SOL/token`);
          console.log(`✅ 状态: 已确认`);
          
          // 🔥 新增：买入成功后5秒回调，更新余额和持仓数据
          this.schedulePostBuyBalanceUpdate(tokenAddress, result);
        } else {
          console.log(`❌ 买入失败: ${result.error}`);
        }
        
          } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (errorMessage.includes('缺少pool信息')) {
          console.log(`⏳ 等待pool信息...`);
        } else {
          console.error(`❌ 买入错误:`, error);
        }
      } finally {
        instance.isTrading = false;
      }
    }
    // 🔥 真实卖出逻辑 - 修复：支持从Manager和Instance获取持仓信息
    else if (hasPosition && sellPrediction >= this.tradingConfig.sellThreshold) {
      console.log(`📉 触发卖出信号 (预测: ${(sellPrediction*100).toFixed(1)}%)`);
      
      // 🔥 修复：获取正确的卖出数量，处理状态不同步问题
      let sellAmount = 0;
      let sellSource = '';
      
      // 🔥 优先从PositionManager获取（最权威的数据源）
      const managerPosition = this.positionManager.getPosition(tokenAddress);
      if (managerPosition && managerPosition.amount > 0) {
        sellAmount = managerPosition.amount;
        sellSource = 'positionManager';
        
        // 🔥 同步修复instance.currentHolding，确保数据一致性
        if (!instance.currentHolding || instance.currentHolding.amount !== managerPosition.amount) {
          console.log(`🔧 同步Manager持仓信息到Instance (${managerPosition.amount.toFixed(6)} tokens)`);
          instance.currentHolding = {
            amount: managerPosition.amount,
            buyPrice: managerPosition.entryPrice,
            buyTime: managerPosition.timestamp,
            buySolAmount: managerPosition.amount * managerPosition.entryPrice,
            buyGasFee: 0.000005, // 估算值
            buyPlatformFee: (managerPosition.amount * managerPosition.entryPrice) * 0.003,
            totalBuyCost: (managerPosition.amount * managerPosition.entryPrice) * 1.003 + 0.000005,
            bundleId: managerPosition.id
          };
        }
      }
      // 如果Manager没有，再尝试从instance.currentHolding获取
      else if (instance.currentHolding && instance.currentHolding.amount > 0) {
        sellAmount = instance.currentHolding.amount;
        sellSource = 'instance.currentHolding';
      }
      
      console.log(`📊 卖出数量分析:`);
      console.log(`   数据源: ${sellSource}`);
      console.log(`   卖出数量: ${sellAmount.toFixed(6)} tokens`);
      console.log(`   数量有效: ${sellAmount > 0 ? '✅' : '❌'}`);
      
      if (sellAmount <= 0) {
        console.error(`❌ 无法获取有效的卖出数量，数据状态异常`);
        console.error(`   instance.currentHolding: ${instance.currentHolding ? `amount=${instance.currentHolding.amount}` : 'null'}`);
        console.error(`   managerPosition: ${managerPosition ? `amount=${managerPosition.amount}` : 'null'}`);
        
        // 🔥 清理无效状态
        if (managerPosition && managerPosition.amount <= 0) {
          console.log(`🔧 清理Manager中的无效持仓记录 (amount=${managerPosition.amount})`);
          this.positionManager.closePosition(tokenAddress, managerPosition.entryPrice, 0);
        }
        
        return;
      }
      
      instance.isTrading = true;
      
      try {
        const result = await this.executeRealSell(tokenAddress, sellAmount, sellPrediction);
        
        if (result.success && result.bundleId && result.actualPrice && result.actualAmount) {
          console.log(`✅ 卖出交易已确认并记录完成`);
          console.log(`📦 卖出Token: ${sellAmount.toFixed(2)} 个`);
          console.log(`💵 收到SOL: ${result.actualAmount.toFixed(4)} SOL`);
          console.log(`💲 卖出价格: ${result.actualPrice.toFixed(8)} SOL/token`);
          console.log(`✅ 状态: 已确认`);
          
          // 卖出完成后移除token
          await this.removeTokenFromMonitoring(tokenAddress, '卖出交易完成');
        } else {
          // 🔥 修复：卖出失败时的完整错误处理和仓位恢复
          const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
          console.log(`❌ 卖出失败: ${result.error}`);
          
          // 🔥 新增：验证当前真实余额，确保仓位状态正确
          try {
            const realBalance = await this.getRealTokenBalance(tokenAddress);
            console.log(`🔍 卖出失败后余额验证: ${realBalance.toFixed(6)} 个Token`);
            
            if (realBalance > 0.001) {
              // 如果还有余额，确保仓位记录正确
              if (!instance.currentHolding) {
                console.log(`🔧 检测到余额但无仓位记录，尝试恢复仓位信息...`);
                
                // 🔥 关键修复：从交易历史中恢复最后一次买入信息
                const lastBuyTrade = instance.tradingHistory
                  .filter(trade => trade.type === 'buy')
                  .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];
                
                if (lastBuyTrade) {
                  console.log(`📋 从交易历史恢复仓位信息:`);
                  console.log(`   买入时间: ${lastBuyTrade.timestamp.toISOString()}`);
                  console.log(`   买入价格: ${lastBuyTrade.price.toFixed(8)} SOL/token`);
                  console.log(`   当前余额: ${realBalance.toFixed(6)} tokens`);
                  
                  // 重建仓位记录
                  instance.currentHolding = {
                    amount: realBalance,
                    buyPrice: lastBuyTrade.price,
                    buyTime: lastBuyTrade.timestamp,
                    buySolAmount: lastBuyTrade.solAmount,
                    buyGasFee: TIP_AMOUNT + PRIORITY_FEE, // 估算值
                    buyPlatformFee: lastBuyTrade.solAmount * 0.01, // 1%平台费估算
                    totalBuyCost: lastBuyTrade.solAmount + TIP_AMOUNT + PRIORITY_FEE,
                    bundleId: lastBuyTrade.bundleId || 'recovered'
                  };
                  
                  // 同步到PositionManager
                  if (!this.positionManager.getPosition(tokenAddress)) {
                    this.positionManager.openPosition(tokenAddress, realBalance, lastBuyTrade.price);
                    console.log(`🔄 已同步仓位到PositionManager`);
                  } else {
                    // 🔥 修复：如果PositionManager中已存在仓位，更新数量而不是重新添加
                    const existingPosition = this.positionManager.getPosition(tokenAddress);
                    if (existingPosition) {
                      existingPosition.amount = realBalance;
                      console.log(`🔄 更新PositionManager中的仓位数量: ${realBalance.toFixed(6)} tokens`);
                    }
                  }
                  
                  // 🔥 重要：检查仓位数量是否超限，如果超限需要清理
                  const currentPositions = this.positionManager.getActivePositions();
                  if (currentPositions.length > this.tradingConfig.maxPositions) {
                    console.log(`⚠️ 检测到仓位数量超限: ${currentPositions.length}/${this.tradingConfig.maxPositions}`);
                    console.log(`🔧 开始清理状态不一致的仓位...`);
                    
                    // 检查哪些仓位在PositionManager中存在但TokenInstance中不存在
                    for (const position of currentPositions) {
                      const instance = this.tokenInstances.get(position.tokenAddress);
                      if (!instance || !instance.currentHolding || instance.currentHolding.amount <= 0.001) {
                        console.log(`🧹 清理无效仓位: ${position.tokenAddress.slice(0, 8)}...`);
                        this.positionManager.closePosition(position.tokenAddress, 0, 0);
                      }
                    }
                    
                    const afterCleanup = this.positionManager.getActivePositions();
                    console.log(`✅ 清理完成，仓位数量: ${afterCleanup.length}/${this.tradingConfig.maxPositions}`);
                  }
                  
                  console.log(`✅ 仓位信息已恢复，避免重复买入`);
                } else {
                  console.log(`⚠️ 无法找到买入历史记录，无法恢复仓位`);
                }
              } else {
                // 仓位记录存在，但更新余额
                const oldAmount = instance.currentHolding.amount;
                instance.currentHolding.amount = realBalance;
                console.log(`🔄 更新仓位余额: ${oldAmount.toFixed(6)} -> ${realBalance.toFixed(6)} tokens`);
              }
            } else {
              // 没有余额，清空仓位（可能之前已经卖出了）
              if (instance.currentHolding) {
                console.log(`🧹 检测到无余额，清理仓位记录`);
                instance.currentHolding = null;
                this.positionManager.closePosition(tokenAddress, 0, 0);
              }
            }
            
            // 🔥 添加到sell-only缓冲队列，避免立即再次买入
            if (realBalance > 0.001) {
              console.log(`🛡️ 将token添加到sell-only缓冲队列，防止重复买入`);
              this.sellOnlyBufferQueue.set(tokenAddress, {
                address: tokenAddress,
                addedTime: new Date(),
                reason: `卖出失败后保护期(${result.error})`,
                lastCheckTime: new Date()
              });
            }
            
          } catch (balanceError) {
            console.error(`❌ 余额验证失败:`, balanceError);
          }
          
          // 🔥 移除卖出失败通知 - 现在统一通过GRPC确认交易状态
          // 不再发送即时失败通知，等待GRPC确认或3秒超时后再判断
          console.log(`⏳ 卖出交易已发送，等待GRPC确认结果...`);
          
          // 🔥 记录pending交易，等待GRPC确认
          if (result.bundleId) {
            instance.pendingTransactions.set(result.bundleId, {
              type: 'sell',
              timestamp: new Date(),
              signature: result.bundleId,
              confirmed: false
            });
            
            // 🔥 设置3秒超时，如果没有收到GRPC确认则认为失败
            setTimeout(async () => {
              const pendingTx = instance.pendingTransactions.get(result.bundleId!);
              if (pendingTx && !pendingTx.confirmed) {
                console.log(`⏰ 卖出交易5秒超时未确认，认为失败: ${result.bundleId}`);
                instance.pendingTransactions.delete(result.bundleId!);
                
                // 发送超时失败通知
                try {
                  await this.telegramNotifier.notifySystem(
                    `❌ 卖出交易超时\n` +
                    `Token: ${tokenDisplay}\n` +
                    `交易ID: ${result.bundleId}\n` +
                    `5秒内未收到GRPC确认`
                  );
                } catch (notifyError) {
                  console.error('发送卖出超时通知错误:', notifyError);
                }
              }
            }, 5000);
          }
        }
        
      } catch (error) {
        console.error(`❌ 卖出错误:`, error);
      } finally {
        instance.isTrading = false;
      }
    }
    else {
      // 🔥 增强调试信息：记录为什么没有交易
      const currentTime = new Date().toISOString();
      console.log(`\n🔍 [${currentTime}] 交易决策详情 ${tokenDisplay}:`);
      console.log(`   💰 持仓状态详情:`);
      console.log(`      - hasInstancePosition: ${hasInstancePosition}`);
      console.log(`      - hasManagerPosition: ${hasManagerPosition}`);
      console.log(`      - 综合hasPosition: ${hasPosition}`);
      console.log(`      - instance.currentHolding存在: ${!!instance.currentHolding}`);
      
      if (instance.currentHolding) {
        console.log(`      - 持仓数量: ${instance.currentHolding.amount.toFixed(6)} tokens`);
        console.log(`      - 买入价格: ${instance.currentHolding.buyPrice.toFixed(8)} SOL/token`);
        console.log(`      - 买入时间: ${instance.currentHolding.buyTime.toISOString()}`);
        
        const holdingTimeMs = Date.now() - instance.currentHolding.buyTime.getTime();
        const holdingMinutes = Math.floor(holdingTimeMs / (1000 * 60));
        console.log(`      - 持仓时间: ${holdingMinutes}分钟`);
      }
      
      console.log(`   🤖 AI预测详情:`);
      console.log(`      - 买入预测: ${(buyPrediction*100).toFixed(2)}%`);
      console.log(`      - 卖出预测: ${(sellPrediction*100).toFixed(2)}%`);
      console.log(`      - 买入阈值: ${(this.tradingConfig.buyThreshold*100).toFixed(1)}%`);
      console.log(`      - 卖出阈值: ${(this.tradingConfig.sellThreshold*100).toFixed(1)}%`);
      console.log(`      - 买入预测达标: ${buyPrediction >= this.tradingConfig.buyThreshold ? '✅' : '❌'}`);
      console.log(`      - 卖出预测达标: ${sellPrediction >= this.tradingConfig.sellThreshold ? '✅' : '❌'}`);
      
      console.log(`   🔒 交易状态:`);
      console.log(`      - instance.isTrading: ${instance.isTrading}`);
      // console.log(`      - hasPendingBuy: ${hasPendingBuy}`);
      // console.log(`      - hasPendingSell: ${hasPendingSell}`);
      // console.log(`      - hasPendingBuy: ${hasPendingBuy}`);
      // console.log(`      - hasPendingSell: ${hasPendingSell}`);
      // console lines commented out to avoid undefined variable errors
      
      console.log(`   📊 其他检查:`);
      console.log(`      - 可开新仓位: ${this.positionManager.canOpenNewPosition()}`);
      console.log(`      - 当前活跃仓位: ${this.positionManager.getActivePositions().length}/${this.tradingConfig.maxPositions}`);
      
      if (hasPosition) {
        if (sellPrediction < this.tradingConfig.sellThreshold) {
          console.log(`   ⏸️ 持仓但卖出预测不足: ${(sellPrediction*100).toFixed(1)}% < ${(this.tradingConfig.sellThreshold*100).toFixed(1)}%`);
          
          // 🔥 额外调试：检查卖出预测器状态
          const currentPrice = this.getLastTradePrice(tokenAddress);
          if (currentPrice && instance.currentHolding) {
            const priceChange = ((currentPrice / instance.currentHolding.buyPrice - 1) * 100);
            console.log(`   💹 价格变化: ${priceChange > 0 ? '+' : ''}${priceChange.toFixed(2)}%`);
            console.log(`   💰 当前价格: ${currentPrice.toFixed(8)} SOL/token`);
            
            // 如果价格涨幅较大但AI不建议卖出，提示可能的原因
            if (priceChange > 10) {
              console.log(`   ⚠️ 价格已上涨${priceChange.toFixed(1)}%但AI不建议卖出，可能原因:`);
              console.log(`      - AI模型预测还会继续上涨`);
              console.log(`      - 交易数据不足，AI信心不够`);
              console.log(`      - 当前市场环境不适合卖出`);
            }
          }
        }
      } else {
        if (buyPrediction < this.tradingConfig.buyThreshold) {
          console.log(`   ⏸️ 无持仓且买入预测不足: ${(buyPrediction*100).toFixed(1)}% < ${(this.tradingConfig.buyThreshold*100).toFixed(1)}%`);
        } else if (!this.positionManager.canOpenNewPosition()) {
          console.log(`   ⏸️ 已达到最大仓位限制: ${this.positionManager.getActivePositions().length}/${this.tradingConfig.maxPositions}`);
        }
      }
      
      console.log(`   🎯 决策结论: ${hasPosition ? '持仓中，等待卖出信号' : '无持仓，等待买入信号'}`);
      console.log(`\n`);
    }
  }

  // 其他辅助方法（从paper trading复制并适配）
  private async createTokenInstance(tokenAddress: string): Promise<TokenTradingInstance> {
    const activity = this.tokenActivities.get(tokenAddress) || {
      address: tokenAddress,
      firstSeen: new Date(),
      lastSeen: new Date(),
      transactionCount: 0,
      actions: []
    };

    // 🔥 V3预测器是唯一的预测器，移除旧模型依赖
    return {
      address: tokenAddress,
      activity,
      // 🔥 保留空的旧预测器引用以兼容接口定义，但不会使用
      // buyPredictor: null as any, // 不再使用 - removed deprecated property
      // sellPredictor: null as any, // 不再使用 - removed deprecated property
      // 🔥 V4预测器 - 唯一使用的预测器
      buyPredictorV4: new BuyPredictorV4(
        'predictors/v4/buy_predictor_v4_relative_features_20250624_143400.cbm',
        'predictors/v4/buy_predictor_v4_relative_features_scaler_20250624_143400.json',
        'predictors/v4/buy_predictor_v4_relative_features_features_20250624_143400.json',
        this.config.models.buyThreshold
      ),
      sellPredictorV4: new SellPredictorV4(
        'predictors/v4/sell_predictor_v4_json_20250624_124138.cbm',
        'predictors/v4/sell_predictor_v4_json_scaler_20250624_124138.json',
        'predictors/v4/sell_predictor_v4_json_features_20250624_124138.json',
        this.config.models.sellThreshold
      ),
      featureWindow: [],
      lastPrediction: new Date(0),
      isActive: true,
      isTrading: false,
      lastTradeTime: new Date(0),
      tradingHistory: [],
      currentHolding: null,
      stats: {
        totalTrades: 0,
        successfulTrades: 0,
        totalPnL: 0,
        currentPosition: 0
      },
      // 🔥 初始化Pool信息相关属性
      poolExtracted: false,
      realPoolData: undefined,
      poolData: undefined,
      // 🔥 初始化交易状态跟踪
      pendingTransactions: new Map(),
      lastConfirmedSellTime: undefined
    };
  }

  private async initializeTokenPredictorData(tokenAddress: string, instance: TokenTradingInstance): Promise<void> {
    const allHistoricalTransactions = this.getTokenSpecificTransactions(tokenAddress);
    
    if (allHistoricalTransactions.length > 0) {
      for (const tx of allHistoricalTransactions) {
        const featureData = {
          timestamp: tx.timestamp instanceof Date ? tx.timestamp : new Date(tx.timestamp),
          action: tx.action === 'buy' ? 1 : tx.action === 'sell' ? 0 : 2,
          sol_amount: tx.solAmount,
          usd_amount: tx.usdAmount,
          is_target_wallet: (tx.wallet || '').includes(TARGET_WALLET.slice(0, 8)),
          wallet: tx.wallet || 'unknown',
          block_number: tx.slot
        };
        
        // 🔥 只使用V4预测器，修复价格计算
        // 尝试从实时价格数据获取正确的SOL per Token价格
        let correctPrice: number | undefined = undefined;
        
        // 🔥 改进的价格获取逻辑：尝试从交易数据本身计算价格
        if (!this.realPriceData.has(tokenAddress)) {
          // 尝试从交易数据本身计算价格
          if (tx.solAmount > 0 && tx.tokenAmount > 0) {
            correctPrice = tx.solAmount / tx.tokenAmount;
            if (process.env.DEBUG_HISTORICAL_INIT === 'true') {
              console.log(`   💡 历史交易使用内嵌价格: ${correctPrice.toFixed(10)} SOL/token (SOL:${tx.solAmount}, Token:${tx.tokenAmount})`);
            }
          } else {
            // 只有在无法计算价格时才跳过
            if (process.env.DEBUG_HISTORICAL_INIT === 'true') {
              console.warn(`   ❌ 跳过历史交易：无法获取价格数据 ${tokenAddress.slice(0, 8)}...`);
              console.warn(`   📊 交易数据: SOL=${tx.solAmount}, Token=${tx.tokenAmount}`);
            }
            continue;
          }
        } else {
          const priceHistory = this.realPriceData.get(tokenAddress)!;
          if (priceHistory.length === 0) {
            // 尝试从交易数据计算价格
            if (tx.solAmount > 0 && tx.tokenAmount > 0) {
              correctPrice = tx.solAmount / tx.tokenAmount;
              if (process.env.DEBUG_HISTORICAL_INIT === 'true') {
                console.log(`   💡 价格历史为空，使用交易内嵌价格: ${correctPrice.toFixed(10)} SOL/token`);
              }
            } else {
              if (process.env.DEBUG_HISTORICAL_INIT === 'true') {
                console.warn(`   ❌ 跳过历史交易：token ${tokenAddress.slice(0, 8)}... 的价格历史为空且无法计算价格`);
              }
              continue;
            }
          }
        }

        // 如果已经有correctPrice，跳过价格查找逻辑
        if (!correctPrice) {
          const priceHistory = this.realPriceData.get(tokenAddress)!;
          const targetTime = featureData.timestamp.getTime();
          let closestPrice = priceHistory.find(p =>
            Math.abs(p.timestamp.getTime() - targetTime) < 30000 // 30秒内
          );

          if (!closestPrice) {
            // 找到最近的价格记录，看看时间差多少
            const sortedPrices = priceHistory.sort((a, b) =>
              Math.abs(a.timestamp.getTime() - targetTime) - Math.abs(b.timestamp.getTime() - targetTime)
            );
            const nearestPrice = sortedPrices[0];
            const timeDiff = Math.abs(nearestPrice.timestamp.getTime() - targetTime);

            if (process.env.DEBUG_HISTORICAL_INIT === 'true') {
              console.warn(`   ❌ 跳过历史交易：token ${tokenAddress.slice(0, 8)}... 在30秒内没有价格数据`);
              console.warn(`   📅 交易时间: ${featureData.timestamp.toLocaleString()}`);
              console.warn(`   📅 最近价格时间: ${nearestPrice.timestamp.toLocaleString()}`);
              console.warn(`   ⏰ 时间差: ${(timeDiff / 1000).toFixed(1)}秒`);
            }
            continue;
          }

          correctPrice = closestPrice.price;
          if (process.env.DEBUG_HISTORICAL_INIT === 'true') {
            console.log(`   ✅ 历史数据使用实时价格: ${correctPrice.toFixed(10)} SOL/token`);
          }
        }
        
        // 🔥 修复历史数据的USD金额计算
        let historicalUsdAmount = featureData.usd_amount || 0;
        if (historicalUsdAmount <= 0 && featureData.sol_amount > 0) {
          try {
            historicalUsdAmount = await this.priceService.convertSolToUsd(featureData.sol_amount);
          } catch (error) {
            historicalUsdAmount = featureData.sol_amount * this.priceService.getCachedSolPrice();
          }
        }

        const v4BuyTransactionData: BuyTransactionDataV4 = {
          timestamp: featureData.timestamp,
          transaction_type: featureData.action === 1 ? 'buy' : featureData.action === 0 ? 'sell' : 'buy',
          sol_amount: featureData.sol_amount,
          usd_amount: historicalUsdAmount
        };
        
        const v4SellTransactionData: TransactionDataForSellV4 = {
          timestamp: featureData.timestamp,
          transaction_type: featureData.action === 1 ? 'buy' : featureData.action === 0 ? 'sell' : 'buy',
          sol_amount: featureData.sol_amount
        };
        
        instance.buyPredictorV4.addTransaction(v4BuyTransactionData);
        instance.sellPredictorV4.addTransaction(v4SellTransactionData);
        
        // V4卖出预测器还需要价格数据
        const v4PriceData: PriceDataV4 = {
          timestamp: featureData.timestamp,
          price: correctPrice || 0
        };
        instance.sellPredictorV4.addPriceData(v4PriceData);
      }
      
      const latestTransaction = allHistoricalTransactions[allHistoricalTransactions.length - 1];
      instance.lastProcessedTransactionTime = latestTransaction.timestamp;
    } else {
      instance.lastProcessedTransactionTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }
  }

  private getNewTransactionsForToken(tokenAddress: string, instance: TokenTradingInstance): any[] {
    const allTransactions = this.subscriptionManager.getWalletTransactionHistory(24);
    const tokenTransactions = allTransactions.filter(tx => 
      tx.tokenAddress === tokenAddress && 
      tx.solAmount >= MIN_TRANSACTION_SOL
    );
    
    const lastProcessedTime = instance.lastProcessedTransactionTime || new Date(0);
    const newTransactions = tokenTransactions.filter(tx => 
      tx.timestamp > lastProcessedTime
    );
    
    return newTransactions;
  }

  private getTokenSpecificTransactions(tokenAddress: string): any[] {
    const allTransactions = this.subscriptionManager.getWalletTransactionHistory(24);
    return allTransactions.filter(tx => 
      tx.tokenAddress === tokenAddress && 
      tx.solAmount >= MIN_TRANSACTION_SOL
    );
  }

  private async manageTokenMonitoringQueue(newTokenAddress: string): Promise<void> {
    const currentActivePositions = this.positionManager.getActivePositions().length;
    const existingIndex = this.tokenMonitoringQueue.findIndex(item => item.address === newTokenAddress);
    
    if (existingIndex !== -1) {
      this.tokenMonitoringQueue.splice(existingIndex, 1);
    }
    
    this.tokenMonitoringQueue.push({
      address: newTokenAddress,
      addedTime: new Date()
    });
    
    console.log(`✅ 新Token已添加到监控队列 (${currentActivePositions}/${this.tradingConfig.maxPositions} 仓位):`);
    console.log(`   Token: ${await this.formatTokenDisplay(newTokenAddress)}`);
    console.log(`   队列长度: ${this.tokenMonitoringQueue.length}/${MAX_MONITORED_TOKENS}`);
    
    while (this.tokenMonitoringQueue.length > MAX_MONITORED_TOKENS) {
      const removedToken = this.tokenMonitoringQueue.shift();
      if (removedToken) {
        await this.removeTokenFromMonitoring(removedToken.address, '队列满员被移除');
      }
    }
  }

  private async removeTokenFromMonitoring(tokenAddress: string, reason: string): Promise<void> {
    const instance = this.tokenInstances.get(tokenAddress);
    const hasPosition = instance && instance.currentHolding && instance.currentHolding.amount > 0;
    
    if (hasPosition) {
      this.sellOnlyBufferQueue.set(tokenAddress, {
        address: tokenAddress,
        addedTime: new Date(),
        reason: reason,
        lastCheckTime: new Date()
      });
      
      if (!this.currentTokenSubscriptions.has(tokenAddress)) {
        this.currentTokenSubscriptions.add(tokenAddress);
      }
      
      const queueIndex = this.tokenMonitoringQueue.findIndex(item => item.address === tokenAddress);
      if (queueIndex !== -1) {
        this.tokenMonitoringQueue.splice(queueIndex, 1);
      }
    } else {
      await this.completelyRemoveToken(tokenAddress, reason);
    }
  }

  /**
   * 🔥 新增：从RPC获取真实的池子余额信息
   */
  private async fetchRealPoolBalances(poolData: PoolData): Promise<{ tokenBalance: number, solBalance: number }> {
    try {
      console.log(`🔍 获取真实池子余额: ${poolData.poolAddress.toBase58().slice(0, 8)}...`);
      
      // 获取池子的base token账户余额 (token数量)
      const baseTokenAccountInfo = await queryConnection.getTokenAccountBalance(poolData.poolBaseTokenAccount);
      const tokenBalance = parseFloat(baseTokenAccountInfo.value.amount) / Math.pow(10, baseTokenAccountInfo.value.decimals);
      
      // 获取池子的quote token账户余额 (SOL数量)
      const quoteTokenAccountInfo = await queryConnection.getTokenAccountBalance(poolData.poolQuoteTokenAccount);
      const solBalance = parseFloat(quoteTokenAccountInfo.value.amount) / Math.pow(10, quoteTokenAccountInfo.value.decimals);
      
      console.log(`✅ 真实池子余额获取成功:`);
      console.log(`   Token余额: ${tokenBalance.toLocaleString()} tokens`);
      console.log(`   SOL余额: ${solBalance.toFixed(4)} SOL`);
      console.log(`   价格比率: ${(solBalance / tokenBalance).toFixed(10)} SOL/token`);
      
      return { tokenBalance, solBalance };
      
    } catch (error) {
      console.error(`❌ 获取真实池子余额失败:`, error);
      // 如果获取失败，抛出错误而不是使用假设值
      throw new Error(`无法获取真实池子余额: ${error}`);
    }
  }

  /**
   * 🔥 新增：从事件数据中提取真实的池子余额变化
   */
  private extractRealPoolBalancesFromEvents(parsedTxn: any): { tokenBalance?: number, solBalance?: number } | null {
    try {
      if (!parsedTxn.events || !Array.isArray(parsedTxn.events)) {
        return null;
      }
      
      for (const event of parsedTxn.events) {
        if (event.data) {
          // 从BuyEvent或SellEvent中提取池子状态
          if (event.name === 'BuyEvent' || event.name === 'SellEvent') {
            const data = event.data;
            
            // 尝试从事件数据中提取池子余额
            if (data.pool_base_amount && data.pool_quote_amount) {
              const tokenBalance = parseFloat(data.pool_base_amount) / 1e6; // 假设6位小数
              const solBalance = parseFloat(data.pool_quote_amount) / 1e9; // SOL是9位小数
              
              console.log(`📊 从事件提取真实池子余额:`);
              console.log(`   Token余额: ${tokenBalance.toLocaleString()} tokens`);
              console.log(`   SOL余额: ${solBalance.toFixed(4)} SOL`);
              
              return { tokenBalance, solBalance };
            }
          }
        }
      }
      
      return null;
    } catch (error) {
      console.error(`❌ 从事件提取池子余额失败:`, error);
      return null;
    }
  }

  /**
   * 🔥 检查系统是否在运行
   */
  public isSystemRunning(): boolean {
    return this.isRunning;
  }

  /**
   * 🔥 查询真实持仓数量
   */
  private async getRealTokenBalance(tokenAddress: string): Promise<number> {
    try {
      console.log(`🔍 开始查询真实Token余额: ${tokenAddress}`);
      const wallet = Keypair.fromSecretKey(bs58.decode(PRIVATE_KEY));
      const tokenMint = new PublicKey(tokenAddress);
      
      // 获取用户的Token ATA地址
      const userTokenAta = getAssociatedTokenAddressSync(
        tokenMint,
        wallet.publicKey,
        false,
        TOKEN_PROGRAM_ID,
        ASSOCIATED_TOKEN_PROGRAM_ID
      );
      
      console.log(`   用户钱包: ${wallet.publicKey.toString()}`);
      console.log(`   Token Mint: ${tokenMint.toString()}`);
      console.log(`   用户Token ATA: ${userTokenAta.toString()}`);
      
      // 查询真实持仓数量
      const tokenAccountInfo = await queryConnection.getTokenAccountBalance(userTokenAta);
      console.log(`   账户查询结果:`, JSON.stringify(tokenAccountInfo, null, 2));
      
      if (tokenAccountInfo?.value?.uiAmount) {
        const amount = tokenAccountInfo.value.uiAmount;
        console.log(`   ✅ 成功获取Token余额: ${amount} 个`);
        return amount;
      } else {
        console.log(`   ⚠️ Token账户不存在或余额为0`);
        
        // 检查账户是否存在
        try {
          const accountInfo = await queryConnection.getAccountInfo(userTokenAta);
          if (accountInfo) {
            console.log(`   账户存在但余额为空`);
          } else {
            console.log(`   Token账户尚未创建`);
          }
        } catch (accountError) {
          console.log(`   检查账户存在性失败:`, accountError);
        }
        
        return 0;
      }
      
    } catch (error) {
      console.error(`❌ 查询真实持仓失败: ${error}`);
      return 0;
    }
  }

  /**
   * 🔥 异步更新真实持仓数量
   */
  private async updateRealTokenBalance(tokenAddress: string, instance: TokenTradingInstance): Promise<void> {
    try {
      console.log(`🔄 异步查询真实持仓数量: ${tokenAddress.slice(0, 8)}...`);
      
      const wallet = Keypair.fromSecretKey(bs58.decode(PRIVATE_KEY));
      const tokenMint = new PublicKey(tokenAddress);
      
      // 获取用户的Token ATA地址
      const userTokenAta = getAssociatedTokenAddressSync(
        tokenMint,
        wallet.publicKey,
        false,
        TOKEN_PROGRAM_ID,
        ASSOCIATED_TOKEN_PROGRAM_ID
      );
      
      // 查询真实持仓数量
      const tokenAccountInfo = await queryConnection.getTokenAccountBalance(userTokenAta);
      
      if (tokenAccountInfo?.value?.uiAmount) {
        const realBalance = tokenAccountInfo.value.uiAmount;
        
        console.log(`📊 真实持仓查询结果:`);
        console.log(`   链上余额: ${realBalance.toFixed(6)} 个`);
        
        if (instance.currentHolding) {
          const originalAmount = instance.currentHolding.amount;
          console.log(`   预估余额: ${originalAmount.toFixed(6)} 个`);
          console.log(`   差异: ${(realBalance - originalAmount).toFixed(6)} 个`);
          
          // 更新持仓数量为真实数量
          instance.currentHolding.amount = realBalance;
          instance.stats.currentPosition = realBalance;
          
          console.log(`✅ 已更新持仓数量为真实链上余额: ${realBalance.toFixed(6)} 个`);
        } else {
          console.log(`⚠️ 当前没有持仓记录，但链上有余额: ${realBalance.toFixed(6)} 个`);
        }
      } else {
        console.log(`⚠️ 未找到Token账户或余额为0`);
      }
      
    } catch (error) {
      console.error(`❌ 查询真实持仓失败: ${error}`);
    }
  }

  /**
   * 🔥 从交易中提取Token转账数量 - 只获取真实链上数据
   */
  private extractTokenAmountFromTransaction(
    transaction: any, 
    tokenAddress: string, 
    userPublicKey: PublicKey
  ): number {
    try {
      console.log(`🔍 开始解析交易中的Token数量...`);
      console.log(`   Token地址: ${tokenAddress}`);
      console.log(`   用户地址: ${userPublicKey.toString()}`);
      

      // 方法1: 从meta.innerInstructions中查找Token转账
      if (transaction.meta?.innerInstructions) {
        console.log(`📊 方法1: 检查innerInstructions (${transaction.meta.innerInstructions.length}个)...`);
        
        for (let i = 0; i < transaction.meta.innerInstructions.length; i++) {
          const innerInstruction = transaction.meta.innerInstructions[i];
          console.log(`   检查innerInstruction ${i}: ${innerInstruction.instructions.length}个指令`);
          
          for (let j = 0; j < innerInstruction.instructions.length; j++) {
            const instruction = innerInstruction.instructions[j];
            console.log(`     指令${j}: programId=${instruction.programId.toString()}`);
            
            // 查找Token Program的transferChecked指令
            if (instruction.programId.toString() === TOKEN_PROGRAM_ID.toString()) {
              console.log(`       找到Token Program指令: ${instruction.parsed?.type}`);
              
              // 解析transferChecked指令
              if (instruction.parsed?.type === 'transferChecked') {
                const info = instruction.parsed.info;
                console.log(`         transferChecked详情:`, JSON.stringify(info, null, 2));
                
                // 🔥 修复：对于卖出交易，我们需要查找用户作为source的转账
                const sourceOwner = info.authority || info.source;
                const amount = parseFloat(info.tokenAmount?.uiAmount || '0');
                
                console.log(`         检查transferChecked: source=${sourceOwner}, amount=${amount}`);
                
                // 如果用户是转账的发起者（卖出），返回转账数量
                if (sourceOwner === userPublicKey.toString() && amount > 0) {
                  console.log(`         ✅ 找到用户卖出的Token: ${amount} 个`);
                  return amount;
                }
              }
              
              // 也检查transfer指令
              if (instruction.parsed?.type === 'transfer') {
                const info = instruction.parsed.info;
                console.log(`         transfer详情:`, JSON.stringify(info, null, 2));
                
                // 🔥 修复：对于卖出交易，查找用户作为authority的转账
                const sourceOwner = info.authority || info.source;
                const amount = parseFloat(info.amount) / (10 ** 6); // 假设6位小数
                
                console.log(`         检查transfer: authority=${sourceOwner}, amount=${amount}`);
                
                // 如果用户是转账的发起者（卖出），返回转账数量
                if (sourceOwner === userPublicKey.toString() && amount > 0) {
                  console.log(`         ✅ 找到用户卖出的Token: ${amount} 个`);
                    return amount;
                  }
              }
            }
          }
        }
      }
      
      // 方法2: 从preTokenBalances和postTokenBalances计算差值
      if (transaction.meta?.preTokenBalances && transaction.meta?.postTokenBalances) {
        console.log(`📊 方法2: 检查Token余额变化...`);
        console.log(`   preTokenBalances: ${transaction.meta.preTokenBalances.length}个`);
        console.log(`   postTokenBalances: ${transaction.meta.postTokenBalances.length}个`);
        
        const preBal = transaction.meta.preTokenBalances.find((bal: any) => 
          bal.owner === userPublicKey.toString() && bal.mint === tokenAddress
        );
        const postBal = transaction.meta.postTokenBalances.find((bal: any) => 
          bal.owner === userPublicKey.toString() && bal.mint === tokenAddress
        );
        
        console.log(`   找到的preBal:`, preBal);
        console.log(`   找到的postBal:`, postBal);
        
        const preAmount = preBal?.uiTokenAmount?.uiAmount || 0;
        const postAmount = postBal?.uiTokenAmount?.uiAmount || 0;
        const difference = preAmount - postAmount; // 🔥 修复：卖出时是余额减少
        
        console.log(`   余额变化: ${preAmount} -> ${postAmount} = 减少${difference}`);
        
        if (difference > 0) {
          console.log(`   ✅ 通过余额变化计算得到卖出: ${difference} 个Token`);
          return difference;
        }
      }
      
      console.log(`   ❌ 无法从交易中提取Token数量`);
      return 0;
    } catch (error) {
      console.error(`❌ 提取Token数量失败: ${error}`);
      return 0;
    }
  }

  /**
   * 🔥 从交易中提取SOL花费数量
   */
  private extractSolSpentFromTransaction(transaction: any, userPublicKey: PublicKey): number {
    try {
      // 🔥 修复：添加对数据结构的详细检查
      if (!transaction?.meta?.preBalances || !transaction?.meta?.postBalances) {
        console.log(`⚠️ 交易缺少余额数据`);
        return 0;
      }
      
      if (!transaction?.transaction?.message?.accountKeys) {
        console.log(`⚠️ 交易缺少账户密钥数据`);
        return 0;
      }
      
      // 找到用户账户的索引
      const userAccountIndex = transaction.transaction.message.accountKeys.findIndex(
        (key: any) => key?.toString && key.toString() === userPublicKey.toString()
      );
      
      if (userAccountIndex !== -1) {
        const preBalance = transaction.meta.preBalances[userAccountIndex];
        const postBalance = transaction.meta.postBalances[userAccountIndex];
        
        if (preBalance !== undefined && postBalance !== undefined) {
          const solSpent = (preBalance - postBalance) / 1e9; // 转换为SOL
          console.log(`💰 SOL花费计算: ${preBalance} -> ${postBalance} = ${solSpent.toFixed(6)} SOL`);
          return solSpent;
        }
      } else {
        console.log(`⚠️ 未找到用户账户索引`);
      }
      
      return 0;
    } catch (error) {
      console.error(`❌ 提取SOL花费失败: ${error}`);
      return 0;
    }
  }

  /**
   * 🔥 从卖出交易中提取实际收到的SOL数量
   */
  private extractSolReceivedFromSellTransaction(
    transaction: any,
    userPublicKey: PublicKey
  ): number {
    console.log(
      `[extractSolReceived] 🔍 Extracting SOL received from token sale for user: ${userPublicKey.toString()}`
    );
    
    // 🔥 添加交易结构调试信息
    console.log(`[extractSolReceived] 📋 Transaction structure debug:`);
    console.log(`   transaction exists: ${!!transaction}`);
    console.log(`   transaction.meta exists: ${!!transaction?.meta}`);
    console.log(`   transaction.meta.innerInstructions exists: ${!!transaction?.meta?.innerInstructions}`);
    console.log(`   transaction.meta.innerInstructions length: ${transaction?.meta?.innerInstructions?.length || 0}`);
    console.log(`   transaction.meta.preTokenBalances exists: ${!!transaction?.meta?.preTokenBalances}`);
    console.log(`   transaction.meta.postTokenBalances exists: ${!!transaction?.meta?.postTokenBalances}`);
    console.log(`   transaction.meta.logMessages exists: ${!!transaction?.meta?.logMessages}`);
    
    if (transaction?.meta?.logMessages) {
      console.log(`   logMessages sample:`, transaction.meta.logMessages.slice(0, 3));
    }
    
    try {
      // 🔥 从 innerInstructions 中查找 SOL 转账到用户钱包
      if (!transaction.meta?.innerInstructions) {
        console.error(`[extractSolReceived] ❌ No innerInstructions found in transaction`);
        return 0;
      }
      
      console.log(`[extractSolReceived] 📊 Checking innerInstructions (${transaction.meta.innerInstructions.length} groups)...`);
      
      let totalSolReceived = 0;
      
      for (let i = 0; i < transaction.meta.innerInstructions.length; i++) {
        const innerInstruction = transaction.meta.innerInstructions[i];
        console.log(`   Checking innerInstruction group ${i}: ${innerInstruction.instructions.length} instructions`);
        
        for (let j = 0; j < innerInstruction.instructions.length; j++) {
          const instruction = innerInstruction.instructions[j];
          
          // 🔥 安全检查：确保 programId 存在
          if (!instruction.programId) {
            console.log(`     Instruction ${j}: programId is undefined, skipping`);
            continue;
          }
          
          // 查找 System Program 的 transfer 指令（SOL 转账）
          if (instruction.programId.toString() === '11111111111111111111111111111111') {
            console.log(`     Found System Program instruction: ${instruction.parsed?.type || 'unknown'}`);
            
            // 🔥 优先使用 parsed 数据（如果存在）
            if (instruction.parsed?.type === 'transfer') {
              const info = instruction.parsed.info;
              const destination = info.destination;
              const lamports = parseInt(info.lamports || '0');
              const solAmount = lamports / 1e9;
              
              console.log(`       Transfer details (parsed): ${info.source} -> ${destination}, amount: ${solAmount.toFixed(9)} SOL`);
              
              // 如果是转账到用户钱包，累加 SOL 数量
              if (destination === userPublicKey.toString() && solAmount > 0) {
                totalSolReceived += solAmount;
                console.log(`       ✅ Found SOL transfer TO user (parsed): +${solAmount.toFixed(9)} SOL`);
              }
            }
            // 🔥 如果没有 parsed 数据，尝试手动解析
            else if (instruction.accounts && instruction.data) {
              console.log(`       Attempting manual parsing of System Program instruction`);
              
              try {
                // System Program 转账指令格式：
                // accounts[0] = source, accounts[1] = destination
                // data: 4字节指令类型 + 8字节金额
                
                if (instruction.accounts.length >= 2) {
                  const source = instruction.accounts[0];
                  const destination = instruction.accounts[1];
                  
                  console.log(`       Manual parse: ${source} -> ${destination}`);
                  
                  // 检查是否转账到用户钱包
                  if (destination === userPublicKey.toString()) {
                    // 解析转账金额
                    const dataBuffer = Buffer.from(instruction.data, 'base64');
                    if (dataBuffer.length >= 12) {
                      const instructionType = dataBuffer.readUInt32LE(0);
                      if (instructionType === 2) { // Transfer 指令
                        const lamports = dataBuffer.readBigUInt64LE(4);
                        const solAmount = Number(lamports) / 1e9;
                        
                        console.log(`       Transfer details (manual): ${source} -> ${destination}, amount: ${solAmount.toFixed(9)} SOL`);
                        
                        if (solAmount > 0) {
                          totalSolReceived += solAmount;
                          console.log(`       ✅ Found SOL transfer TO user (manual): +${solAmount.toFixed(9)} SOL`);
                        }
                      }
                    }
                  }
                }
              } catch (parseError) {
                console.log(`       ⚠️ Manual parsing failed:`, parseError);
              }
            }
          }
        }
      }
      
              if (totalSolReceived > 0) {
          console.log(`[extractSolReceived] ✅ SUCCESS: Total SOL received from sale: ${totalSolReceived.toFixed(9)} SOL`);
          return totalSolReceived;
        } else {
          console.error(`[extractSolReceived] ❌ FAILED: No SOL transfers to user found in innerInstructions`);
          
          // 🔥 调试：显示所有 innerInstructions 的详细信息
          console.log(`[extractSolReceived] 🔍 DEBUG: All innerInstructions details:`);
          for (let i = 0; i < transaction.meta.innerInstructions.length; i++) {
            const innerInstruction = transaction.meta.innerInstructions[i];
            console.log(`   Group ${i}:`);
            for (let j = 0; j < innerInstruction.instructions.length; j++) {
              const instruction = innerInstruction.instructions[j];
              console.log(`     Instruction ${j}:`);
              console.log(`       programId: ${instruction.programId ? instruction.programId.toString() : 'undefined'}`);
              console.log(`       parsed type: ${instruction.parsed?.type || 'N/A'}`);
              if (instruction.parsed?.info) {
                console.log(`       parsed info:`, JSON.stringify(instruction.parsed.info, null, 2));
              }
        }
      }
      
      return 0;
        }
      
    } catch (err) {
      console.error(`[extractSolReceived] ❌ Error extracting SOL received:`, err);
      return 0;
    }
  }

  /**
   * 🍃 诊断卖出问题的详细方法
   */
  public async diagnoseSellIssues(): Promise<void> {
    console.log(`\n🔍 ====== 卖出问题诊断报告 ======`);
    console.log(`⏰ 诊断时间: ${new Date().toISOString()}\n`);
    
    // 1. 全局配置检查
    console.log(`🔧 系统配置:`);
    console.log(`   卖出阈值: ${(this.tradingConfig.sellThreshold * 100).toFixed(1)}%`);
    console.log(`   买入阈值: ${(this.tradingConfig.buyThreshold * 100).toFixed(1)}%`);
    console.log(`   最大仓位: ${this.tradingConfig.maxPositions}`);
    console.log(`   系统运行状态: ${this.isRunning ? '✅ 运行中' : '❌ 已停止'}`);
    
    // 2. 风险管理状态
    const riskCheck = this.riskManager.canTrade();
    console.log(`\n🛡️ 风险管理状态:`);
    console.log(`   交易允许: ${riskCheck.allowed ? '✅ 允许' : '❌ 禁止'}`);
    console.log(`   原因: ${riskCheck.reason}`);
    
    // 3. 仓位管理器状态
    const activePositions = this.positionManager.getActivePositions();
    console.log(`\n📊 仓位管理器状态:`);
    console.log(`   活跃仓位数: ${activePositions.length}`);
    console.log(`   可开新仓: ${this.positionManager.canOpenNewPosition() ? '✅ 可以' : '❌ 不可以'}`);
    
    if (activePositions.length > 0) {
      console.log(`   持仓详情:`);
      for (const position of activePositions) {
        console.log(`      Token: ${position.tokenAddress.slice(0, 8)}...`);
        console.log(`      数量: ${position.amount.toFixed(6)}`);
        console.log(`      入场价: ${position.entryPrice.toFixed(8)} SOL`);
        console.log(`      状态: ${position.status}`);
        console.log(`      ---`);
      }
    }
    
    // 4. Token实例状态检查
    console.log(`\n🪙 Token实例状态 (共${this.tokenInstances.size}个):`);
    let hasAnyPosition = false;
    
    for (const [tokenAddress, instance] of this.tokenInstances) {
      const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
      const hasInstancePosition = instance.currentHolding !== null && instance.currentHolding.amount > 0;
      const hasManagerPosition = this.positionManager.getPosition(tokenAddress) !== undefined;
      
      if (hasInstancePosition || hasManagerPosition) {
        hasAnyPosition = true;
        console.log(`\n   Token: ${tokenDisplay}`);
        console.log(`   📊 持仓状态:`);
        console.log(`      Instance持仓: ${hasInstancePosition ? '✅ 有' : '❌ 无'}`);
        console.log(`      Manager持仓: ${hasManagerPosition ? '✅ 有' : '❌ 无'}`);
        console.log(`      交易锁定: ${instance.isTrading ? '🔒 是' : '🔓 否'}`);
        
        if (instance.currentHolding) {
          const holdingTimeMs = Date.now() - instance.currentHolding.buyTime.getTime();
          const holdingMinutes = Math.floor(holdingTimeMs / (1000 * 60));
          
          console.log(`   💰 持仓详情:`);
          console.log(`      数量: ${instance.currentHolding.amount.toFixed(6)} tokens`);
          console.log(`      买入价: ${instance.currentHolding.buyPrice.toFixed(8)} SOL/token`);
          console.log(`      买入金额: ${instance.currentHolding.buySolAmount.toFixed(6)} SOL`);
          console.log(`      持仓时间: ${holdingMinutes}分钟`);
          console.log(`      买入时间: ${instance.currentHolding.buyTime.toISOString()}`);
        }
        
        // 获取最新AI预测
        try {
          const sellPrediction = await instance.sellPredictorV4.predictSell();
          const currentPrice = this.getLastTradePrice(tokenAddress);
          
          console.log(`   🤖 最新AI预测:`);
          console.log(`      卖出概率: ${(sellPrediction.probability * 100).toFixed(2)}%`);
          console.log(`      预测结果: ${sellPrediction.prediction ? '✅ 建议卖出' : '❌ 不建议卖出'}`);
          console.log(`      信心度: ${(sellPrediction.confidence * 100).toFixed(1)}%`);
          console.log(`      达到阈值: ${sellPrediction.probability >= this.tradingConfig.sellThreshold ? '✅ 是' : '❌ 否'}`);
          
          if (currentPrice && instance.currentHolding) {
            const priceChange = ((currentPrice / instance.currentHolding.buyPrice - 1) * 100);
            const currentValue = instance.currentHolding.amount * currentPrice;
            const unrealizedPnL = currentValue - instance.currentHolding.buySolAmount;
            
            console.log(`   💹 价格表现:`);
            console.log(`      当前价格: ${currentPrice.toFixed(8)} SOL/token`);
            console.log(`      价格变化: ${priceChange > 0 ? '+' : ''}${priceChange.toFixed(2)}%`);
            console.log(`      未实现盈亏: ${unrealizedPnL > 0 ? '+' : ''}${unrealizedPnL.toFixed(6)} SOL`);
            console.log(`      当前价值: ${currentValue.toFixed(6)} SOL`);
          }
          
          // 诊断为什么不卖出
          console.log(`   🔍 卖出决策分析:`);
          const sellConditions = [
            { name: '有持仓', met: hasInstancePosition, required: true },
            { name: '交易未锁定', met: !instance.isTrading, required: true },
            { name: 'AI预测达标', met: sellPrediction.probability >= this.tradingConfig.sellThreshold, required: true },
            { name: '风险允许交易', met: riskCheck.allowed, required: true }
          ];
          
          for (const condition of sellConditions) {
            console.log(`      ${condition.met ? '✅' : '❌'} ${condition.name}`);
          }
          
          const allConditionsMet = sellConditions.every(c => c.met);
          console.log(`   🎯 卖出条件: ${allConditionsMet ? '✅ 全部满足，应该卖出' : '❌ 条件不满足，无法卖出'}`);
          
          if (!allConditionsMet) {
            const failedConditions = sellConditions.filter(c => !c.met);
            console.log(`   ⚠️ 未满足的条件: ${failedConditions.map(c => c.name).join(', ')}`);
          }
          
        } catch (error) {
          console.log(`   ❌ AI预测失败: ${error}`);
        }
        
        console.log(`   ---`);
      }
    }
    
    if (!hasAnyPosition) {
      console.log(`   ❌ 没有发现任何持仓`);
    }
    
    // 5. 最近交易历史
    console.log(`\n📈 最近交易活动:`);
    let recentTrades = 0;
    for (const [tokenAddress, instance] of this.tokenInstances) {
      const recentSells = instance.tradingHistory
        .filter(trade => trade.type === 'sell')
        .filter(trade => Date.now() - trade.timestamp.getTime() < 3600000) // 最近1小时
        .length;
      
      if (recentSells > 0) {
        const tokenDisplay = await this.formatTokenDisplay(tokenAddress);
        console.log(`   ${tokenDisplay}: ${recentSells}次卖出 (最近1小时)`);
        recentTrades += recentSells;
      }
    }
    
    if (recentTrades === 0) {
      console.log(`   ❌ 最近1小时内没有卖出交易`);
    }
    
    console.log(`\n🎯 诊断建议:`);
    
    if (!hasAnyPosition) {
      console.log(`   💡 没有持仓，等待买入信号即可`);
    } else if (!riskCheck.allowed) {
      console.log(`   ⚠️ 风险管理阻止了交易，需要检查风险设置`);
    } else {
      // 检查是否所有持仓的AI预测都不足
      let allPredictionsLow = true;
      for (const [_, instance] of this.tokenInstances) {
        if (instance.currentHolding) {
          try {
            const sellPrediction = await instance.sellPredictorV4.predictSell();
            if (sellPrediction.probability >= this.tradingConfig.sellThreshold) {
              allPredictionsLow = false;
              break;
            }
          } catch {}
        }
      }
      
      if (allPredictionsLow) {
        console.log(`   💡 所有持仓的AI预测都低于卖出阈值 (${(this.tradingConfig.sellThreshold * 100).toFixed(1)}%)`);
        console.log(`   💡 这可能意味着:`);
        console.log(`      - AI认为还会继续上涨，建议继续持有`);
        console.log(`      - 市场数据不足，AI预测信心不够`);
        console.log(`      - 卖出阈值设置过高，可以考虑适当降低`);
        console.log(`   💡 如果急需卖出，可以:`);
        console.log(`      - 临时降低卖出阈值 (当前${(this.tradingConfig.sellThreshold * 100).toFixed(1)}%)`);
        console.log(`      - 手动执行止损卖出`);
        console.log(`      - 等待更多市场数据让AI做出判断`);
      }
    }
    
    console.log(`\n====== 诊断报告结束 ======\n`);
  }

  /**
   * 🔍 调试卖出价格计算问题
   */
  public async debugSellPriceCalculation(tokenAddress: string): Promise<void> {
    console.log(`\n🔍 ====== 卖出价格计算调试 ======`);
    console.log(`🪙 Token: ${tokenAddress}`);
    console.log(`⏰ 调试时间: ${new Date().toISOString()}\n`);
    
    const instance = this.tokenInstances.get(tokenAddress);
    if (!instance) {
      console.log(`❌ 未找到Token实例`);
      return;
    }
    
    if (!instance.currentHolding) {
      console.log(`❌ 当前没有持仓`);
      return;
    }
    
    console.log(`📊 当前持仓信息:`);
    console.log(`   持仓数量: ${instance.currentHolding.amount.toFixed(6)} tokens`);
    console.log(`   买入价格: ${instance.currentHolding.buyPrice.toFixed(8)} SOL/token`);
    console.log(`   买入金额: ${instance.currentHolding.buySolAmount.toFixed(6)} SOL`);
    console.log(`   买入时间: ${instance.currentHolding.buyTime.toISOString()}`);
    
    // 获取池子数据
    let poolData = this.globalPoolCache.get(tokenAddress);
    if (!poolData) {
      console.log(`⚠️ 没有缓存的池子数据，尝试重新创建...`);
      try {
        const tokenMint = new PublicKey(tokenAddress);
        poolData = await this.createPoolDataFromCache(tokenMint);
        this.globalPoolCache.set(tokenAddress, poolData);
      } catch (error) {
        console.error(`❌ 创建池子数据失败:`, error);
        return;
      }
    }
    
    console.log(`\n🏊 池子数据:`);
    console.log(`   Token余额: ${poolData.tokenBalance.toFixed(6)} tokens`);
    console.log(`   SOL余额: ${poolData.solBalance.toFixed(6)} SOL`);
    
    // 计算基础价格
    const basePrice = poolData.solBalance / poolData.tokenBalance;
    console.log(`   基础价格: ${basePrice.toFixed(8)} SOL/token`);
    
    // 模拟卖出价格计算
    const tokenAmount = instance.currentHolding.amount;
    const baseSolAmount = tokenAmount * basePrice;
    
    console.log(`\n💰 卖出价格计算模拟:`);
    console.log(`   卖出数量: ${tokenAmount.toFixed(6)} tokens`);
    console.log(`   基础SOL价值: ${baseSolAmount.toFixed(6)} SOL`);
    
    // 价格影响计算
    const poolTotalValue = poolData.solBalance + (poolData.tokenBalance * basePrice);
    const tradeImpactRatio = baseSolAmount / poolTotalValue;
    const priceImpact = Math.min(tradeImpactRatio * 0.5, 0.02);
    const slippageLoss = 0.005; // 固定0.5%用于调试
    const tradingFee = 0.003;
    
    const totalPriceChange = priceImpact + slippageLoss + tradingFee;
    const estimatedExecutionPrice = basePrice * (1 - totalPriceChange);
    const estimatedSolReceived = tokenAmount * estimatedExecutionPrice;
    
    console.log(`   价格影响: ${(priceImpact * 100).toFixed(3)}%`);
    console.log(`   滑点损失: ${(slippageLoss * 100).toFixed(3)}%`);
    console.log(`   手续费: ${(tradingFee * 100).toFixed(1)}%`);
    console.log(`   总价格变化: ${(totalPriceChange * 100).toFixed(3)}%`);
    console.log(`   预估执行价格: ${estimatedExecutionPrice.toFixed(8)} SOL/token`);
    console.log(`   预估收到SOL: ${estimatedSolReceived.toFixed(6)} SOL`);
    
    // 计算盈亏
    const estimatedProfit = estimatedSolReceived - instance.currentHolding.buySolAmount;
    const estimatedProfitPercentage = ((estimatedSolReceived / instance.currentHolding.buySolAmount - 1) * 100);
    
    console.log(`\n📈 预估盈亏:`);
    console.log(`   预估净收益: ${estimatedProfit > 0 ? '+' : ''}${estimatedProfit.toFixed(6)} SOL`);
    console.log(`   预估收益率: ${estimatedProfitPercentage > 0 ? '+' : ''}${estimatedProfitPercentage.toFixed(2)}%`);
    
    // 获取最新价格数据
    const lastTradePrice = this.getLastTradePrice(tokenAddress);
    if (lastTradePrice) {
      console.log(`\n📊 最新市场价格:`);
      console.log(`   最新交易价格: ${lastTradePrice.toFixed(8)} SOL/token`);
      console.log(`   与基础价格差异: ${((lastTradePrice / basePrice - 1) * 100).toFixed(2)}%`);
      console.log(`   与买入价格差异: ${((lastTradePrice / instance.currentHolding.buyPrice - 1) * 100).toFixed(2)}%`);
    }
    
    // 检查真实余额
    try {
      const realBalance = await this.getRealTokenBalance(tokenAddress);
      console.log(`\n🔍 链上真实余额:`);
      console.log(`   实际余额: ${realBalance.toFixed(6)} tokens`);
      console.log(`   与记录差异: ${(realBalance - instance.currentHolding.amount).toFixed(6)} tokens`);
      
      if (Math.abs(realBalance - instance.currentHolding.amount) > 0.001) {
        console.log(`   ⚠️ 余额不一致，建议更新持仓记录`);
      }
    } catch (error) {
      console.log(`   ❌ 获取真实余额失败: ${error}`);
    }
    
    console.log(`\n🎯 调试建议:`);
    console.log(`   1. 检查池子数据是否最新`);
    console.log(`   2. 验证价格计算公式是否正确`);
    console.log(`   3. 确认交易解析逻辑是否准确`);
    console.log(`   4. 检查Telegram通知的数据传递`);
    
    console.log(`\n====== 调试完成 ======\n`);
  }

  /**
   * 🚨 紧急清理交易锁定状态
   */
  public emergencyUnlockTradingStates(): void {
    console.log(`🚨 开始紧急清理所有交易锁定状态...`);
    let unlockedCount = 0;
    
    for (const [tokenAddress, instance] of this.tokenInstances) {
      if (instance.isTrading) {
        console.log(`🔓 解锁交易状态: ${tokenAddress.slice(0, 8)}...`);
        instance.isTrading = false;
        unlockedCount++;
        
        // 清理pending交易记录
        const pendingTxs = Array.from(instance.pendingTransactions.keys());
        if (pendingTxs.length > 0) {
          console.log(`🧹 清理pending交易记录: ${pendingTxs.length}个`);
          instance.pendingTransactions.clear();
        }
      }
    }
    
    console.log(`✅ 紧急清理完成，共解锁 ${unlockedCount} 个Token的交易状态`);
  }

  /**
   * 🔍 检查并清理长时间锁定的交易状态
   */
  public checkAndCleanStuckTrades(): void {
    const STUCK_TIMEOUT = 180000; // 3分钟超时
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [tokenAddress, instance] of this.tokenInstances) {
      if (instance.isTrading) {
        // 检查pending交易是否超时
        for (const [signature, pendingTx] of instance.pendingTransactions) {
          const txAge = now - pendingTx.timestamp.getTime();
          if (txAge > STUCK_TIMEOUT) {
            console.log(`🚨 发现卡住的交易 ${tokenAddress.slice(0, 8)}... 超时${Math.floor(txAge/1000)}秒`);
            console.log(`🧹 清理卡住的交易状态...`);
            
            // 强制清理
            instance.isTrading = false;
            instance.pendingTransactions.delete(signature);
            cleanedCount++;
            
            console.log(`✅ 已清理卡住的交易: ${signature.slice(0, 8)}...`);
          }
        }
      }
    }
    
    if (cleanedCount > 0) {
      console.log(`🧹 清理完成，共处理 ${cleanedCount} 个卡住的交易`);
    }
  }

  /**
   * 🔥 新增：检查和修复仓位状态一致性
   */
  public async validateAndFixPositionConsistency(): Promise<void> {
    console.log('\n🔍 ===== 开始仓位状态一致性检查 =====');
    
    const managerPositions = this.positionManager.getActivePositions();
    const instancePositions: Array<{tokenAddress: string, amount: number}> = [];
    
    // 收集TokenInstance中的持仓信息
    for (const [tokenAddress, instance] of this.tokenInstances) {
      if (instance.currentHolding && instance.currentHolding.amount > 0.001) {
        instancePositions.push({
          tokenAddress,
          amount: instance.currentHolding.amount
        });
      }
    }
    
    console.log(`📊 仓位统计:`);
    console.log(`   PositionManager仓位数: ${managerPositions.length}`);
    console.log(`   TokenInstance持仓数: ${instancePositions.length}`);
    console.log(`   最大仓位限制: ${this.tradingConfig.maxPositions}`);
    
    // 检查超限情况
    if (managerPositions.length > this.tradingConfig.maxPositions) {
      console.log(`⚠️ PositionManager仓位数超限: ${managerPositions.length}/${this.tradingConfig.maxPositions}`);
    }
    
    // 检查不一致的仓位
    let fixedCount = 0;
    
    // 1. 检查PositionManager中存在但TokenInstance中不存在的仓位
    for (const position of managerPositions) {
      const instance = this.tokenInstances.get(position.tokenAddress);
      if (!instance || !instance.currentHolding || instance.currentHolding.amount <= 0.001) {
        console.log(`🔧 修复：移除PositionManager中的孤立仓位 ${position.tokenAddress.slice(0, 8)}...`);
        this.positionManager.closePosition(position.tokenAddress, 0, 0);
        fixedCount++;
      }
    }
    
    // 2. 检查TokenInstance中存在但PositionManager中不存在的仓位
    for (const instancePos of instancePositions) {
      const managerPos = this.positionManager.getPosition(instancePos.tokenAddress);
      if (!managerPos) {
        // 检查是否还有仓位空间
        const currentCount = this.positionManager.getActivePositions().length;
        if (currentCount < this.tradingConfig.maxPositions) {
          console.log(`🔧 修复：添加缺失的PositionManager仓位 ${instancePos.tokenAddress.slice(0, 8)}...`);
          const instance = this.tokenInstances.get(instancePos.tokenAddress);
          if (instance && instance.currentHolding) {
            this.positionManager.openPosition(
              instancePos.tokenAddress, 
              instance.currentHolding.amount, 
              instance.currentHolding.buyPrice
            );
            fixedCount++;
          }
        } else {
          console.log(`⚠️ 无法添加仓位（已达上限），清理TokenInstance持仓 ${instancePos.tokenAddress.slice(0, 8)}...`);
          const instance = this.tokenInstances.get(instancePos.tokenAddress);
          if (instance) {
            instance.currentHolding = null;
            fixedCount++;
          }
        }
      }
    }
    
    // 3. 检查数量不一致的仓位
    for (const position of managerPositions) {
      const instance = this.tokenInstances.get(position.tokenAddress);
      if (instance && instance.currentHolding) {
        const amountDiff = Math.abs(instance.currentHolding.amount - position.amount);
        if (amountDiff > 0.001) {
          console.log(`🔧 修复：同步仓位数量 ${position.tokenAddress.slice(0, 8)}... (${position.amount.toFixed(6)} -> ${instance.currentHolding.amount.toFixed(6)})`);
          position.amount = instance.currentHolding.amount;
          fixedCount++;
        }
      }
    }
    
    const finalManagerPositions = this.positionManager.getActivePositions();
    const finalInstancePositions = Array.from(this.tokenInstances.values())
      .filter(i => i.currentHolding && i.currentHolding.amount > 0.001);
    
    console.log(`✅ 一致性检查完成:`);
    console.log(`   修复问题数: ${fixedCount}`);
    console.log(`   最终PositionManager仓位数: ${finalManagerPositions.length}/${this.tradingConfig.maxPositions}`);
    console.log(`   最终TokenInstance持仓数: ${finalInstancePositions.length}`);
    console.log(`   状态一致: ${finalManagerPositions.length === finalInstancePositions.length ? '✅' : '❌'}`);
    console.log('=====================================\n');
  }

  /**
   * 🔥 新增：检查Token最近交易量
   */
  private getRecentTransactionCount(tokenAddress: string, minutes: number): number {
    const now = Date.now();
    const timeWindow = minutes * 60 * 1000; // 转换为毫秒
    const cutoffTime = now - timeWindow;
    
    // 检查tokenActivities中的交易记录
    const activity = this.tokenActivities.get(tokenAddress);
    let activityCount = 0;
    if (activity) {
      activityCount = activity.actions.filter(action => 
        action.timestamp.getTime() >= cutoffTime
      ).length;
    }
    
    // 检查realPriceData中的交易记录（更准确的实时数据）
    const priceData = this.realPriceData.get(tokenAddress);
    let priceDataCount = 0;
    if (priceData) {
      priceDataCount = priceData.filter(data => 
        data.timestamp.getTime() >= cutoffTime
      ).length;
    }
    
    // 🔥 新增：检查token实例的tradingHistory
    const instance = this.tokenInstances.get(tokenAddress);
    let tradingHistoryCount = 0;
    if (instance && instance.tradingHistory) {
      tradingHistoryCount = instance.tradingHistory.filter(trade => 
        trade.timestamp.getTime() >= cutoffTime
      ).length;
    }
    
    // 🔥 新增：检查DynamicSubscriptionManager的活跃度
    const isActivelyMonitored = this.subscriptionManager.getActiveTokens().includes(tokenAddress);
    const hasOpenPosition = this.positionManager.getPosition(tokenAddress) !== undefined;
    
    // 返回所有数据源中的最大值（更保守的策略）
    const maxCount = Math.max(activityCount, priceDataCount, tradingHistoryCount);
    
    console.log(`📊 [${tokenAddress.slice(0, 8)}...] 交易量统计 (最近${minutes}分钟):`);
    console.log(`   tokenActivities: ${activityCount} 笔`);
    console.log(`   realPriceData: ${priceDataCount} 笔`);
    console.log(`   tradingHistory: ${tradingHistoryCount} 笔`);
    console.log(`   最终计数: ${maxCount} 笔`);
    console.log(`   订阅状态: ${isActivelyMonitored ? '✅ 主动监控' : '❌ 未监控'}`);
    console.log(`   持仓状态: ${hasOpenPosition ? '✅ 有持仓' : '❌ 无持仓'}`);
    
    // 🔥 如果所有数据源都为0但有持仓，则警告可能的数据问题
    if (maxCount === 0 && hasOpenPosition) {
      console.warn(`⚠️ 警告: 持仓token但交易量为0，可能存在数据同步问题!`);
      console.warn(`   建议检查订阅状态和数据流连接`);
    }
    
    return maxCount;
  }

  /**
   * 🔥 新增：输出当前持仓情况
   */
  private printCurrentPositions(): void {
    console.log('\n💼 ===== 当前持仓情况 =====');
    
    const managerPositions = this.positionManager.getActivePositions();
    let instancePositions = 0;
    
    console.log(`📊 PositionManager仓位数: ${managerPositions.length}/${this.tradingConfig.maxPositions}`);
    
    if (managerPositions.length === 0) {
      console.log('📭 当前无持仓');
    } else {
      for (const position of managerPositions) {
        const instance = this.tokenInstances.get(position.tokenAddress);
        const tokenDisplay = position.tokenAddress.slice(0, 8) + '...';
        
        if (instance && instance.currentHolding) {
          instancePositions++;
          const currentPrice = this.getLastTradePrice(position.tokenAddress) || instance.currentHolding.buyPrice;
          const currentValue = instance.currentHolding.amount * currentPrice;
          const pnl = currentValue - instance.currentHolding.buySolAmount;
          const holdingTimeMs = Date.now() - instance.currentHolding.buyTime.getTime();
          const holdingMinutes = Math.floor(holdingTimeMs / (1000 * 60));
          
          // 检查最近交易量
          const recentTxCount = this.getRecentTransactionCount(
            position.tokenAddress, 
            this.tradingConfig.lowVolumeCheckMinutes
          );
          
          console.log(`🪙 ${tokenDisplay}:`);
          console.log(`   💰 数量: ${instance.currentHolding.amount.toFixed(6)} tokens`);
          console.log(`   💵 买入价: ${instance.currentHolding.buyPrice.toFixed(8)} SOL/token`);
          console.log(`   💲 当前价: ${currentPrice.toFixed(8)} SOL/token`);
          console.log(`   📈 盈亏: ${pnl > 0 ? '+' : ''}${pnl.toFixed(4)} SOL (${((pnl/instance.currentHolding.buySolAmount)*100).toFixed(1)}%)`);
          console.log(`   ⏱️ 持仓时间: ${holdingMinutes}分钟`);
          console.log(`   📊 最近${this.tradingConfig.lowVolumeCheckMinutes}分钟交易量: ${recentTxCount} 笔`);
          console.log(`   🔄 是否交易中: ${instance.isTrading ? '是' : '否'}`);
          
          // 低交易量警告
          if (this.tradingConfig.lowVolumeAutoSell && recentTxCount < this.tradingConfig.lowVolumeThreshold) {
            console.log(`   ⚠️ 交易量不足警告: ${recentTxCount} < ${this.tradingConfig.lowVolumeThreshold} (将触发自动卖出)`);
          }
        } else {
          console.log(`🪙 ${tokenDisplay}: ❌ 状态不一致 (PositionManager有记录但Instance无持仓)`);
        }
      }
    }
    
    console.log(`📊 状态一致性: PositionManager=${managerPositions.length}, TokenInstance=${instancePositions}`);
    console.log('===============================\n');
  }

  /**
   * 🔥 处理自己钱包的交易（获取真实交易数据）
   */
  private async processMyWalletTransaction(txn: any, data: any, parsedTxn: any, transactionTimestamp?: Date): Promise<void> {
    try {
      // 🔥 防止重复处理同一交易
      const txSignature = data.transaction?.transaction?.signatures?.[0];
      if (txSignature && this.processedTransactions.has(txSignature)) {
        console.log(`⏭️ 跳过已处理的交易: ${txSignature.slice(0, 8)}...`);
        return;
      }
      
      const walletPublicKey = new PublicKey(MY_WALLET);
      const extractedInfo = await this.extractTokenInfoFromTransaction(parsedTxn, true);
      
      console.log(`💰 检测到自己钱包交易: ${MY_WALLET.slice(0, 8)}...`);
      
      // 🔥 记录已处理的交易
      if (txSignature) {
        this.processedTransactions.add(txSignature);
        // 🔥 限制缓存大小，避免内存泄漏
        if (this.processedTransactions.size > 1000) {
          const firstTx = this.processedTransactions.values().next().value;
          this.processedTransactions.delete(firstTx);
        }
      }
      
      if (extractedInfo && extractedInfo.tokens) {
        for (const tokenInfo of extractedInfo.tokens) {
          if (tokenInfo.action === 'buy' || tokenInfo.action === 'sell') {
            const tokenDisplay = await this.formatTokenDisplay(tokenInfo.address);
            
            // 🔥 从交易记录中提取真实的数据
            let realSolAmount = 0;
            let realTokenAmount = 0;
            
            if (tokenInfo.action === 'buy') {
              // 🔥 修复：优先使用GRPC事件中已经提取的数据
              realSolAmount = tokenInfo.solAmount || 0;
              realTokenAmount = tokenInfo.tokenAmount || 0;
              
              console.log(`🔍 从GRPC事件提取买入交易数据:`);
              console.log(`   Token地址: ${tokenInfo.address}`);
              console.log(`   花费SOL数量: ${realSolAmount.toFixed(9)} SOL`);
              console.log(`   获得Token数量: ${realTokenAmount.toFixed(6)} 个`);
              
              // 🔥 如果GRPC事件中没有完整数据，才从交易数据中提取
              if (realSolAmount === 0) {
                console.log(`⚠️ GRPC事件中无SOL数据，尝试从交易数据提取...`);
                realSolAmount = this.extractSolSpentFromTransaction(txn, walletPublicKey);
                console.log(`📊 交易数据提取结果: ${realSolAmount.toFixed(9)} SOL`);
              }
              if (realTokenAmount === 0) {
                console.log(`⚠️ GRPC事件中无Token数据，尝试从交易数据提取...`);
                realTokenAmount = this.extractTokenAmountFromTransaction(txn, tokenInfo.address, walletPublicKey);
                console.log(`📊 交易数据提取结果: ${realTokenAmount.toFixed(6)} 个`);
              }
              
              if (realSolAmount > 0 && realTokenAmount > 0) {
                // 🔥 修复：使用统一的标准化函数计算买入价格
                const normalizedTokenAmount = this.normalizeTokenAmount(realTokenAmount);
                
                // 验证标准化后的Token数量
                if (normalizedTokenAmount <= 0) {
                  console.error(`❌ 买入价格计算失败: 标准化Token数量为0 (原始: ${realTokenAmount.toExponential(3)})`);
                  return;
                }
                
                const realBuyPrice = realSolAmount / normalizedTokenAmount;
                
                // 验证价格合理性
                if (!isFinite(realBuyPrice) || realBuyPrice <= 0) {
                  console.error(`❌ 买入价格计算异常: ${realBuyPrice}`);
                  console.error(`   SOL: ${realSolAmount}, Token标准化: ${normalizedTokenAmount}`);
                  return;
                }
                
                console.log(`✅ 真实买入数据提取成功:`);
                console.log(`   Token: ${tokenDisplay}`);
                console.log(`   实际花费SOL: ${realSolAmount.toFixed(6)} SOL`);
                console.log(`   实际获得Token: ${realTokenAmount.toFixed(6)} 个 (原始)`);
                console.log(`   标准化Token: ${normalizedTokenAmount.toFixed(6)} 个 (标准化)`);
                console.log(`   真实买入价格: ${realBuyPrice.toFixed(8)} SOL/token`);
                
                // 🔥 获取对应的实例并更新持仓信息
                let instance = this.tokenInstances.get(tokenInfo.address);
                if (instance) {
                  // 如果有持仓记录，更新为真实数据
                  if (instance.currentHolding) {
                    const oldBuySolAmount = instance.currentHolding.buySolAmount;
                    instance.currentHolding.amount = realTokenAmount;
                    instance.currentHolding.buyPrice = realBuyPrice;
                    instance.currentHolding.buySolAmount = realSolAmount;
                    instance.stats.currentPosition = realTokenAmount;
                    
                    console.log(`📊 已更新持仓为真实数据:`);
                    console.log(`   旧买入成本: ${oldBuySolAmount.toFixed(8)} SOL`);
                    console.log(`   新买入成本: ${realSolAmount.toFixed(8)} SOL (GRPC实际花费)`);
                    console.log(`   成本差异: ${(realSolAmount - oldBuySolAmount).toFixed(8)} SOL`);
                  }
                  
                  // 记录真实价格数据
                  this.recordRealPrice(tokenInfo.address, realSolAmount, realTokenAmount, 'buy', 'own_trade');
                }
                
                // 🔥 修复：从pending交易中获取实际的买入预测值并标记为已确认
                let buyPrediction = 0;
                const txSignature = data.transaction?.transaction?.signatures?.[0];
                if (txSignature && instance.pendingTransactions.has(txSignature)) {
                  const pendingTx = instance.pendingTransactions.get(txSignature);
                  if (pendingTx && pendingTx.type === 'buy') {
                    // 🔥 标记交易为已确认
                    pendingTx.confirmed = true;
                    console.log(`✅ 买入交易已通过GRPC确认: ${txSignature}`);
                    
                    // 从交易历史中查找最近的买入预测
                    const recentBuyTrades = instance.tradingHistory
                      .filter(trade => trade.type === 'buy')
                      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
                    if (recentBuyTrades.length > 0) {
                      buyPrediction = recentBuyTrades[0].prediction;
                    }
                    
                    // 🔥 清理已确认的pending交易
                    setTimeout(() => {
                      instance.pendingTransactions.delete(txSignature);
                    }, 1000);
                  }
                }
                
                // 如果还是没有找到，使用最后一次AI预测结果
                if (buyPrediction === 0) {
                  // 🔥 修复：保持0-1小数格式，不再乘以100
                  const rawPrediction = instance.lastBuyPrediction || 0;
                  buyPrediction = rawPrediction; // 保持原始格式，在Telegram通知中再乘以100
                  console.log(`🔍 买入预测值调试: 原始=${rawPrediction.toFixed(6)}, 最终=${buyPrediction.toFixed(6)} (${(buyPrediction * 100).toFixed(1)}%)`);
                }
                
                // 🔥 修复价格一致性：等待持仓记录创建完成后再发送通知
                let finalBuyPrice = realBuyPrice;
                // 重用之前获取的instance变量

                // 检查是否有pending的买入交易，如果有就等待持仓记录创建
                if (instance && instance.pendingTransactions.size > 0) {
                  console.log(`⏳ 检测到pending买入交易，等待持仓记录创建...`);

                  // 等待最多5秒让持仓记录创建完成
                  let waitAttempts = 0;
                  const maxWaitAttempts = 10; // 5秒 (500ms * 10)

                  while (waitAttempts < maxWaitAttempts && (!instance.currentHolding || instance.currentHolding.amount <= 0)) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                    waitAttempts++;
                    console.log(`   等待持仓记录... (${waitAttempts}/${maxWaitAttempts})`);
                  }
                }

                // 使用持仓记录中的价格（如果存在）
                if (instance && instance.currentHolding && instance.currentHolding.buyPrice > 0) {
                  finalBuyPrice = instance.currentHolding.buyPrice;
                  console.log(`🔍 价格一致性修复:`);
                  console.log(`   交易流价格: ${realBuyPrice.toFixed(12)} SOL/token`);
                  console.log(`   持仓记录价格: ${finalBuyPrice.toFixed(12)} SOL/token`);
                  console.log(`   价格差异: ${Math.abs(realBuyPrice - finalBuyPrice).toFixed(12)} SOL/token`);
                } else {
                  console.log(`⚠️ 持仓记录未创建，使用交易流价格: ${finalBuyPrice.toFixed(12)} SOL/token`);
                }

                // 发送Telegram通知（使用持仓记录中的价格确保一致性）
                await this.telegramNotifier.notifyBuy(
                  tokenInfo.address,
                  realSolAmount,
                  realTokenAmount,
                  finalBuyPrice, // 🔥 修复：使用持仓记录中的价格确保与卖出通知一致
                  buyPrediction, // 🔥 修复：使用实际的买入预测值
                  data.transaction?.transaction?.signatures?.[0] || 'unknown',
                  undefined, // slippage
                  undefined, // gasFee
                  await this.getTokenSymbol(tokenInfo.address)
                );
              }
              
            } else if (tokenInfo.action === 'sell') {
              // 🔥 修复：直接使用GRPC事件中已经提取的SOL数量
              realTokenAmount = tokenInfo.tokenAmount || 0;
              realSolAmount = tokenInfo.solAmount || 0;
              
              console.log(`🔍 从GRPC事件提取卖出交易数据:`);
              console.log(`   Token地址: ${tokenInfo.address}`);
              console.log(`   卖出Token数量: ${realTokenAmount.toFixed(6)} 个`);
              console.log(`   收到SOL数量: ${realSolAmount.toFixed(9)} SOL`);
              
              // 🔥 如果GRPC事件中没有SOL数量，才尝试从交易数据中提取
              if (realSolAmount === 0) {
                console.log(`⚠️ GRPC事件中无SOL数据，尝试从交易数据提取...`);
                realSolAmount = this.extractSolReceivedFromSellTransaction(txn, walletPublicKey);
                console.log(`📊 交易数据提取结果: ${realSolAmount.toFixed(9)} SOL`);
              }
              
              if (realSolAmount > 0 && realTokenAmount > 0) {
                // 🔥 修复：使用统一的标准化函数计算卖出价格
                const normalizedTokenAmount = this.normalizeTokenAmount(realTokenAmount);
                
                // 验证标准化后的Token数量
                if (normalizedTokenAmount <= 0) {
                  console.error(`❌ 卖出价格计算失败: 标准化Token数量为0 (原始: ${realTokenAmount.toExponential(3)})`);
                  return;
                }
                
                const realSellPrice = realSolAmount / normalizedTokenAmount;
                
                // 验证价格合理性
                if (!isFinite(realSellPrice) || realSellPrice <= 0) {
                  console.error(`❌ 卖出价格计算异常: ${realSellPrice}`);
                  console.error(`   SOL: ${realSolAmount}, Token标准化: ${normalizedTokenAmount}`);
                  return;
                }
                
                console.log(`✅ 真实卖出数据提取成功:`);
                console.log(`   Token: ${tokenDisplay}`);
                console.log(`   实际卖出Token: ${realTokenAmount.toFixed(6)} 个 (原始)`);
                console.log(`   标准化Token: ${normalizedTokenAmount.toFixed(6)} 个 (标准化)`);
                console.log(`   实际收到SOL: ${realSolAmount.toFixed(6)} SOL`);
                console.log(`   真实卖出价格: ${realSellPrice.toFixed(8)} SOL/token`);
                
                // 🔥 获取对应的实例并更新持仓信息
                const instance = this.tokenInstances.get(tokenInfo.address);
                if (instance && instance.currentHolding) {
                  const buyPrice = instance.currentHolding.buyPrice;
                  const buySolAmount = instance.currentHolding.buySolAmount;
                  const profit = realSolAmount - buySolAmount;
                  const profitPercentage = ((realSolAmount / buySolAmount - 1) * 100);
                  const holdingTimeMinutes = (Date.now() - instance.currentHolding.buyTime.getTime()) / (1000 * 60);
                  
                  console.log(`💰 收益计算详情:`);
                  console.log(`   买入成本: ${buySolAmount.toFixed(9)} SOL (包含手续费)`);
                  console.log(`   卖出收入: ${realSolAmount.toFixed(9)} SOL (净收入)`);
                  console.log(`   买入价格: ${buyPrice.toFixed(8)} SOL/token`);
                  console.log(`   卖出价格: ${realSellPrice.toFixed(8)} SOL/token`);
                  console.log(`   价格变化: ${((realSellPrice / buyPrice - 1) * 100).toFixed(2)}%`);
                  console.log(`   绝对收益: ${profit > 0 ? '+' : ''}${profit.toFixed(9)} SOL`);
                  console.log(`   收益率计算: (${realSolAmount.toFixed(9)} / ${buySolAmount.toFixed(9)} - 1) * 100 = ${profitPercentage.toFixed(2)}%`);
                  console.log(`   持仓时间: ${holdingTimeMinutes.toFixed(1)} 分钟`);
                  
                  // 🔥 验证盈亏计算的合理性
                  const expectedProfitFromPrice = ((realSellPrice / buyPrice - 1) * buySolAmount);
                  const actualProfit = profit;
                  const profitDifference = Math.abs(expectedProfitFromPrice - actualProfit);
                  
                  console.log(`🔍 盈亏计算验证:`);
                  console.log(`   基于价格变化的预期收益: ${expectedProfitFromPrice > 0 ? '+' : ''}${expectedProfitFromPrice.toFixed(9)} SOL`);
                  console.log(`   实际收益: ${actualProfit > 0 ? '+' : ''}${actualProfit.toFixed(9)} SOL`);
                  console.log(`   差异: ${profitDifference.toFixed(9)} SOL ${profitDifference > 0.000001 ? '⚠️ 异常' : '✅ 正常'}`);
                  
                  // 更新统计数据
                  this.riskManager.recordTrade(profit, TIP_AMOUNT + PRIORITY_FEE);
                  this.positionManager.closePosition(tokenInfo.address, realSellPrice, profit);
                  
                  // 🔥 修复：从pending交易中获取实际的卖出预测值并标记为已确认
                  let sellPrediction = 0;
                  const txSignature = data.transaction?.transaction?.signatures?.[0];
                  if (txSignature && instance.pendingTransactions.has(txSignature)) {
                    const pendingTx = instance.pendingTransactions.get(txSignature);
                    if (pendingTx && pendingTx.type === 'sell') {
                      // 🔥 标记交易为已确认
                      pendingTx.confirmed = true;
                      console.log(`✅ 卖出交易已通过GRPC确认: ${txSignature}`);
                      
                      // 从交易历史中查找最近的卖出预测
                      const recentSellTrades = instance.tradingHistory
                        .filter(trade => trade.type === 'sell')
                        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
                      if (recentSellTrades.length > 0) {
                        sellPrediction = recentSellTrades[0].prediction;
                      }
                      
                      // 🔥 清理已确认的pending交易
                      setTimeout(() => {
                        instance.pendingTransactions.delete(txSignature);
                      }, 1000);
                    }
                  }
                  
                  // 如果还是没有找到，使用最后一次AI预测结果
                  if (sellPrediction === 0) {
                    // 🔥 修复：保持0-1小数格式，不再乘以100
                    const rawPrediction = instance.lastSellPrediction || 0;
                    sellPrediction = rawPrediction; // 保持原始格式，在Telegram通知中再乘以100
                    console.log(`🔍 卖出预测值调试: 原始=${rawPrediction.toFixed(6)}, 最终=${sellPrediction.toFixed(6)} (${(sellPrediction * 100).toFixed(1)}%)`);
                  }
                  
                  // 记录交易历史
                  instance.tradingHistory.push({
                    type: 'sell',
                    timestamp: new Date(),
                    tokenAmount: realTokenAmount,
                    solAmount: realSolAmount,
                    price: realSellPrice,
                    prediction: sellPrediction,
                    bundleId: data.transaction?.transaction?.signatures?.[0] || 'unknown'
                  });
                  
                  // 清空持仓记录
                  instance.currentHolding = null;
                  instance.stats.currentPosition = 0;
                  instance.stats.successfulTrades++;
                  instance.stats.totalPnL += profit;
                  instance.lastTradeTime = new Date();
                  instance.lastConfirmedSellTime = new Date();
                  
                  // 记录真实价格数据
                  this.recordRealPrice(tokenInfo.address, realSolAmount, realTokenAmount, 'sell', 'own_trade');
                  
                  // 发送Telegram通知（包含真实数据）
                  await this.telegramNotifier.notifySell(
                    tokenInfo.address,
                    realTokenAmount,
                    realSolAmount,
                    realSellPrice,
                    buyPrice,
                    buySolAmount, // originalSolAmount - 原始买入时花费的SOL
                    holdingTimeMinutes,
                    sellPrediction, // 🔥 修复：使用实际的卖出预测值
                    data.transaction?.transaction?.signatures?.[0] || 'unknown',
                    undefined, // slippage
                    undefined, // gasFee
                    await this.getTokenSymbol(tokenInfo.address)
                  );
                }
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('处理自己钱包交易时出错:', error);
    }
  }
}

// 主函数
async function main() {
  // 🔥 获取实际SOL余额并更新配置
  const actualSolBalance = await getWalletSolBalance();
  REAL_TRADING_CONFIG.trading!.initialCapitalSol = actualSolBalance;
  console.log(`✅ 初始资金已更新为: ${actualSolBalance.toFixed(6)} SOL`);
  
  const realTrading = new RealTradingWithTracker();
  let shutdownRequested = false;
  
  // 🔥 Enhanced signal handling
  process.on('SIGINT', async () => {
    if (shutdownRequested) {
      console.log('\n🔴 强制退出程序...');
      process.exit(1);
    }
    
    shutdownRequested = true;
    console.log('\n🛑 收到停止信号，正在安全关闭Real Trading系统...');
    
    try {
      // 给关闭过程10秒超时
      const shutdownTimeout = setTimeout(() => {
        console.log('❌ 关闭超时，强制退出');
        process.exit(1);
      }, 10000);
      
      await realTrading.stop();
      clearTimeout(shutdownTimeout);
      console.log('✅ Real Trading系统已安全关闭');
      process.exit(0);
    } catch (error) {
      console.error('❌ 关闭过程中发生错误:', error);
      process.exit(1);
    }
  });

  // 🔥 Enhanced error handling for uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
    console.error('Stack trace:', error.stack);
    
    // 尝试安全关闭，但不等太久
    if (!shutdownRequested) {
      console.log('🔄 尝试安全重启系统...');
      shutdownRequested = true;
      
      setTimeout(async () => {
        try {
          await realTrading.stop();
        } catch (stopError) {
          console.error('关闭时发生错误:', stopError);
        }
        
        // 重启系统而不是退出
        console.log('🚀 重启系统...');
        shutdownRequested = false;
        setTimeout(() => {
          startWithRetry(realTrading);
        }, 2000);
      }, 1000);
    }
  });

  // 🔥 Enhanced error handling for unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    console.error('Promise:', promise);
    
    // 网络相关错误不应该导致程序退出
    if (reason && typeof reason === 'object' && 'code' in reason) {
      const errorCode = (reason as any).code;
      if (errorCode === 'UND_ERR_CONNECT_TIMEOUT' || 
          errorCode === 'ECONNABORTED' ||
          errorCode === 'ECONNREFUSED' ||
          errorCode === 'ENOTFOUND' ||
          errorCode === 'ETIMEDOUT') {
        console.log('🔄 检测到网络错误，将继续运行...');
        return;
      }
    }
    
    // 对于其他类型的Promise拒绝，记录但不退出
    console.log('⚠️ Promise拒绝已记录，系统继续运行...');
  });

  // 🔥 Robust startup with retry mechanism
  async function startWithRetry(tradingInstance: RealTradingWithTracker, maxRetries: number = 5): Promise<void> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      if (shutdownRequested) {
        console.log('🛑 检测到关闭请求，停止重试');
        break;
      }

      try {
        console.log(`🚀 启动Real Trading系统 (尝试 ${attempt}/${maxRetries})...`);
        await tradingInstance.start();
        
        console.log('✅ Real Trading系统启动成功');
        
        // 🔥 如果设置了诊断模式，则运行诊断
        if (process.env.DIAGNOSE_SELL === 'true') {
          console.log('\n🔍 检测到诊断模式，将在启动后10秒执行卖出问题诊断...\n');
          
          setTimeout(async () => {
            try {
              await tradingInstance.diagnoseSellIssues();
            } catch (diagError) {
              console.error('❌ 诊断过程出错:', diagError);
            }
          }, 10000); // 10秒后执行诊断
        }
        
        // 🔥 Main event loop with error recovery
        while (!shutdownRequested) {
          try {
            // 简单的心跳检查
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 检查系统状态
            if (!tradingInstance.isRunning) {
              console.log('⚠️ 检测到系统已停止，尝试重启...');
              throw new Error('System stopped unexpectedly');
            }
            
          } catch (loopError) {
            console.error('❌ 主循环错误:', loopError);
            
            // 如果是网络相关错误，等待后重试
            if (isNetworkError(loopError)) {
              console.log('🔄 网络错误，等待10秒后重试...');
              await new Promise(resolve => setTimeout(resolve, 10000));
              continue;
            }
            
            // 对于其他错误，重启系统
            throw loopError;
          }
        }
        
        break; // 如果到达这里说明正常退出
        
      } catch (error) {
        console.error(`❌ Real Trading系统启动失败 (尝试 ${attempt}/${maxRetries}):`, error);
        
        // 尝试停止当前实例
        try {
          await tradingInstance.stop();
        } catch (stopError) {
          console.error('停止系统时发生错误:', stopError);
        }
        
        if (attempt === maxRetries) {
          console.error('❌ 所有重试都失败了，程序将退出');
          process.exit(1);
        }
        
        // 计算退避延迟（指数退避）
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 30000);
        console.log(`⏳ 等待 ${delay/1000} 秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // 🔥 Network error detection helper
  function isNetworkError(error: any): boolean {
    if (!error) return false;
    
    const networkErrorCodes = [
      'UND_ERR_CONNECT_TIMEOUT',
      'ECONNABORTED',
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      'ECONNRESET',
      'EPIPE',
      'EHOSTUNREACH',
      'ENETUNREACH'
    ];
    
    const networkErrorMessages = [
      'fetch failed',
      'connect timeout',
      'connection timeout',
      'network error',
      'connection refused',
      'host unreachable',
      'network unreachable'
    ];
    
    // 检查错误代码
    if (error.code && networkErrorCodes.includes(error.code)) {
      return true;
    }
    
    // 检查错误消息
    const errorMessage = (error.message || '').toLowerCase();
    if (networkErrorMessages.some(msg => errorMessage.includes(msg))) {
      return true;
    }
    
    // 检查嵌套的错误
    if (error.cause && isNetworkError(error.cause)) {
      return true;
    }
    
    return false;
  }

  // 🔥 System health monitoring
  setInterval(() => {
    if (!shutdownRequested) {
      const memUsage = process.memoryUsage();
      const heapUsedMB = (memUsage.heapUsed / 1024 / 1024).toFixed(2);
      const heapTotalMB = (memUsage.heapTotal / 1024 / 1024).toFixed(2);
      
      // 只在内存使用过高时警告
      if (memUsage.heapUsed > 500 * 1024 * 1024) { // 500MB
        console.log(`⚠️ 高内存使用: ${heapUsedMB}MB / ${heapTotalMB}MB`);
      }
      
      // 强制垃圾回收（如果可用）
      if (global.gc && memUsage.heapUsed > 200 * 1024 * 1024) {
        global.gc();
      }
    }
  }, 60000); // 每分钟检查一次

  console.log('🎯 启动robust Real Trading系统...');
  console.log('📋 系统特性:');
  console.log('   ✅ 自动重连机制');
  console.log('   ✅ 网络错误恢复');
  console.log('   ✅ 异常处理和重启');
  console.log('   ✅ 内存监控');
  console.log('   ✅ 安全关闭机制');
  console.log('');
  
  // 开始运行
  await startWithRetry(realTrading);
}

// 运行系统
main().catch((error) => {
  console.error('❌ 主函数发生致命错误:', error);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}); 